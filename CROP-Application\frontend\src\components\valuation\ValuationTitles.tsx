import { CommentOutlined } from '@ant-design/icons'
import { Empty, Skeleton, Table } from 'antd'
import React from 'react'
import type { ValuationsSavedTitle } from '@store/services/sdk'
import { squareMetresToHectares } from '@util/helpers'
import ValuationTitle from './ValuationTitle'
import styles from './ValuationTitles.module.scss'

interface ValuationPageTitleWidgetProps {
  loading?: boolean
  titles?: ValuationsSavedTitle[]
}

const sumTitlesArea = (sum: number, title: ValuationsSavedTitle) =>
  sum + Number(title.surveyArea ?? 0)

const columns = [
  {
    dataIndex: 'titleNo',
    title: 'Title',
    key: 'title',
  },
  {
    dataIndex: 'estateDescription',
    title: 'Description',
  },
  {
    dataIndex: 'surveyArea',
    title: 'Area (Ha)',
    render: squareMetresToHectares,
  },
  {
    width: 20,
    render: (row: ValuationsSavedTitle) =>
      !!row.review?.comments && <CommentOutlined />,
  },
]

const expandable = {
  expandedRowRender: (title: ValuationsSavedTitle) => (
    <ValuationTitle title={title} />
  ),
}

const summary = (titles: readonly ValuationsSavedTitle[]) => {
  return (
    <Table.Summary.Row>
      <Table.Summary.Cell index={0} colSpan={3} align="right">
        Total
      </Table.Summary.Cell>
      <Table.Summary.Cell index={4} colSpan={2}>
        {squareMetresToHectares(titles.reduce(sumTitlesArea, 0))}
      </Table.Summary.Cell>
    </Table.Summary.Row>
  )
}

const ValuationTitles = ({
  loading,
  titles,
}: ValuationPageTitleWidgetProps) => {
  if (loading) return <Skeleton />
  if (!titles) return <Empty />

  return (
    <Table
      className={styles.container}
      size="small"
      pagination={false}
      dataSource={titles}
      rowKey={(row) => row.id}
      columns={columns}
      expandable={expandable}
      summary={summary}
    />
  )
}

export default ValuationTitles
