import { getLegend } from '@/types/GISLayerDescriptors'
import { View } from '@react-pdf/renderer'
import { PdfLegend, PdfMap } from '@components/pdf'
import type { ValuationsPvsSummary } from '@store/services/sdk'
import { truthy } from '@util/guards'

const legend = getLegend('luc')

type Props = {
  union: ValuationsPvsSummary['anzUnion']
}

const LucMap = ({ union }: Props) => {
  const key = [
    ...new Set(
      union.features
        .map((feature) => feature.properties?.luc?.charAt(0))
        .filter(truthy)
    ),
  ]
    .sort()
    .map((code) => {
      return {
        key: code,
        color: legend?.getColor(code) ?? '#eeeeee',
        label: legend?.getDescriptor(code) ?? '',
      }
    })

  return (
    <View wrap={false}>
      <PdfMap
        geoJson={union}
        featureOptions={{
          style(feature) {
            const luc: string = feature?.properties?.luc ?? ''
            return {
              color: legend?.getColor(luc.charAt(0)),
            }
          },
        }}
      />
      <PdfLegend legend={key} />
    </View>
  )
}

export default LucMap
