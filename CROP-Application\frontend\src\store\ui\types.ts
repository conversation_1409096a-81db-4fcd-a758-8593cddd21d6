import * as moment from 'moment'
import { ReactNode } from 'react-markdown/lib/react-markdown'
import {
  Sale,
  SalesListApiArg,
  User,
  ValocitySalesListApiArg,
} from '@store/services/sdk'
import { StraightLineMeasurement } from '../../components/map/types'
import {
  SerializableComparableSaleAdjustment,
  SerializableComparableSaleAssetMetricAdjustment,
} from '../../models/assets/ComparableSaleAdjustment'
import { SaleEditState } from '../../models/sales/util/SaleEditState'
import { AgriGISMapViewPort } from '../hooks/useViewport'

export type SalesAndListingTabOption = 'SALES' | 'LISTINGS' | 'COMPARISON'

export type UserWithSettings = User & { settings?: SavedSettings }

export interface MapSelectedAddresses {
  addressIds: string[] | undefined
  saleIds: string[] | undefined
  listingIds: string[] | undefined
  valocitySaleIds: string[] | undefined
  valocityListingIds: string[] | undefined
  consentIds: string[] | undefined
  tab: string | undefined
}

export interface WidgetState {
  [widgetKey: string]: boolean
}

export interface LayerState {
  [layerKey: string]: boolean
}

export interface InfoState {
  title?: string
  content?: ReactNode | ReactNode[]
  type?: 'sale' | 'address' | 'valocityListing' | 'valocitySale'
  selectedId?: string
  properties?: any
}

// Setting pretty much anything on this type seems to break the state types?
export type LayerStyleRecord = any

export type MapBaseLayer =
  | 'ESRI Satellite'
  | 'LINZ Satellite'
  | 'OpenStreetMap'
  | 'Sentinel-2 True Colour'
  | 'Sentinel-2 NDVI'
  | 'OpenTopoMap'

export interface ExplorerFilters {
  saleStartDate: moment.Moment
  saleEndDate: moment.Moment | null
  keyword: string
}
export interface MapState {
  bookmarks: SelectionBookmark[]
  selectedBookmarkId: SelectionBookmark['id'] | null
  viewport: AgriGISMapViewPort | undefined
  selected: MapSelectedAddresses
  widgetState: WidgetState
  layerState: LayerState
  baseLayer: MapBaseLayer
  previousBaseLayer: MapBaseLayer
  saleColors: {
    [saleId: string]: string
  }
  addressColors: {
    [addressId: string]: string
  }
  layerStyles?: {
    address?: LayerStyleRecord
    sale?: LayerStyleRecord
  }
  toolState: {
    draft: boolean
    straightLineMeasure: StraightLineMeasurement[] | undefined
  }
  filters: ExplorerFilters
}

export interface MapContainerRect {
  x: number
  y: number
  width: number
  height: number
  top: number
  right: number
  bottom: number
  left: number
}

export interface TemporalMapState {
  infoState: InfoState
  mapContainerRect: MapContainerRect
}

export interface SaleSearchFilters extends SalesListApiArg {
  useDateFilter: boolean
}

export interface ValocitySaleSearchFilters extends ValocitySalesListApiArg {
  useDateFilter: boolean
}

export interface SavedSettings {
  map: MapState
  saleSearchFilters: {
    [pageName: string]: SaleSearchFilters
  }
  valocitySaleSearchFilters: {
    [pageName: string]: ValocitySaleSearchFilters
  }
  filters: {
    [pageName: string]: {
      [filterKey: string]: any
    }
  }
  layouts: {
    [pageName: string]: {
      [layoutKey: string]: any
    }
  }
  recent: {
    [categoryName: string]: Array<any>
  }
}

export type GreenPageTab = 'returns'

export type CustomerPageTab =
  | 'funding'
  | 'financials'
  | 'emissions'
  | 'climate'
  | 'valuations'
  | 'data'

export type TradingGroupPageTab =
  | 'overview'
  | 'funding'
  | 'benchmarking'
  | 'emissions'

export interface TradingGroupPageState {
  drawer: {
    visible: boolean
  }
  tab: TradingGroupPageTab
}

export interface GreenPageState {
  drawer: {
    visible: boolean
    projectId: number | undefined
    annualReportingId: number | undefined
  }
  tab: GreenPageTab
}

export interface TemporalSettings {
  map: TemporalMapState
  green: GreenPageState
  tradingGroup: TradingGroupPageState
}

export interface UIState {
  salesAndListings: ComparableSalesState
  user?: UserWithSettings
  loaded: boolean
  temporal: TemporalSettings
  saved: SavedSettings
}

export interface ComparableSalesState {
  layerVisibility: boolean
  currentTab: SalesAndListingTabOption
  // selected comparable sale when doing adjustments
  selectedComparableSaleId?: number
  comparableSaleAdjustments: SerializableComparableSaleAdjustment[]
  comparableSaleAssetMetricAdjustments: SerializableComparableSaleAssetMetricAdjustment[]
  // selected sale in search
  selectedSale?: Sale
  selectedSaleRowIndex?: number
  sales: {
    saleEdit: SaleEditState
  }
}

export interface BaseValuationUIPayload {
  valuationId: string
}

export interface SelectionBookmark {
  id: number
  name: string
  addressIds: string[]
  saleIds: string[]
  listingIds: string[]
  valocityListingIds: string[]
  valocitySaleIds: string[]
}

export interface SetSalesAndListingsCurrentTabPayload {
  currentTab: SalesAndListingTabOption
}

export interface SetSaleEditStatePayload {
  saleEdit: SaleEditState
}

export interface ToggleSaleInSaleEditState {
  saleId: string
  saleHighestAndBestUseType?: number
  highestAndBestUseType?: number
  lsdbId?: string
}
