import { LatLng, LatLngBounds } from 'leaflet'
import { PaginatedResponse } from '@models/generic/PaginatedResponse'
import { AddressPointFeatureCollection } from '../../../models/address/AddressFeatureCollection'
import { ResourceConsentFeatureCollection } from '../../../models/consent/ResourceConsent'
import { AnzUnionFeatureCollection } from '../../../models/gis/AnzUnionFeatureCollection'
import { TerritorialAuthorityFeatureCollection } from '../../../models/gis/TerritorialAuthority'
import {
  ValocityListingFeatureCollection,
  ValocitySaleFeatureCollection,
} from '../../../models/listings/SaleFeatureCollection'
import { SaleFeatureCollection } from '../../../models/sales/SaleFeatureCollection'
import { AggregateTitleFeatureCollection } from '../../../models/title/TitleFeatureCollection'
import { Address } from '../../../types'
import { baseApi } from '../baseApi'

function getBoundsString(
  bounds: LatLngBounds | undefined,
  zoom: number | undefined
) {
  zoom = zoom ? Math.round(zoom) : 18
  if (bounds) {
    try {
      const southWest = bounds?.getSouthWest()
      const northEast = bounds?.getNorthEast()
      if (!(southWest && northEast)) return ''
      const { lng: minLng, lat: minLat } = southWest
      const { lng: maxLng, lat: maxLat } = northEast
      return `?min_lng=${minLng.toFixed(10)}&min_lat=${minLat.toFixed(
        10
      )}&max_lng=${maxLng.toFixed(10)}&max_lat=${maxLat.toFixed(
        10
      )}&zoom=${zoom}`
    } catch (error) {
      const { NorthEast: northEast, SouthWest: southWest } = bounds as any
      const { lng: minLng, lat: minLat } = southWest
      const { lng: maxLng, lat: maxLat } = northEast
      return `?min_lng=${minLng.toFixed(10)}&min_lat=${minLat.toFixed(
        10
      )}&max_lng=${maxLng.toFixed(10)}&max_lat=${maxLat.toFixed(
        10
      )}&zoom=${zoom}`
    }
  } else {
    return ''
  }
}

export const mapApi = baseApi.injectEndpoints({
  endpoints: (build) => ({
    getViewportAddresses: build.query<
      AddressPointFeatureCollection,
      { bounds: LatLngBounds | undefined; zoom: number | undefined }
    >({
      query: (body) => {
        const { bounds, zoom } = body
        const boundsString = bounds && zoom ? getBoundsString(bounds, zoom) : ''
        return `/api/address_bbox/${boundsString}`
      },
      providesTags: (result, error, args) => [
        {
          type: 'ViewportAddresses',
          id: `${JSON.stringify(args.bounds)}`,
        },
      ],
    }),
    getViewportSales: build.query<
      SaleFeatureCollection,
      {
        bounds: LatLngBounds | undefined
        zoom: number | undefined
      }
    >({
      query: (body) => {
        const { bounds, zoom } = body
        const boundsString = getBoundsString(bounds, zoom)
        return `/api/sales_bbox/${boundsString}`
      },
      providesTags: (result, error, args) => [
        {
          type: 'ViewportSales',
          id: JSON.stringify(args.bounds),
        },
      ],
    }),
    getViewportAnzUnion: build.query<
      AnzUnionFeatureCollection,
      { bounds: LatLngBounds | undefined; zoom: number | undefined }
    >({
      query: (body) => {
        const { bounds, zoom } = body
        const boundsString = getBoundsString(bounds, zoom)
        return `/api/anz_union_bbox/${boundsString}`
      },
      providesTags: (result, error, args) => [
        {
          type: 'ViewportAnzUnion',
          id: `${JSON.stringify(args.bounds)}`,
        },
      ],
    }),
    getViewportTerritorialAuthorities: build.query<
      TerritorialAuthorityFeatureCollection,
      { bounds: LatLngBounds | undefined; zoom: number | undefined }
    >({
      query: (body) => {
        const { bounds, zoom } = body
        const boundsString = getBoundsString(bounds, zoom)
        return `/api/ta_bbox/${boundsString}`
      },
      providesTags: (result, error, args) => [
        {
          type: 'ViewportTerritorialAuthorities',
          id: `${JSON.stringify(args.bounds)}`,
        },
      ],
    }),
    getViewportResourceConsents: build.query<
      ResourceConsentFeatureCollection,
      { bounds: LatLngBounds | undefined; zoom: number | undefined }
    >({
      query: (body) => {
        const { bounds, zoom } = body
        const boundsString = getBoundsString(bounds, zoom)
        return `/api/consents_bbox/${boundsString}`
      },
      providesTags: (result, error, args) => [
        {
          type: 'ViewportResourceConsents',
          id: `${JSON.stringify(args.bounds)}`,
        },
      ],
    }),
    getViewportValocityListings: build.query<
      ValocityListingFeatureCollection,
      { bounds: LatLngBounds | undefined; zoom: number | undefined }
    >({
      query: (body) => {
        const { bounds, zoom } = body
        const boundsString = getBoundsString(bounds, zoom)
        return `/api/valocity_listings_bbox/${boundsString}`
      },
      providesTags: (result, error, args) => [
        {
          type: 'ViewportListings',
          id: `${JSON.stringify(args.bounds)}`,
        },
      ],
    }),
    getViewportValocitySales: build.query<
      ValocitySaleFeatureCollection,
      {
        bounds: LatLngBounds | undefined
        zoom: number | undefined
      }
    >({
      query: (body) => {
        const { bounds, zoom } = body
        const boundsString = getBoundsString(bounds, zoom)
        return `/api/valocity_sales_bbox/${boundsString}`
      },
      providesTags: (result, error, args) => [
        {
          type: 'ViewportValocitySales',
          id: JSON.stringify(args.bounds),
        },
      ],
    }),
    getViewportTitles: build.query<
      AggregateTitleFeatureCollection,
      { bounds: LatLngBounds | undefined; zoom: number | undefined }
    >({
      query: (body) => {
        const { bounds, zoom } = body
        const boundsString = getBoundsString(bounds, zoom)
        return `/api/titles_bbox/${boundsString}`
      },
      providesTags: (result, error, args) => [
        {
          type: 'ViewportTitles',
          id: `${JSON.stringify(args.bounds)}`,
        },
      ],
    }),
    searchForAddresses: build.query<
      PaginatedResponse<Address[]>,
      { match: string; page: number }
    >({
      query: ({ match, page }) => {
        return `/api/map_search/?match=${match}&page=${page}`
      },
    }),
    findAddressFromClick: build.query<
      {
        addressId: string
        fullAddress: string
        source: 'derived' | 'dvr'
      },
      { latLng: LatLng }
    >({
      query: (body) => {
        const { lat, lng } = body.latLng
        return `/api/find_address/?lat=${lat}&lng=${lng}`
      },
    }),
  }),
})

export const {
  useGetViewportAddressesQuery,
  useGetViewportSalesQuery,
  useGetViewportValocityListingsQuery,
  useSearchForAddressesQuery,
  useFindAddressFromClickQuery,
  useGetViewportResourceConsentsQuery,
  useGetViewportAnzUnionQuery,
  useGetViewportTitlesQuery,
  useGetViewportTerritorialAuthoritiesQuery,
  useGetViewportValocitySalesQuery,
} = mapApi
