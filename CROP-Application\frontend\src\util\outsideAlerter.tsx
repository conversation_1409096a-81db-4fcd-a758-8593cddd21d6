import type React from 'react'
import { useCallback, useEffect } from 'react'

export const useOutsideAlerter = <T extends HTMLElement>(
  ref: React.RefObject<T>,
  onClick: () => void
) => {
  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as HTMLElement)) {
        onClick()
      }
    },
    [ref, onClick]
  )

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [handleClickOutside])
}
