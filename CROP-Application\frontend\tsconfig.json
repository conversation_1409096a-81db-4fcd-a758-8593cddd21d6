{"compilerOptions": {"baseUrl": "src", "rootDir": ".", "target": "es2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "noEmit": true, "jsx": "preserve", "downlevelIteration": true, "paths": {"@assets": ["assets"], "@assets/*": ["assets/*"], "@components": ["components"], "@components/*": ["components/*"], "@hooks": ["hooks"], "@hooks/*": ["hooks/*"], "@models": ["models"], "@models/*": ["models/*"], "@pages": ["pages"], "@pages/*": ["pages/*"], "@store": ["store"], "@store/*": ["store/*"], "@sass": ["sass"], "@sass/*": ["sass/*"], "@styles": ["styles"], "@styles/*": ["styles/*"], "@types": ["types"], "@types/*": ["types/*"], "@util": ["util"], "@util/*": ["util/*"], "@/*": ["*"]}, "plugins": [{"name": "typescript-plugin-css-modules"}]}, "include": ["src", "./vite.config.ts", "module.d.ts", "node_modules/jest-expect-message/types/index.d.ts"], "exclude": ["**/node_modules", "**/.*/"]}