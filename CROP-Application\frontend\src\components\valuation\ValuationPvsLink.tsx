import { Button } from 'antd'
import EntitledComponent from '@components/EntitledComponent'
import { LinkButton, type LinkButtonProps } from '@components/generic'
import { useSelector } from '@store'
import { isAdmin, isValuer } from '@store/ui/selectors'
import { toURL } from '@util'

interface Props extends Omit<LinkButtonProps, 'id' | 'href'> {
  id: number | string | undefined
  completed: boolean
}

const ValuationPvsLink = ({
  id = '',
  children,
  completed,
  ...props
}: Props) => {
  isAdmin
  const valuer = useSelector(isValuer)

  const viewable = completed || valuer

  return (
    <>
      <LinkButton
        {...props}
        disabled={!(id && viewable)}
        href={id ? `/valuations/${id}/ria-export` : '#'}
        target="_blank"
        rel="noopener"
      >
        {children || 'Generate Appraisal'}
      </LinkButton>
    </>
  )
}

export default ValuationPvsLink
