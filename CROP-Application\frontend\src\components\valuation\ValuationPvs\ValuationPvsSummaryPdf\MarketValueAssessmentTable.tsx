import { PdfTable, stylesheet } from '@components/pdf'
import type { ValuationsPvsSummary } from '@store/services/sdk'

type Props = {
  summary: ValuationsPvsSummary['summary']
}

const columns = [
  {
    key: 'description',
    isHeader: true,
    title: '',
  },
  {
    key: 'value',
    title: '',
  },
]

const MarketValueAssessmentTable = ({ summary }: Props) => {
  const rows = [
    {
      description: 'Total Area (ha)',
      value: summary.totalHectares,
    },
    {
      description: 'Land without Building Value (LWB)',
      value: summary.totalLwb,
    },
    {
      description: 'Added Value of Buildings',
      value: summary.improvementsMarketValueDollars,
    },
    {
      description: 'Market Value',
      value: summary.marketValueDollars,
    },
  ]

  return (
    <PdfTable
      margins={false}
      striped
      columns={columns}
      rows={rows}
      rowStyle={(row) => ({
        ...(typeof row.description === 'string' &&
          ['Land without Building Value (LWB)'].includes(row.description) && {
            ...stylesheet.cellBorderBottom,
            borderBottomWidth: '2px',
          }),
      })}
    />
  )
}

export default MarketValueAssessmentTable
