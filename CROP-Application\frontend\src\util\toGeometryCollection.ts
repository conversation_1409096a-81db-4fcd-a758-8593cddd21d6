import type { FeatureCollection, GeometryCollection } from 'geojson'

const initialState: GeometryCollection = {
  type: 'GeometryCollection',
  geometries: [],
}

export const toGeometryCollection = (
  featureCollection: FeatureCollection | undefined
): GeometryCollection => {
  if (featureCollection) {
    return {
      ...initialState,
      geometries: featureCollection.features.map((feature) => feature.geometry),
    }
  }
  return initialState
}
