/* 
 * All styles in this file should conform to:
 * https://pages.github.service.anz/horizon/brand-and-design/brand/typography/
 */

.sans {
  font-family: MyriadPro;
  font-weight: 400;
  line-height: 1.5;
  color: var(--color-primary);
}

.sansHeading {
  composes: sans;

  font-weight: 600;
  letter-spacing: -0.1px;
  color: currentColor;
  -webkit-font-smoothing: antialiased;
}

.heading1 {
  composes: sansHeading;
  font-size: 52px;
  letter-spacing: -0.2px;
}

.heading2 {
  composes: sansHeading;
  font-size: 34px;
}

.heading3 {
  composes: sansHeading;
  font-size: 24px;
}

.heading4 {
  composes: sansHeading;
  font-size: 16px;
}

.headingSmall {
  composes: sansHeading;
  font-size: 14px;
}

.body {
  composes: sans;
  font-size: 16px;
}

.bodySmall {
  composes: sans;
  font-size: 14px;
}

.anchor {
  color: var(--color-interactive);

  &:hover {
    color: var(--color-interactive--hover);
  }
}

.anchorUnderlined {
  composes: anchor;
  text-decoration: underline;
}
