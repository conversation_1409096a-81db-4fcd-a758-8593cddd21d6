import { evaluate } from 'mathjs'
import { skipToken } from '@reduxjs/toolkit/dist/query'
import type { Position } from 'geojson'
import type { LatLngLiteral } from 'leaflet'
import { pick } from 'lodash'
import type { NamePath } from 'rc-field-form/es/interface'
import type { ExtendedKpi } from '@store/hooks/useCustomerKpi'
import { hasKey, isNonNullableObject } from './guards'
import type { MaybeNumber } from './types'

export const SQMHA_DIVISOR = 1e4

type NonNullablePropertyObject<T> = { [K in keyof T]: NonNullable<T[K]> }

export const access =
  <T, U extends keyof T>(property: U) =>
  (entity: T): T[U] =>
    entity[property]

export const valueToProperty =
  <K extends string | number | symbol>(key: K) =>
  <T>(value: T) =>
    ({ [key]: value }) as { [Key in K]: T }

export const equals =
  <T>(comparison: T) =>
  (value: T) =>
    comparison === value

export const equalsProperty =
  <T, U extends keyof T>(key: U, comparison?: T[U]) =>
  (value: T): boolean =>
    comparison === value[key]

export function not<T>(func: (value: T) => boolean) {
  return (value: T) => !func(value)
}

// Should combine these two by providing the higher order method
export function all<T>(...funcs: ((value: T) => boolean)[]) {
  return (value: T) => funcs.every((f) => f(value))
}

export function any<T>(...funcs: ((value: T) => boolean)[]) {
  return (value: T) => funcs.some((f) => f(value))
}

export const getPathArray = (path: NamePath) =>
  Array.isArray(path) ? path : [path]

export const concatPaths = (...paths: NamePath[]) => paths.flatMap(getPathArray)

export const createArrayFunctionProxy = <T extends Record<string, unknown>>(
  obj: T,
  func = () => []
) =>
  new Proxy(obj as Required<T> & { [k: string]: () => [] }, {
    get(target, prop) {
      if (hasKey(target, prop)) return target[prop]
      return func
    },
  })

export function insertText(predicate: boolean, text: string): string {
  if (predicate) return text
  return ''
}

export const latLng = ([lng, lat]: Position): LatLngLiteral => ({ lng, lat })

export function insertIf<T>(condition: boolean, ...items: T[]) {
  return condition ? [...items] : []
}

export function noop() {
  // Do nothing
}

export const pickKeys = <T extends Record<number | string, unknown>>(
  obj: T,
  ...keys: Array<keyof T>
) => pick(obj, keys)

export const pickFrom =
  <T>(...keys: Array<keyof T>) =>
  (obj: T) =>
    pick(obj, keys)

export function skipArgObject<T>(
  arg: T
): NonNullablePropertyObject<T> | typeof skipToken {
  const isNonNullableObj = isNonNullableObject(arg)
  if (isNonNullableObj) return arg as NonNullablePropertyObject<T>
  return skipToken
}

export function skipPk(id: number | string | undefined) {
  const pk = Number(id)
  if (Number.isNaN(pk)) return skipToken
  return { pk }
}

export function stdDev(arr: number[]): number {
  const sum = arr.reduce((s, i) => s + i, 0)
  const mean = sum / arr.length

  const sumOfSquares = arr.reduce((s, i) => s + (i - mean) ** 2, 0)
  const variance = sumOfSquares / (arr.length - 1)

  const stdDev = Math.sqrt(variance)
  return stdDev
}

export function mean(arr: number[]): number | null {
  if (arr.length === 0) {
    return null
  }
  const sum = arr.reduce((s, i) => s + i, 0)
  const mean = sum / arr.length
  return mean
}

export const calculateFormula = (
  kpis: ExtendedKpi[],
  measureArr: string[] | undefined,
  periodTo: string,
  entityName: string
) => {
  const operators: string[] = ['*', '/', '+', '-']
  const formulaArr: (string | number)[] = []

  if (!measureArr) {
    return undefined
  }

  for (const x of measureArr) {
    if (operators.includes(x)) {
      formulaArr.push(x)
    } else {
      const value = kpis.find((kpi) => {
        return (
          kpi.periodTo === periodTo &&
          kpi.measure === x &&
          kpi.entityName === entityName
        )
      })
      if (value) formulaArr.push(value.value)
    }
  }

  let value = undefined

  if (
    formulaArr.length > 1 &&
    formulaArr.every((x) => !operators.includes(x as string))
  ) {
    value = formulaArr.reduce((acc, cur) => Number(acc) + Number(cur), 0)
  } else {
    const numeratorFormula = formulaArr.join(' ')
    try {
      value = evaluate(numeratorFormula)
    } catch (error) {
      console.error(error)
    }
  }

  return value
}

export const squareMetresToHectares = (sqM = 0) => sqM / SQMHA_DIVISOR

export const hectaresToSquareMetres = (ha = 0) => ha * SQMHA_DIVISOR

export const valueFrom =
  (key: string) =>
  <T extends Record<string, unknown>>(obj: T) =>
    obj[key]

export const riskRadarScenarioDescription = (
  territorialUnit0: string,
  territorialUnit1: string,
  territorialUnit2: string,
  territorialUnit3: string,
  territorialUnit4: string,
  territorialUnit5: string
) => {
  const labelMap = [
    { label: 'territorialUnit0', value: territorialUnit0.split(',') },
    { label: 'territorialUnit1', value: territorialUnit1.split(',') },
    { label: 'territorialUnit2', value: territorialUnit2.split(',') },
    { label: 'territorialUnit3', value: territorialUnit3.split(',') },
    { label: 'territorialUnit4', value: territorialUnit4.split(',') },
    { label: 'territorialUnit5', value: territorialUnit5.split(',') },
  ]

  const descriptionArr = labelMap
    .map(({ label, value }) => {
      const arr = value.filter((x) => x)
      if (arr.length === 0) return null
      return `These ${label} have been entirely selected: ${arr.join(', ')}`
    })
    .filter((x) => x)

  return descriptionArr
}

export function joinArrayProperties<T extends Record<string, unknown>>(obj: T) {
  for (const key in obj) {
    if (Array.isArray(obj[key])) {
      obj[key] = (obj[key] as unknown[]).join(',') as T[typeof key]
    }
  }
  return obj as {
    [K in keyof T]: string[] extends T[K] ? string : T[K]
  }
}

export const sumPropertyReducer =
  <T extends object>(property: keyof T) =>
  (a: number, b: T) =>
    a + (Number(b[property]) || 0)

export const sumProperty = <T extends object>(
  objArray: T[],
  property: keyof T
) => objArray.reduce(sumPropertyReducer(property), 0)

export const toNumber = (value: MaybeNumber) => Number(value) || 0

export const isNumberRepresentation = (value: MaybeNumber) =>
  value != null && value !== '' && !Number.isNaN(Number(value))
