import type { LatLngBounds } from 'leaflet'
import { useEffect, useMemo, useState } from 'react'
import type { FeatureGroup } from 'react-leaflet'
import type { LayerStyles } from '@store/features/map/types'
import {
  CENTER_MAP_ON_SELECTED,
  type CenterMap,
} from './dispatchCenterMapEvent'
import { DEFAULT_STYLES } from './layers'

type LayerID = number | string | undefined

type CenteredMapEvent = CustomEventInit<CenterMap>

export type RefitBoundsEventDetail = { newBounds: LatLngBounds }
export type RefitBoundsEvent = CustomEventInit<RefitBoundsEventDetail>

function centeredEventHandler(layerId: LayerID, ref: FeatureGroup | null) {
  return ({ detail }: CenteredMapEvent) => {
    if (!(layerId && detail && ref)) return
    if (detail.id.toString() !== layerId.toString()) return

    const newBounds = ref.leafletElement.getBounds()
    dispatchEvent(
      new CustomEvent<RefitBoundsEventDetail>('refitBoundsToSelected', {
        detail: { newBounds },
      })
    )
  }
}

/**
 * @param  {(number|string)} [id] addressId or saleId
 * @param  {LayerStyles} [savedStyles]
 */
function useLayer(id?: number | string, savedStyles?: LayerStyles) {
  const [ref, setRef] = useState<FeatureGroup | null>(null)

  const styles = useMemo(
    () => ({ ...DEFAULT_STYLES, ...savedStyles }),
    [savedStyles]
  )

  const onCentered = useMemo(() => centeredEventHandler(id, ref), [id, ref])

  // This coudl go in its own component for the referenced element to better reflect the mount state and the above ref could then use the useCallback method to self-document its reliance on the Leaflet element loading
  useEffect(() => {
    window.addEventListener(CENTER_MAP_ON_SELECTED, onCentered)
    return () => {
      window.removeEventListener(CENTER_MAP_ON_SELECTED, onCentered)
    }
  }, [onCentered])

  useEffect(() => {
    if (ref?.leafletElement?.setStyle) {
      ref.leafletElement.setStyle(styles)
    }
  }, [ref, styles])

  return { ref, setRef, styles }
}

export default useLayer
