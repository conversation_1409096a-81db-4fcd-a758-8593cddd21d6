const react = require('/app/node_modules/@vitejs/plugin-react/dist/index.js')
const path = require('path')
const { defineConfig, loadEnv } = require('/app/node_modules/vite/dist/node/index.js')
const tsconfigPaths = require('/app/node_modules/vite-tsconfig-paths/dist/index.js')
const biomePlugin = require('/app/node_modules/vite-plugin-biome/dist/index.mjs')

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const enableLinting = !env.VITE_AG_QUIET

  return {
    plugins: [
      react(),
      tsconfigPaths(),
      enableLinting &&
        biomePlugin({
          mode: 'lint',
          files: './src',
          failOnError: true,
        }),
    ],
    build: {
      sourcemap: true,
      outDir: 'build',
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "./src/sass/base.scss";`,
        },
        less: {
          modifyVars: {
            'primary-color': '#007dba;',
            'font-size-base': '12px',
          },
          javascriptEnabled: true,
        },
      },
    },
    resolve: {
      alias: {
        '@sass': path.resolve(__dirname, './src/sass'),
        '@styles': path.resolve(__dirname, './src/styles'),
      },
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/tests/setup.ts',
      deps: {
        fallbackCJS: true,
      },
      test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: './src/tests/setup.ts',
        deps: {
          fallbackCJS: true,
        },
      },
    },
  }
})
