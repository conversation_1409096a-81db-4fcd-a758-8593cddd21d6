import type { MaybeNumber } from './types'

export const toNumber = (value: MaybeNumber) => Number(value) || 0

export function deltaPercent(a: number, b: number): number {
  return ((a && (b - a) / a) ?? 0) * 100
}

export function decimal(num: number, places = 2) {
  return Number(`${Math.round(Number(`${num}e+${places}`))}e-${places}`)
}

export const percentage = (a: MaybeNumber, b: MaybeNumber): number =>
  ((Number(a) || 0) / (Number(b) || 1)) * 100

export const toMillions = (value: MaybeNumber) => toNumber(value) / 1e6
