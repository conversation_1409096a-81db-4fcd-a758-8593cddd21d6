import { shouldSkipValidation } from './customInputValidation'
import type { FormInstance } from 'antd'

describe('shouldSkipValidation', () => {
  const createMockForm = ({
    fieldValues,
  }: { fieldValues: Record<string, number[]> }) =>
    ({
      getFieldValue: (field: string[]) => {
        return fieldValues[field.join('.')]
      },
    }) as FormInstance

  describe('with multiSelect equals condition', () => {
    it('should skip validation when values match exactly', () => {
      const mockForm = createMockForm({
        fieldValues: {
          'greenAsset.properties.categories': [1, 2],
        },
      })

      const conditions = [
        {
          field: ['greenAsset', 'properties', 'categories'],
          inputType: 'multiSelect',
          condition: 'equals',
          values: [1, 2],
        },
      ]

      expect(shouldSkipValidation(conditions, mockForm)).toBe(true)
    })

    it('should not skip validation when values do not match exactly', () => {
      const mockForm = createMockForm({
        fieldValues: {
          'greenAsset.properties.categories': [1, 2, 3],
        },
      })

      const conditions = [
        {
          field: ['greenAsset', 'properties', 'categories'],
          inputType: 'multiSelect',
          condition: 'equals',
          values: [1, 2],
        },
      ]

      expect(shouldSkipValidation(conditions, mockForm)).toBe(false)
    })

    it('should skip validation when values match but in different order', () => {
      const mockForm = createMockForm({
        fieldValues: {
          'greenAsset.properties.categories': [2, 1],
        },
      })

      const conditions = [
        {
          field: ['greenAsset', 'properties', 'categories'],
          inputType: 'multiSelect',
          condition: 'equals',
          values: [1, 2],
        },
      ]

      expect(shouldSkipValidation(conditions, mockForm)).toBe(true)
    })
  })

  describe('with multiSelect contains condition', () => {
    it('should skip validation when all selected values are in the condition values', () => {
      const mockForm = createMockForm({
        fieldValues: {
          'greenAsset.properties.categories': [1, 2],
        },
      })

      const conditions = [
        {
          field: ['greenAsset', 'properties', 'categories'],
          inputType: 'multiSelect',
          condition: 'contains',
          values: [1, 2, 3],
        },
      ]

      expect(shouldSkipValidation(conditions, mockForm)).toBe(true)
    })
    it('should not skip validation when any selected value is not in the allowed values', () => {
      const mockForm = createMockForm({
        fieldValues: {
          'greenAsset.properties.categories': [1, 4],
        },
      })

      const conditions = [
        {
          field: ['greenAsset', 'properties', 'categories'],
          inputType: 'multiSelect',
          condition: 'contains',
          values: [1, 2, 3],
        },
      ]

      expect(shouldSkipValidation(conditions, mockForm)).toBe(false)
    })
  })
})
