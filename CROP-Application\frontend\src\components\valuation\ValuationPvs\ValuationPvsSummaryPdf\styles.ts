import myriadProLight from '@assets/fonts/MyriadPro-Light.otf'
import myriadProRegular from '@assets/fonts/MyriadPro-Regular.otf'
import myriadProSemibold from '@assets/fonts/MyriadPro-Semibold.otf'
import { Font, StyleSheet } from '@react-pdf/renderer'

const BASE_FONT_SIZE = 9

export const rem = (value: number) => BASE_FONT_SIZE * value

export const COLORS = {
  primary: '#494949',
  secondary: 'rgb(0, 65, 101)',
  grey1: 'rgb(248, 248, 248)',
  grey2: 'rgb(242, 242, 242)',
}

export const SPACE = [
  rem(0.25),
  rem(0.5),
  rem(0.75),
  rem(1),
  rem(1.5),
  rem(2),
  rem(3),
  rem(4),
  rem(5),
  rem(6),
  rem(8),
]

Font.register({
  family: 'MyriadPro',
  fonts: [
    {
      src: myriadProRegular,
      fontWeight: 400,
    },
    {
      src: myriadProLight,
      fontWeight: 200,
    },
    {
      src: myriadProSemibold,
      fontWeight: 600,
    },
  ],
})

Font.registerHyphenationCallback((word) => [word])

const styles = StyleSheet.create({
  page: {
    padding: SPACE[9],
    paddingBottom: SPACE[9] + rem(3),
  },
  section: {
    paddingTop: SPACE[0],
    paddingBottom: SPACE[4],
  },
  row: {
    display: 'flex',
  },
  grow: {
    flex: 1,
  },
  heading: {
    fontFamily: 'MyriadPro',
    fontSize: rem(1),
    lineHeight: 1.5,
    fontWeight: 600,
    marginTop: rem(1.5),
    marginBottom: rem(0.5),
    color: COLORS.secondary,
  },
  headingXL: {
    fontFamily: 'MyriadPro',
    fontWeight: 600,
    color: COLORS.secondary,

    fontSize: rem(2.125),
    lineHeight: 2.5,
    marginTop: rem(3.25),
    marginBottom: rem(1),
  },
  headingL: {
    fontFamily: 'MyriadPro',
    fontWeight: 600,
    color: COLORS.secondary,

    fontSize: rem(1.5),
    lineHeight: 2,
    marginTop: rem(1.5),
    marginBottom: rem(1),
  },
  body: {
    maxWidth: 420,
    fontFamily: 'MyriadPro',
    fontWeight: 400,
    fontSize: rem(1),
    lineHeight: 1.5,
    letterSpacing: 0,
    marginBottom: rem(1), // TODO: move this
    color: COLORS.primary,
  },
  bodyS: {
    fontFamily: 'MyriadPro',
    fontWeight: 400,
    letterSpacing: 0,
    color: COLORS.primary,

    maxWidth: 330,
    fontSize: rem(0.875),
    lineHeight: 1.25,
    marginBottom: rem(2),
  },
  list: {
    marginTop: SPACE[3],
    marginBottom: SPACE[3],
    paddingLeft: SPACE[2],
    paddingRight: SPACE[2],
  },
  listItem: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingBottom: SPACE[2],
  },
  listItemS: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingBottom: SPACE[1],
  },
  listNumber: {
    width: rem(2),
    marginRight: SPACE[2],
  },
  bullet: {
    fontSize: rem(1.5),
    lineHeight: 1,
    marginTop: -1 * SPACE[1],
    marginRight: SPACE[2],
    color: COLORS.secondary,
  },
  bulletS: {
    fontSize: rem(1.3125),
    lineHeight: 1,
    marginTop: -1 * SPACE[0],
    marginRight: SPACE[1],
    color: COLORS.secondary,
  },
  borderHairline: {
    borderBottom: '0.25 solid black',
  },
})

export function isStyleKey(key: number | string): key is keyof typeof styles {
  return key in styles
}

export default styles
