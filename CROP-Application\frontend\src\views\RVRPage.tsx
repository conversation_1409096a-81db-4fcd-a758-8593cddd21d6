import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Layout, message } from 'antd'
import { Content } from 'antd/lib/layout/layout'
import React, { useState } from 'react'
import { Helmet } from 'react-helmet'
import { useDispatch } from 'react-redux'
import { Route, Routes, useParams } from 'react-router-dom'
import { FormPage } from '@components/generic/Layout'
import { PromptModal } from '@components/generic/PromptModal'
import { RVRSidebar } from '@components/rvr'
import { RVRAttachmentWidget } from '@components/rvr/RVRAttachmentWidget'
import { RVRFormWidget } from '@components/rvr/RVRFormWidget'
import { ValuationDetailView } from '@components/valuation'
import { baseApi } from '@store/services/baseApi'
import { useGetRVRQuery, useGetRVRValuationsQuery } from '@store/services/rvr'
import { useTradingGroupRetrieveQuery } from '@store/services/sdk'
import { useUpdateValuationMutation } from '@store/services/valuations'
import { skipArgObject } from '@util/helpers'
import CreateRVRValuationView from '../components/rvr/CreateRVRValuationView'
import ProtectedRoute from '@components/ProtectedRoute'

const DELETE_VALUATION_MESSAGE =
  'Deleting this valuation will result in a loss of data associated with it.'

const SummaryFormPane = ({ rvrId }: { rvrId: string }) => {
  return (
    <div style={{ padding: '10px', height: '100%', overflow: 'auto' }}>
      <RVRAttachmentWidget rvrId={rvrId} />
      <RVRFormWidget rvrId={rvrId} />
    </div>
  )
}

const ValuationPane = () => {
  const { valuationId } = useParams()
  return valuationId ? <ValuationDetailView valuationId={valuationId} /> : null
}

const Valuations = ({ rvrId }: { rvrId: string }) => {
  return (
    <>
      <Routes>
        <Route path="valuations">
          <Route path="" element={<div>select an address</div>} />
          <Route path={':valuationId'} element={<ValuationPane />} />
          <Route
            path="new"
            element={<CreateRVRValuationView rvrId={rvrId} />}
          />
        </Route>
        <Route path={'summary'} element={<SummaryFormPane rvrId={rvrId} />} />
      </Routes>
    </>
  )
}

export const RVRPage = () => {
  const { rvrId } = useParams<{ rvrId: string }>()

  const dispatch = useDispatch()

  const { data: RVR } = useGetRVRQuery(rvrId ?? skipToken)
  const { data: tradingGroupInformation } = useTradingGroupRetrieveQuery(
    skipArgObject({ pk: RVR?.tradingGroupId })
  )
  const { data: rvrValuations } = useGetRVRValuationsQuery(RVR?.id ?? skipToken)

  const [updateValuation] = useUpdateValuationMutation()

  const [valuationToDelete, setValuationToDelete] = useState<
    string | undefined
  >()

  const deleteValuation = async () => {
    if (!valuationToDelete) return

    try {
      await updateValuation({
        valuationId: valuationToDelete,
        valuation: {
          deletedDate: new Date().toISOString(),
        },
      }).unwrap()
      dispatch(
        baseApi.util.invalidateTags([{ type: 'RVRValuations', id: rvrId }])
      )
      setValuationToDelete(undefined)
    } catch (err) {
      console.error(err)
      void message.error('Failed to delete valuation, unknown error occurred.')
    }
  }

  if (!rvrId) return null

  return (
    <ProtectedRoute requiredEntitlements={['client:propertyflow:*']}>
      <FormPage data-testid="rvr-page">
        <Helmet>
          <title>RVR</title>
        </Helmet>
        <Layout style={{ flexDirection: 'row' }}>
          <RVRSidebar
            subtitle={
              <>
                <div>{tradingGroupInformation?.tradingGroupName}</div>
                <div>RM #: {RVR?.customerRmNumber}</div>
              </>
            }
            setValuationToDelete={setValuationToDelete}
            rvr={RVR}
            valuations={rvrValuations}
          />
          <Content>
            {rvrId && <Valuations rvrId={rvrId} />}
            <PromptModal
              show={!!valuationToDelete}
              handleClose={() => setValuationToDelete(undefined)}
              header="Confirm Valuation Deletion"
              body={DELETE_VALUATION_MESSAGE}
              handleConfirm={deleteValuation}
            />
          </Content>
        </Layout>
      </FormPage>
    </ProtectedRoute>
  )
}
