import type { HighestAndBestUseType } from '../models/assets/ValuationTypes'
import type { siPortfolioData } from '../models/portfolio/PortfolioData'

export const TITLE_COLOUR = '#398eff'

export const MIN_ELEVATION = -75
export const MAX_ELEVATION = 3800

export const contourKeys = ['0', '1', '2', '3', '-9999']
export const contourDescriptions = [
  'Flat',
  'Rolling',
  'Medium Hill',
  'Steep',
  'Undefined',
]
export const contourColors = [
  '#008000',
  '#ffff00',
  '#ffa500',
  '#ff0000',
  '#eeeeee',
]
export const particleSizeKeys = [
  'C',
  'C/K',
  'C/L',
  'C/P',
  'K',
  'K/L',
  'K/S',
  'K/Z',
  'L',
  'L/C',
  'L/K',
  'L/P',
  'L/S',
  'S',
  'S/K',
  'S/L',
  'S/Z',
  'Tl',
  'Tp',
  'Ts',
  'Z',
  'Z/C',
  'Z/K',
  'Z/L',
  'Z/S',
  'estu',
  'ice',
  'lake',
  'quar',
  'rive',
  'town',
  'undef',
  '-9999',
  'BRock',
  'MSoil',
]
export const particleSizeDescriptions = [
  'Clayey',
  'Clayey over Skeletal',
  'Clayey over Loamy',
  'Clayey over Peat',
  'Skeletal',
  'Skeletal over Loamy',
  'Skeletal over Sandy',
  'Skeletal over Silty',
  'Loamy',
  'Loamy over Clayey',
  'Loamy over Skeletal',
  'Loamy over Peat',
  'Loamy over Sandy',
  'Sandy',
  'Sandy over Skeletal',
  'Sandy over Loamy',
  'Sandy over Silty',
  'Loamy peat',
  'Peat or litter',
  'Sandy peat',
  'Silty',
  'Silty over Clayey',
  'Silty over Skeletal',
  'Silty over Loamy',
  'Silty over Sandy',
  'Undefined',
  'Undefined',
  'Undefined',
  'Undefined',
  'Undefined',
  'Undefined',
  'Undefined',
  'Undefined',
  'Undefined',
  'Undefined',
]
export const particleSizeColors = [
  '#004247',
  '#007d7f',
  '#00bcbd',
  '#00ffff',
  '#004500',
  '#457f38',
  '#82bd72',
  '#c2ffb0',
  '#540064',
  '#863994',
  '#b969c7',
  '#f09bfd',
  '#ffd0ff',
  '#5b1a00',
  '#9c5136',
  '#e08d6e',
  '#ffccab',
  '#503c00',
  '#a28600',
  '#ffd94c',
  '#0000c8',
  '#0000ff',
  '#7444ff',
  '#b778ff',
  '#f5acff',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
]
export const vegetationKeys = [
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  '9',
  '10',
  '11',
  '12',
  '-9999',
]
export const vegetationDescriptions = [
  'Grassland',
  'High Producing Pasture',
  'Low Producing Pasture',
  'Scrubland',
  'Manuka or Kanuka',
  'Cropland',
  'Podocarp-Hardwood Forest',
  'Hardwood Forest',
  'Exotic Forest',
  'Other Forest',
  'Miscellaneous Vegetation',
  'Undefined',
  'Undefined',
]
export const vegetationColors = [
  '#ffff00',
  '#15ffea',
  '#d42bff',
  '#ff0040',
  '#ff6e00',
  '#6a4595',
  '#4877c7',
  '#6acd65',
  '#248b1b',
  '#327100',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
]
export const lucKeys = [
  '1',
  '2',
  '3',
  '4',
  '5',
  '6',
  '7',
  '8',
  'e',
  'r',
  't',
  'q',
  'l',
  '-9999',
]
export const lucDescriptions = [
  '1 - Land with virtually no limitations for arable use and suitable for cultivated crops, pasture or forestry',
  '2 - Land with slight limitations for arable use and suitable for cultivated crops, pasture or forestry',
  '3 - Land with moderate limitations for arable use, but suitable for cultivated crops, pasture or forestry',
  '4 - Land with moderate limitations for arable use, but suitable for occasional cropping, pasture or forestry',
  '5 - High producing land unsuitable for arable use, but only slight limitations for pastoral or forestry use',
  '6 - Non-arable land with moderate limitations for use under perennial vegetation such as pasture or forest',
  '7 - Non-arable land with severe limitations to use under perennial vegetation such as pasture or forest',
  '8 - Land with very severe to extreme limitations or hazards that make it unsuitable for cropping, pasture or forestry',
  'e - Undefined',
  'r - Undefined',
  't - Undefined',
  'q - Undefined',
  'l - Undefined',
  'Undefined',
]
export const lucColors = [
  '#008000',
  '#6db600',
  '#dbed00',
  '#ffe500',
  '#ffbf00',
  '#ff8d00',
  '#ff4700',
  '#ff0000',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '#eeeeee',
  '-9999',
]
export const aspectKeys = ['0', '1', '2', '3', '4', '-9999']
export const aspectDescriptions = [
  'Open',
  'Cold Faces >400m',
  'Cold Faces <400m',
  'Warm Faces >400m',
  'Warm Faces <400m',
  'Undefined',
]
export const aspectColors = [
  '#008000',
  '#ffff00',
  '#ffa500',
  '#ff0000',
  '#c2ffb0',
  '#eeeeee',
]

export const farmTypeOptions = [
  { value: 'Dairy', label: 'Dairy' },
  { value: 'Dairy Support', label: 'Dairy Support' },
  {
    value: 'Sheep & Beef Breeding Store',
    label: 'Sheep & Beef Breeding Store',
  },
  {
    value: 'Drystock / Finishing / Deer',
    label: 'Drystock / Finishing / Deer',
  },
  { value: 'Goats Milking', label: 'Goats Milking' },
  { value: 'Cropping North Island', label: 'Cropping North Island' },
  { value: 'Cropping South Island', label: 'Cropping South Island' },
  { value: 'Kiwifruit Gold', label: 'Kiwifruit Gold' },
  { value: 'Kiwifruit Green', label: 'Kiwifruit Green' },
  { value: 'Viticulture', label: 'Viticulture' },
  { value: 'Pipfruit', label: 'Pipfruit' },
  { value: 'Hops', label: 'Hops' },
  { value: 'Stonefruit', label: 'Stonefruit' },
  { value: 'Broiler', label: 'Broiler' },
  { value: 'Market Garden', label: 'Market Garden' },
  { value: 'Citrus', label: 'Citrus' },
  { value: 'Cherries', label: 'Cherries' },
  { value: 'Avocado', label: 'Avocado' },
]

export const districtOptions = [
  { value: 'Gisborne District', label: 'Gisborne District' },
  { value: 'Nelson City', label: 'Nelson City' },
  { value: 'Selwyn District', label: 'Selwyn District' },
  { value: 'Manawatu District', label: 'Manawatu District' },
  { value: 'Waimakariri District', label: 'Waimakariri District' },
  { value: 'Waikato District', label: 'Waikato District' },
  { value: 'Porirua City', label: 'Porirua City' },
  { value: 'Taupo District', label: 'Taupo District' },
  { value: 'Masterton District', label: 'Masterton District' },
  { value: 'Dunedin City', label: 'Dunedin City' },
  { value: 'Waitaki District', label: 'Waitaki District' },
  { value: 'Carterton District', label: 'Carterton District' },
  { value: 'South Taranaki District', label: 'South Taranaki District' },
  { value: 'Rotorua District', label: 'Rotorua District' },
  { value: 'Rangitikei District', label: 'Rangitikei District' },
  { value: 'Southland District', label: 'Southland District' },
  { value: 'Mackenzie District', label: 'Mackenzie District' },
  { value: 'Waipa District', label: 'Waipa District' },
  { value: 'Ashburton District', label: 'Ashburton District' },
  { value: 'Far North District', label: 'Far North District' },
  { value: 'Christchurch City', label: 'Christchurch City' },
  { value: 'Hastings District', label: 'Hastings District' },
  { value: 'Hurunui District', label: 'Hurunui District' },
  { value: 'Kaipara District', label: 'Kaipara District' },
  { value: 'Whanganui District', label: 'Whanganui District' },
  { value: 'South Waikato District', label: 'South Waikato District' },
  { value: 'Whakatane District', label: 'Whakatane District' },
  { value: 'Waitomo District', label: 'Waitomo District' },
  { value: 'Matamata-Piako District', label: 'Matamata-Piako District' },
  { value: 'Horowhenua District', label: 'Horowhenua District' },
  { value: 'Waimate District', label: 'Waimate District' },
  { value: 'Central Otago District', label: 'Central Otago District' },
  {
    value: "Central Hawke's Bay District",
    label: "Central Hawke's Bay District",
  },
  {
    value: 'Thames-Coromandel District',
    label: 'Thames-Coromandel District',
  },
  { value: 'Tararua District', label: 'Tararua District' },
  { value: 'Stratford District', label: 'Stratford District' },
  { value: 'Westland District', label: 'Westland District' },
  { value: 'Grey District', label: 'Grey District' },
  { value: 'Tauranga City', label: 'Tauranga City' },
  { value: 'Invercargill City', label: 'Invercargill City' },
  { value: 'Otorohanga District', label: 'Otorohanga District' },
  { value: 'Queenstown-Lakes District', label: 'Queenstown-Lakes District' },
  { value: 'Kapiti Coast District', label: 'Kapiti Coast District' },
  { value: 'Buller District', label: 'Buller District' },
  { value: 'Wellington City', label: 'Wellington City' },
  { value: 'Upper Hutt City', label: 'Upper Hutt City' },
  { value: 'Palmerston North City', label: 'Palmerston North City' },
  { value: 'Opotiki District', label: 'Opotiki District' },
  { value: 'Matamata Piako District', label: 'Matamata Piako District' },
  { value: 'Hauraki District', label: 'Hauraki District' },
  { value: 'Kaikoura District', label: 'Kaikoura District' },
  { value: 'Napier City', label: 'Napier City' },
  { value: 'Gore District', label: 'Gore District' },
  { value: 'Manukau City', label: 'Manukau City' },
  { value: 'Lower Hutt City', label: 'Lower Hutt City' },
  { value: 'Timaru District', label: 'Timaru District' },
  {
    value: 'Central Hawkes Bay District',
    label: 'Central Hawkes Bay District',
  },
  { value: 'Auckland City', label: 'Auckland City' },
  { value: 'Rodney District', label: 'Rodney District' },
  { value: 'Wairoa District', label: 'Wairoa District' },
  {
    value: 'Thames Coromandel District',
    label: 'Thames Coromandel District',
  },
  { value: 'Western Bop District', label: 'Western Bop District' },
  { value: 'Hamilton City', label: 'Hamilton City' },
  { value: 'Queenstown Lakes District', label: 'Queenstown Lakes District' },
  { value: 'Franklin District', label: 'Franklin District' },
  { value: 'Wanganui District', label: 'Wanganui District' },
  { value: 'Chatham Islands', label: 'Chatham Islands' },
  { value: 'Tauranga District', label: 'Tauranga District' },
  { value: 'South Waikato', label: 'South Waikato' },
  { value: 'Waitakere City', label: 'Waitakere City' },
  { value: 'Hutt City', label: 'Hutt City' },
  { value: 'North Shore City', label: 'North Shore City' },
]

export const anzDistrictOptions = [
  { value: 'Blenheim - Marlborough', label: 'Blenheim - Marlborough' },
  { value: 'Nelson', label: 'Nelson' },
  { value: 'Pukekohe', label: 'Pukekohe' },
  { value: 'Tauranga', label: 'Tauranga' },
  {
    value: 'King Country - Te Awamutu/Te Kuiti',
    label: 'King Country - Te Awamutu/Te Kuiti',
  },
  { value: 'Northland - Whangarei', label: 'Northland - Whangarei' },
  { value: 'Masterton and Dannevirke', label: 'Masterton and Dannevirke' },
  { value: 'Otago - Dunedin/Alexandra', label: 'Otago - Dunedin/Alexandra' },
  {
    value: 'Taranaki - New Plymouth/Hawera',
    label: 'Taranaki - New Plymouth/Hawera',
  },
  { value: 'Gisborne', label: 'Gisborne' },
  {
    value: 'Christchurch - North/Central Canterbury',
    label: 'Christchurch - North/Central Canterbury',
  },
  { value: 'Palmerston North', label: 'Palmerston North' },
  {
    value: 'Southland - Invercargill/Gore',
    label: 'Southland - Invercargill/Gore',
  },
  { value: 'Hamilton', label: 'Hamilton' },
  { value: 'Rotorua', label: 'Rotorua' },
  { value: 'Timaru - South Canterbury', label: 'Timaru - South Canterbury' },
  { value: 'Hastings - Hawkes Bay', label: 'Hastings - Hawkes Bay' },
  { value: 'Whanganui', label: 'Whanganui' },
  {
    value: 'Ashburton - Mid Canterbury',
    label: 'Ashburton - Mid Canterbury',
  },
  { value: 'Matamata', label: 'Matamata' },
  { value: 'Whakatane', label: 'Whakatane' },
  { value: 'Morrinsville', label: 'Morrinsville' },
  { value: 'Greymouth - West Coast', label: 'Greymouth - West Coast' },
]

export const landUseOptions = [
  { value: 'Single Unit', label: 'Single Unit' },
  {
    value: 'Market Gardens and Orchards',
    label: 'Market Gardens and Orchards',
  },
  { value: 'Vacant Lifestyle', label: 'Vacant Lifestyle' },
  { value: 'Store Livestock', label: 'Store Livestock' },
  { value: 'Stock Finishing', label: 'Stock Finishing' },
  { value: 'Specialist Livestock', label: 'Specialist Livestock' },
  { value: 'Forestry', label: 'Forestry' },
  { value: 'Dairying', label: 'Dairying' },
  {
    value: 'Multi-use within Lifestyle',
    label: 'Multi-use within Lifestyle',
  },
  { value: 'Multi-unit', label: 'Multi-unit' },
  { value: 'Arable Farming', label: 'Arable Farming' },
  {
    value: 'Multi use within Rural Industry',
    label: 'Multi use within Rural Industry',
  },
  { value: 'Vacant', label: 'Vacant' },
  { value: 'Vacant/Indeterminate', label: 'Vacant/Indeterminate' },
  {
    value: 'Single Unit excluding Bach',
    label: 'Single Unit excluding Bach',
  },
  { value: 'Vacant Residential', label: 'Vacant Residential' },
  { value: 'Retail', label: 'Retail' },
  { value: 'Lifestyle', label: 'Lifestyle' },
  { value: 'Rural Industry', label: 'Rural Industry' },
  { value: 'Passive outdoor', label: 'Passive outdoor' },
  { value: 'Residential', label: 'Residential' },
  { value: 'Industrial', label: 'Industrial' },
  {
    value: 'Multi-use within Residential',
    label: 'Multi-use within Residential',
  },
  { value: 'Mineral Extraction', label: 'Mineral Extraction' },
  { value: 'Services', label: 'Services' },
  { value: 'Commercial', label: 'Commercial' },
  { value: 'Water Supply', label: 'Water Supply' },
  { value: 'Bach', label: 'Bach' },
  {
    value: 'Other Industries, including Storage',
    label: 'Other Industries, including Storage',
  },
  { value: 'District office choice', label: 'District office choice' },
  { value: 'Vacant Industrial', label: 'Vacant Industrial' },
  {
    value: 'Multi-use within Commercial',
    label: 'Multi-use within Commercial',
  },
  { value: 'Food, Drink and Tobacco', label: 'Food, Drink and Tobacco' },
  { value: 'Vacant Recreational', label: 'Vacant Recreational' },
  {
    value: 'Public Communal - Licensed',
    label: 'Public Communal - Licensed',
  },
  { value: 'Special Accommodation', label: 'Special Accommodation' },
  { value: 'Religious', label: 'Religious' },
  { value: 'Halls', label: 'Halls' },
  {
    value: 'Public Communal - Unlicensed',
    label: 'Public Communal - Unlicensed',
  },
  {
    value: 'Multi-use within Industrial',
    label: 'Multi-use within Industrial',
  },
  { value: 'Sanitary', label: 'Sanitary' },
  { value: 'Active Outdoor', label: 'Active Outdoor' },
  { value: 'Recreational', label: 'Recreational' },
  { value: 'Communications', label: 'Communications' },
  {
    value: 'Multi-use in Utility Services',
    label: 'Multi-use in Utility Services',
  },
  { value: 'Educational    ', label: 'Educational    ' },
]

export const industryLabel: { [industryId: string]: string } = {
  '-999': 'UNDEFINED',
  '1': 'BERRY FRUIT',
  '10': 'KIWI FRUIT ORCHARD',
  '11': 'CITRUS FRUIT GROWING',
  '12': 'FRUIT GROWING N.E.C',
  '13': 'INTENSIVE CROPPING',
  '14': 'GRAIN GROWING WITH SOME FINISHING STOCK',
  '15': 'SHEEP HIGH COUNTRY SOUTH ISLAND',
  '16': 'SHEEP & BEEF HIGH COUNTRY BREEDING/STORE',
  '17': 'SHEEP & BEEF HIGH COUNTRY BREEDING/FINISHING',
  '18': 'SHEEP INTENSIVE',
  '19': 'BEEF BREEDING/FINISHING/CONTRACT',
  '2': 'CITRUS',
  '20': 'DAIRY FARMING TOWN MILK',
  '21': 'SHARE MILKING OWN HERD',
  '22': 'DAIRY FARMING SEASONAL',
  '23': 'CONTRACT MILKING',
  '24': 'POULTRY MEAT',
  '25': 'POULTRY EGGS',
  '26': 'PIG FARMING',
  '27': 'HORSE FARMING BREEDING/TRAINING',
  '28': 'DEER',
  '29': 'GOAT FARMING',
  '3': 'HORTICULTURE GLASSHOUSES',
  '30': 'LIVESTOCK FARMING N.E.C.',
  '31': 'CROP AND PLANT N.E.C.',
  '32': 'KIWIFRUIT PACKHOUSE/COOLSTORE ONLY',
  '33': 'OTHER HORTICULTURAL COOLSTORE',
  '34': 'GRAIN DRYING/SEED DRESSING',
  '35': 'SERVICES TO AGRICULTURE',
  '37': 'BEE KEEPING',
  '38': 'SHEARING SERVICES',
  '39': 'AERIAL AGRICULTURE SERVICES',
  '4': 'PLANT NURSERIES',
  '40': 'AGRICULTURE CONTRACT SERVICES',
  '41': 'HORTICULTURE CONTRACT SERVICES',
  '5': 'CUT FLOWER & FLOWER SEED GROWING',
  '6': 'VEGETABLE GROWING',
  '7': 'GRAPE GROWING',
  '8': 'APPLE & PEAR GROWING',
  '9': 'STONE FRUIT',
}

export const districtLabel: { [districtId: string]: string } = {
  '-999': 'UNDEFINED',
  '45': 'WAITOMO',
  '39': 'WAIKATO',
  '77': 'ROTORUA',
  '65': 'PALMERSTON NORTH CITY',
  '71': 'KAPITI COAST',
  '69': 'CARTERTON',
  '52': 'OPOTIKI',
  '51': 'WHAKATANE',
  '28': 'KAIPARA',
  '27': 'WHANGAREI',
  '24': 'GORE',
  '5': 'HURUNUI',
  '44': 'SOUTH WAIKATO',
  '36': 'THAMES COROMANDEL',
  '68': 'MASTERTON',
  '47': 'TAURANGA',
  '78': 'TAUPO',
  '34': 'PAPAKURA',
  '80': 'TAUPO',
  '22': 'CLUTHA',
  '21': 'DUNEDIN CITY',
  '76': 'WAITAKI',
  '15': 'WAITAKI',
  '46': 'TAUPO',
  '42': 'WAIPA',
  '40': 'MATAMATA PIAKO',
  '67': 'HOROWHENUA',
  '62': 'WANGANUI',
  '61': 'RUAPEHU',
  '83': 'TAUPO',
  '29': 'RODNEY',
  '59': 'STRATFORD',
  '53': 'GISBORNE',
  '54': 'WAIROA',
  '14': 'CHATHAM ISLANDS TERRITORY',
  '75': 'WELLINGTON CITY',
  '84': 'TARARUA',
  '33': 'MANUKAU',
  '1': 'TASMAN',
  '17': 'GREY',
  '56': 'NAPIER CITY',
  '55': 'HASTINGS',
  '79': 'RANGITIKEI',
  '66': 'TARARUA',
  '72': 'UPPER HUTT CITY',
  '50': 'KAWERAU',
  '31': 'WAITAKERE CITY',
  '30': 'NORTH SHORE CITY',
  '26': 'FAR NORTH',
  '8': 'CHRISTCHURCH CITY',
  '12': 'TIMARU',
  '10': 'ASHBURTON',
  '38': 'HAURAKI',
  '37': 'FRANKLIN',
  '64': 'MANAWATU',
  '81': 'WAITOMO',
  '48': 'WESTERN BAY OF PLENTY',
  '35': 'FRANKLIN',
  '18': 'WESTLAND',
  '23': 'SOUTHLAND',
  '4': 'KAIKOURA',
  '13': 'WAIMATE',
  '63': 'RANGITIKEI',
  '82': 'STRATFORD',
  '73': 'PORIRUA CITY',
  '70': 'SOUTH WAIRARAPA',
  '49': 'ROTORUA',
  '2': 'NELSON CITY',
  '60': 'SOUTH TARANAKI',
  '57': 'CENTRAL HAWKES BAY',
  '20': 'CENTRAL OTAGO',
  '9': 'BANKS PENINSULA',
  '6': 'SELWYN',
  '43': 'OTOROHANGA',
  '41': 'HAMILTON CITY',
  '74': 'HUTT CITY',
  '32': 'AUCKLAND CITY',
  '16': 'BULLER',
  '58': 'NEW PLYMOUTH',
  '3': 'MARLBOROUGH',
  '19': 'QUEENSTOWN-LAKES',
  '25': 'INVERCARGILL CITY',
  '7': 'WAIMAKARIRI',
  '11': 'MACKENZIE',
}

export const regionLabel: { [regionId: string]: string } = {
  '-999': 'UNDEFINED',
  '6': 'OTAGO',
  '13': 'HAWKES BAY',
  '3': 'MARLBOROUGH',
  '10': 'WAIKATO',
  '4': 'CANTERBURY',
  '11': 'BAY OF PLENTY',
  '16': 'WELLINGTON',
  '15': 'WANGANUI - MANAWATU',
  '5': 'WEST COAST',
  '12': 'GISBORNE',
  '7': 'SOUTHLAND',
  '2': 'NELSON',
  '14': 'TARANAKI',
  '9': 'AUCKLAND',
  '8': 'NORTHLAND',
  '1': 'TASMAN',
}

export const categoryOptions = [
  {
    value: "Lifestyle, 1950's, average",
    label: "Lifestyle, 1950's, average",
  },
  { value: "Lifestyle, 1990's, poor", label: "Lifestyle, 1990's, poor" },
  {
    value: 'Lifestyle, 2000/2009, average',
    label: 'Lifestyle, 2000/2009, average',
  },
  {
    value: "Lifestyle, 1990's, average",
    label: "Lifestyle, 1990's, average",
  },
  { value: 'Lifestyle, Vacant', label: 'Lifestyle, Vacant' },
  {
    value: "Lifestyle, 1980's, average",
    label: "Lifestyle, 1980's, average",
  },
  {
    value: 'Pastoral, Grazing, uneconomic, not separate',
    label: 'Pastoral, Grazing, uneconomic, not separate',
  },
  {
    value: 'Lifestyle, 2010/2019, average',
    label: 'Lifestyle, 2010/2019, average',
  },
  {
    value: 'Pastoral, Fattening, uneconomic, not separate',
    label: 'Pastoral, Fattening, uneconomic, not separate',
  },
  {
    value: 'Lifestyle, Mixed Age, average',
    label: 'Lifestyle, Mixed Age, average',
  },
  {
    value: 'Lifestyle, 2010/2019 superior',
    label: 'Lifestyle, 2010/2019 superior',
  },
  {
    value: 'Horticulture, Other/Mixed, average economic',
    label: 'Horticulture, Other/Mixed, average economic',
  },
  { value: "Lifestyle, 1940's, poor", label: "Lifestyle, 1940's, poor" },
  { value: 'Forestry, Exotic', label: 'Forestry, Exotic' },
  {
    value: 'Dairying, Factory, uneconomic, not separate',
    label: 'Dairying, Factory, uneconomic, not separate',
  },
  {
    value: 'Pastoral, Fattening, average economic',
    label: 'Pastoral, Fattening, average economic',
  },
  {
    value: 'Pastoral, Fattening, uneconomic, separate',
    label: 'Pastoral, Fattening, uneconomic, separate',
  },
  {
    value: 'Lifestyle, Bare land with subdivision potential',
    label: 'Lifestyle, Bare land with subdivision potential',
  },
  {
    value: "Lifestyle, 1970's, average",
    label: "Lifestyle, 1970's, average",
  },
  {
    value: 'Horticulture, Other/Mixed, unimproved economic',
    label: 'Horticulture, Other/Mixed, unimproved economic',
  },
  {
    value: 'Lifestyle, Prior 1914, average',
    label: 'Lifestyle, Prior 1914, average',
  },
  {
    value: 'Lifestyle, 2000/2009 superior',
    label: 'Lifestyle, 2000/2009 superior',
  },
  {
    value: "Lifestyle, 1990's, superior",
    label: "Lifestyle, 1990's, superior",
  },
  {
    value: 'Specialist, Deer, unimproved economic',
    label: 'Specialist, Deer, unimproved economic',
  },
  {
    value: "Lifestyle, 1960's, average",
    label: "Lifestyle, 1960's, average",
  },
  {
    value: 'Horticulture, Market Garden, uneconomic, not separate',
    label: 'Horticulture, Market Garden, uneconomic, not separate',
  },
  {
    value: 'Arable, Irrigated, average economic',
    label: 'Arable, Irrigated, average economic',
  },
  {
    value: 'Dairying, Factory, uneconomic, separate',
    label: 'Dairying, Factory, uneconomic, separate',
  },
  {
    value: 'Arable, Not Irrigat, uneconomic, not separate',
    label: 'Arable, Not Irrigat, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Vines, average economic',
    label: 'Horticulture, Vines, average economic',
  },
  {
    value: "Lifestyle, 1940's, average",
    label: "Lifestyle, 1940's, average",
  },
  {
    value: 'Pastoral, Fattening, fair economic',
    label: 'Pastoral, Fattening, fair economic',
  },
  {
    value: 'Pastoral, Grazing, average economic',
    label: 'Pastoral, Grazing, average economic',
  },
  { value: "Lifestyle, 1930's, poor", label: "Lifestyle, 1930's, poor" },
  { value: 'Forestry, Indigenous', label: 'Forestry, Indigenous' },
  {
    value: 'Horticulture, Citrus, uneconomic, not separate',
    label: 'Horticulture, Citrus, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Other/Mixed, uneconomic, not separate',
    label: 'Horticulture, Other/Mixed, uneconomic, not separate',
  },
  {
    value: 'Lifestyle, 1914/1929, average',
    label: 'Lifestyle, 1914/1929, average',
  },
  {
    value: 'Horticulture, Kiwifruit, fair economic',
    label: 'Horticulture, Kiwifruit, fair economic',
  },
  {
    value: 'Pastoral, Grazing, uneconomic, separate',
    label: 'Pastoral, Grazing, uneconomic, separate',
  },
  { value: "Lifestyle, 1960's, poor", label: "Lifestyle, 1960's, poor" },
  {
    value: 'Dairying, Factory, average economic',
    label: 'Dairying, Factory, average economic',
  },
  { value: "Lifestyle, 1950's, poor", label: "Lifestyle, 1950's, poor" },
  {
    value: "Lifestyle, 1930's, average",
    label: "Lifestyle, 1930's, average",
  },
  {
    value: 'Lifestyle, Prior 1914, poor',
    label: 'Lifestyle, Prior 1914, poor',
  },
  {
    value: 'Lifestyle, 1914/1929, poor',
    label: 'Lifestyle, 1914/1929, poor',
  },
  {
    value: 'Specialist, Horses, uneconomic, separate',
    label: 'Specialist, Horses, uneconomic, separate',
  },
  {
    value: 'Horticulture, Stonefruit, average economic',
    label: 'Horticulture, Stonefruit, average economic',
  },
  {
    value: 'Horticulture, Other/Mixed, fair economic',
    label: 'Horticulture, Other/Mixed, fair economic',
  },
  {
    value: "Lifestyle, 1980's, superior",
    label: "Lifestyle, 1980's, superior",
  },
  {
    value: 'Horticulture, Other/Mixed, uneconomic, separate',
    label: 'Horticulture, Other/Mixed, uneconomic, separate',
  },
  {
    value: 'Lifestyle, 2010/2019, poor',
    label: 'Lifestyle, 2010/2019, poor',
  },
  {
    value: "Lifestyle, 1960's, superior",
    label: "Lifestyle, 1960's, superior",
  },
  {
    value: 'Horticulture, Stonefruit, fair economic',
    label: 'Horticulture, Stonefruit, fair economic',
  },
  {
    value: 'Specialist, Deer, average economic',
    label: 'Specialist, Deer, average economic',
  },
  {
    value: 'Horticulture, Kiwifruit, average economic',
    label: 'Horticulture, Kiwifruit, average economic',
  },
  {
    value: 'Dairying, Factory, excellent economic',
    label: 'Dairying, Factory, excellent economic',
  },
  {
    value: 'Specialist, Deer, unecononomic, not separate',
    label: 'Specialist, Deer, unecononomic, not separate',
  },
  {
    value: 'Specialist, Deer, uneconomic, separate',
    label: 'Specialist, Deer, uneconomic, separate',
  },
  { value: "Lifestyle, 1980's, poor", label: "Lifestyle, 1980's, poor" },
  {
    value: 'Lifestyle, 2000/2009, poor',
    label: 'Lifestyle, 2000/2009, poor',
  },
  {
    value: "Lifestyle, 1950's, superior",
    label: "Lifestyle, 1950's, superior",
  },
  {
    value: "Lifestyle, 1970's, superior",
    label: "Lifestyle, 1970's, superior",
  },
  {
    value: 'Dairying, Factory, fair economic',
    label: 'Dairying, Factory, fair economic',
  },
  {
    value: 'Horticulture, Vines, uneconomic, not separate',
    label: 'Horticulture, Vines, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Kiwifruit, uneconomic, not separate',
    label: 'Horticulture, Kiwifruit, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Citrus, uneconomic, separate',
    label: 'Horticulture, Citrus, uneconomic, separate',
  },
  { value: 'Forestry, Protected', label: 'Forestry, Protected' },
  {
    value: 'Horticulture, Kiwifruit, excellent, economic',
    label: 'Horticulture, Kiwifruit, excellent, economic',
  },
  {
    value: 'Lifestyle, Prior 1914, superior',
    label: 'Lifestyle, Prior 1914, superior',
  },
  {
    value: 'Pastoral, Fattening, excellent economic',
    label: 'Pastoral, Fattening, excellent economic',
  },
  {
    value: 'Horticulture, Vines, unimproved economic',
    label: 'Horticulture, Vines, unimproved economic',
  },
  { value: "Lifestyle, 1970's'poor", label: "Lifestyle, 1970's'poor" },
  {
    value: 'Specialist, Horses, uneconomic, not separate',
    label: 'Specialist, Horses, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Kiwifruit, unimproved economic',
    label: 'Horticulture, Kiwifruit, unimproved economic',
  },
  {
    value: 'Dairying, Factory, unimproved, economic',
    label: 'Dairying, Factory, unimproved, economic',
  },
  {
    value: 'Horticulture, Glasshouse, fair, economic',
    label: 'Horticulture, Glasshouse, fair, economic',
  },
  {
    value: 'Horticulture, Vines, excellent economic',
    label: 'Horticulture, Vines, excellent economic',
  },
  {
    value: 'Horticulture, Berry, fair economic',
    label: 'Horticulture, Berry, fair economic',
  },
  {
    value: 'Horticulture, Vines, fair economic',
    label: 'Horticulture, Vines, fair economic',
  },
  {
    value: 'Horticulture, Citrus, fair economic',
    label: 'Horticulture, Citrus, fair economic',
  },
  {
    value: 'Arable, Not Irrigated, uneconomic, separate',
    label: 'Arable, Not Irrigated, uneconomic, separate',
  },
  {
    value: 'Lifestyle, Mixed Age, superior',
    label: 'Lifestyle, Mixed Age, superior',
  },
  {
    value: 'Lifestyle, Mixed Age, poor',
    label: 'Lifestyle, Mixed Age, poor',
  },
  {
    value: 'Horticulture, Market Garden, fair economic',
    label: 'Horticulture, Market Garden, fair economic',
  },
  {
    value: 'Arable, Irrigated, uneconomic, Separate',
    label: 'Arable, Irrigated, uneconomic, Separate',
  },
  {
    value: "Lifestyle, 1930's, superior",
    label: "Lifestyle, 1930's, superior",
  },
  {
    value: 'Horticulture, Kiwifruit, uneconomic, separate',
    label: 'Horticulture, Kiwifruit, uneconomic, separate',
  },
  {
    value: 'Specialist, Poultry, uneconomic, separate',
    label: 'Specialist, Poultry, uneconomic, separate',
  },
  {
    value: 'Horticulture, Citrus, average economic',
    label: 'Horticulture, Citrus, average economic',
  },
  {
    value: 'Horticulture, Berry, uneconomic, separate',
    label: 'Horticulture, Berry, uneconomic, separate',
  },
  {
    value: 'Specialist, Horses, average economic',
    label: 'Specialist, Horses, average economic',
  },
  {
    value: 'Horticulture, Vines, uneconomic, separate',
    label: 'Horticulture, Vines, uneconomic, separate',
  },
  {
    value: 'Horticulture, Glasshouse, average economic',
    label: 'Horticulture, Glasshouse, average economic',
  },
  {
    value: 'Horticulture, Citrus, excellent economic',
    label: 'Horticulture, Citrus, excellent economic',
  },
  {
    value: 'Specialist, Other, average economic',
    label: 'Specialist, Other, average economic',
  },
  {
    value: 'Lifestyle, 1914/1929, superior',
    label: 'Lifestyle, 1914/1929, superior',
  },
  {
    value: 'Horticulture, Pipfruit, average economic',
    label: 'Horticulture, Pipfruit, average economic',
  },
  {
    value: 'Horticulture, Pipfruit, uneconomic, not separate',
    label: 'Horticulture, Pipfruit, uneconomic, not separate',
  },
  {
    value: 'Specialist, Horses, unimproved economic',
    label: 'Specialist, Horses, unimproved economic',
  },
  {
    value: 'Horticulture, Stonefruit, uneconomic separate',
    label: 'Horticulture, Stonefruit, uneconomic separate',
  },
  {
    value: 'Horticulture, Market Garden, average economic',
    label: 'Horticulture, Market Garden, average economic',
  },
  {
    value: 'Pastoral, Grazing, excellent economic',
    label: 'Pastoral, Grazing, excellent economic',
  },
  {
    value: 'Specialist, Deer, fair economic',
    label: 'Specialist, Deer, fair economic',
  },
  {
    value: 'Arable, Irrigated, excellent economic',
    label: 'Arable, Irrigated, excellent economic',
  },
  {
    value: 'Specialist, Other, unimproved economic',
    label: 'Specialist, Other, unimproved economic',
  },
  {
    value: 'Horticulture, Citrus, unimproved economic',
    label: 'Horticulture, Citrus, unimproved economic',
  },
  {
    value: 'Horticulture, Market Garden, uneconomic, separate',
    label: 'Horticulture, Market Garden, uneconomic, separate',
  },
  {
    value: 'Pastoral, Fattening, unimproved economic',
    label: 'Pastoral, Fattening, unimproved economic',
  },
  {
    value: 'Pastoral, Grazing, fair economic',
    label: 'Pastoral, Grazing, fair economic',
  },
  {
    value: 'Horticulture, Glasshouse, uneconomic, separate',
    label: 'Horticulture, Glasshouse, uneconomic, separate',
  },
  {
    value: 'Pastoral run, average economic',
    label: 'Pastoral run, average economic',
  },
  {
    value: 'Horticulture, Pipfruit, uneconomic, separate',
    label: 'Horticulture, Pipfruit, uneconomic, separate',
  },
  {
    value: 'Arable, Not Irrigated, fair economic',
    label: 'Arable, Not Irrigated, fair economic',
  },
  {
    value: 'Horticulture, Pipfruit, unimproved economic',
    label: 'Horticulture, Pipfruit, unimproved economic',
  },
  {
    value: 'Horticulture, Glasshouse, uneconomic, not separate',
    label: 'Horticulture, Glasshouse, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Flower, uneconomic, separate',
    label: 'Horticulture, Flower, uneconomic, separate',
  },
  {
    value: 'Horticulture, Flower, fair economic',
    label: 'Horticulture, Flower, fair economic',
  },
  {
    value: 'Specialist, Poultry, average economic',
    label: 'Specialist, Poultry, average economic',
  },
  {
    value: 'Horticulture, Stonefruit, excellent economic',
    label: 'Horticulture, Stonefruit, excellent economic',
  },
  {
    value: "Lifestyle, 1940's, superior",
    label: "Lifestyle, 1940's, superior",
  },
  {
    value: 'Horticulture, Glasshouse, unimproved economic',
    label: 'Horticulture, Glasshouse, unimproved economic',
  },
  { value: 'Forestry, Vacant', label: 'Forestry, Vacant' },
  {
    value: 'Pastoral run, fair economic',
    label: 'Pastoral run, fair economic',
  },
  {
    value: 'Horticulture, Pipfruit, fair economic',
    label: 'Horticulture, Pipfruit, fair economic',
  },
  {
    value: 'Horticulture, Berry, average economic',
    label: 'Horticulture, Berry, average economic',
  },
  {
    value: 'Horticulture, Flower, uneconomic, not separate',
    label: 'Horticulture, Flower, uneconomic, not separate',
  },
  {
    value: 'Arable, Irrigated, uneconomic, not separate',
    label: 'Arable, Irrigated, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Stonefruit, uneconomic, not separate',
    label: 'Horticulture, Stonefruit, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Flower, excellent economic',
    label: 'Horticulture, Flower, excellent economic',
  },
  {
    value: 'Specialist, Pigs, average economic',
    label: 'Specialist, Pigs, average economic',
  },
  {
    value: 'Arable, Not Irrigated, average economic',
    label: 'Arable, Not Irrigated, average economic',
  },
  {
    value: 'Arable, Irrigated, fair economic',
    label: 'Arable, Irrigated, fair economic',
  },
  {
    value: 'Horticulture, Flower, average economic',
    label: 'Horticulture, Flower, average economic',
  },
  {
    value: 'Horticulture, Market Garden, unimproved economic',
    label: 'Horticulture, Market Garden, unimproved economic',
  },
  {
    value: 'Specialist, Horses, excellent economic',
    label: 'Specialist, Horses, excellent economic',
  },
  {
    value: 'Horticulture, Pipfruit, excellent economic',
    label: 'Horticulture, Pipfruit, excellent economic',
  },
  {
    value: 'Specialist, Horses, fair economic',
    label: 'Specialist, Horses, fair economic',
  },
  {
    value: 'Specialist, Pigs, fair economic',
    label: 'Specialist, Pigs, fair economic',
  },
  {
    value: 'Horticulture, Stonefruit, unimproved economic',
    label: 'Horticulture, Stonefruit, unimproved economic',
  },
  {
    value: 'Specialist, Other, uneconomic, separate',
    label: 'Specialist, Other, uneconomic, separate',
  },
  {
    value: 'Pastoral, Grazing, unimproved economic',
    label: 'Pastoral, Grazing, unimproved economic',
  },
  {
    value: 'Horticulture, Berry, excellent economic',
    label: 'Horticulture, Berry, excellent economic',
  },
  {
    value: 'Specialist, Poultry, fair economic',
    label: 'Specialist, Poultry, fair economic',
  },
  {
    value: 'Pastoral run, uneconomic, separate',
    label: 'Pastoral run, uneconomic, separate',
  },
  {
    value: 'Pastoral run, uneconomic, not separate',
    label: 'Pastoral run, uneconomic, not separate',
  },
  {
    value: 'Specialist, Deer, excellent economic',
    label: 'Specialist, Deer, excellent economic',
  },
  {
    value: 'Arable, Not Irrigated, unimproved economic',
    label: 'Arable, Not Irrigated, unimproved economic',
  },
  {
    value: 'Pastoral run, excellent economic',
    label: 'Pastoral run, excellent economic',
  },
  {
    value: 'Specialist, Poultry, uneconomic, not separate',
    label: 'Specialist, Poultry, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Berry, uneconomic, not separate',
    label: 'Horticulture, Berry, uneconomic, not separate',
  },
  {
    value: 'Specialist, Other, fair economic',
    label: 'Specialist, Other, fair economic',
  },
  {
    value: 'Horticulture, Glasshouse, excellent economic',
    label: 'Horticulture, Glasshouse, excellent economic',
  },
  {
    value: 'Specialist, Aquaculture, average economic',
    label: 'Specialist, Aquaculture, average economic',
  },
  {
    value: 'Horticulture, Other/Mixed, excellent economic',
    label: 'Horticulture, Other/Mixed, excellent economic',
  },
  {
    value: 'Specialist, Other, uneconomic, not separate',
    label: 'Specialist, Other, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Market Garden, excellent economic',
    label: 'Horticulture, Market Garden, excellent economic',
  },
  {
    value: 'Specialist, Poultry, excellent economic',
    label: 'Specialist, Poultry, excellent economic',
  },
  { value: 'Lifestyle, Vacant run', label: 'Lifestyle, Vacant run' },
  { value: 'Lifestyle, mixed ages', label: 'Lifestyle, mixed ages' },
  {
    value: 'Specialist, Aquaculture, unimproved economic',
    label: 'Specialist, Aquaculture, unimproved economic',
  },
  {
    value: 'Specialist, Pigs, uneconomic, separate',
    label: 'Specialist, Pigs, uneconomic, separate',
  },
  {
    value: 'Specialist, Other, excellent economic',
    label: 'Specialist, Other, excellent economic',
  },
  {
    value: 'Arable, Not Irrigated, excellent economic',
    label: 'Arable, Not Irrigated, excellent economic',
  },
  {
    value: 'Specialist, Poultry, unimproved economic',
    label: 'Specialist, Poultry, unimproved economic',
  },
  {
    value: "Lifestyle, 1950's, average",
    label: "Lifestyle, 1950's, average",
  },
  { value: "Lifestyle, 1990's, poor", label: "Lifestyle, 1990's, poor" },
  {
    value: 'Lifestyle, 2000/2009, average',
    label: 'Lifestyle, 2000/2009, average',
  },
  {
    value: "Lifestyle, 1990's, average",
    label: "Lifestyle, 1990's, average",
  },
  { value: 'Lifestyle, Vacant', label: 'Lifestyle, Vacant' },
  {
    value: "Lifestyle, 1980's, average",
    label: "Lifestyle, 1980's, average",
  },
  {
    value: 'Pastoral, Grazing, uneconomic, not separate',
    label: 'Pastoral, Grazing, uneconomic, not separate',
  },
  {
    value: 'Lifestyle, 2010/2019, average',
    label: 'Lifestyle, 2010/2019, average',
  },
  {
    value: 'Pastoral, Fattening, uneconomic, not separate',
    label: 'Pastoral, Fattening, uneconomic, not separate',
  },
  {
    value: 'Lifestyle, Mixed Age, average',
    label: 'Lifestyle, Mixed Age, average',
  },
  {
    value: 'Lifestyle, 2010/2019 superior',
    label: 'Lifestyle, 2010/2019 superior',
  },
  {
    value: 'Horticulture, Other/Mixed, average economic',
    label: 'Horticulture, Other/Mixed, average economic',
  },
  { value: "Lifestyle, 1940's, poor", label: "Lifestyle, 1940's, poor" },
  { value: 'Forestry, Exotic', label: 'Forestry, Exotic' },
  {
    value: 'Dairying, Factory, uneconomic, not separate',
    label: 'Dairying, Factory, uneconomic, not separate',
  },
  {
    value: 'Pastoral, Fattening, average economic',
    label: 'Pastoral, Fattening, average economic',
  },
  {
    value: 'Pastoral, Fattening, uneconomic, separate',
    label: 'Pastoral, Fattening, uneconomic, separate',
  },
  {
    value: 'Lifestyle, Bare land with subdivision potential',
    label: 'Lifestyle, Bare land with subdivision potential',
  },
  {
    value: "Lifestyle, 1970's, average",
    label: "Lifestyle, 1970's, average",
  },
  {
    value: 'Horticulture, Other/Mixed, unimproved economic',
    label: 'Horticulture, Other/Mixed, unimproved economic',
  },
  {
    value: 'Lifestyle, Prior 1914, average',
    label: 'Lifestyle, Prior 1914, average',
  },
  {
    value: 'Lifestyle, 2000/2009 superior',
    label: 'Lifestyle, 2000/2009 superior',
  },
  {
    value: "Lifestyle, 1990's, superior",
    label: "Lifestyle, 1990's, superior",
  },
  {
    value: 'Specialist, Deer, unimproved economic',
    label: 'Specialist, Deer, unimproved economic',
  },
  {
    value: "Lifestyle, 1960's, average",
    label: "Lifestyle, 1960's, average",
  },
  {
    value: 'Horticulture, Market Garden, uneconomic, not separate',
    label: 'Horticulture, Market Garden, uneconomic, not separate',
  },
  {
    value: 'Arable, Irrigated, average economic',
    label: 'Arable, Irrigated, average economic',
  },
  {
    value: 'Dairying, Factory, uneconomic, separate',
    label: 'Dairying, Factory, uneconomic, separate',
  },
  {
    value: 'Arable, Not Irrigat, uneconomic, not separate',
    label: 'Arable, Not Irrigat, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Vines, average economic',
    label: 'Horticulture, Vines, average economic',
  },
  {
    value: "Lifestyle, 1940's, average",
    label: "Lifestyle, 1940's, average",
  },
  {
    value: 'Pastoral, Fattening, fair economic',
    label: 'Pastoral, Fattening, fair economic',
  },
  {
    value: 'Pastoral, Grazing, average economic',
    label: 'Pastoral, Grazing, average economic',
  },
  { value: "Lifestyle, 1930's, poor", label: "Lifestyle, 1930's, poor" },
  { value: 'Forestry, Indigenous', label: 'Forestry, Indigenous' },
  {
    value: 'Horticulture, Citrus, uneconomic, not separate',
    label: 'Horticulture, Citrus, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Other/Mixed, uneconomic, not separate',
    label: 'Horticulture, Other/Mixed, uneconomic, not separate',
  },
  {
    value: 'Lifestyle, 1914/1929, average',
    label: 'Lifestyle, 1914/1929, average',
  },
  {
    value: 'Horticulture, Kiwifruit, fair economic',
    label: 'Horticulture, Kiwifruit, fair economic',
  },
  {
    value: 'Pastoral, Grazing, uneconomic, separate',
    label: 'Pastoral, Grazing, uneconomic, separate',
  },
  { value: "Lifestyle, 1960's, poor", label: "Lifestyle, 1960's, poor" },
  {
    value: 'Dairying, Factory, average economic',
    label: 'Dairying, Factory, average economic',
  },
  { value: "Lifestyle, 1950's, poor", label: "Lifestyle, 1950's, poor" },
  {
    value: "Lifestyle, 1930's, average",
    label: "Lifestyle, 1930's, average",
  },
  {
    value: 'Lifestyle, Prior 1914, poor',
    label: 'Lifestyle, Prior 1914, poor',
  },
  {
    value: 'Lifestyle, 1914/1929, poor',
    label: 'Lifestyle, 1914/1929, poor',
  },
  {
    value: 'Specialist, Horses, uneconomic, separate',
    label: 'Specialist, Horses, uneconomic, separate',
  },
  {
    value: 'Horticulture, Stonefruit, average economic',
    label: 'Horticulture, Stonefruit, average economic',
  },
  {
    value: 'Horticulture, Other/Mixed, fair economic',
    label: 'Horticulture, Other/Mixed, fair economic',
  },
  {
    value: "Lifestyle, 1980's, superior",
    label: "Lifestyle, 1980's, superior",
  },
  {
    value: 'Horticulture, Other/Mixed, uneconomic, separate',
    label: 'Horticulture, Other/Mixed, uneconomic, separate',
  },
  {
    value: 'Lifestyle, 2010/2019, poor',
    label: 'Lifestyle, 2010/2019, poor',
  },
  {
    value: "Lifestyle, 1960's, superior",
    label: "Lifestyle, 1960's, superior",
  },
  {
    value: 'Horticulture, Stonefruit, fair economic',
    label: 'Horticulture, Stonefruit, fair economic',
  },
  {
    value: 'Specialist, Deer, average economic',
    label: 'Specialist, Deer, average economic',
  },
  {
    value: 'Horticulture, Kiwifruit, average economic',
    label: 'Horticulture, Kiwifruit, average economic',
  },
  {
    value: 'Dairying, Factory, excellent economic',
    label: 'Dairying, Factory, excellent economic',
  },
  {
    value: 'Specialist, Deer, unecononomic, not separate',
    label: 'Specialist, Deer, unecononomic, not separate',
  },
  {
    value: 'Specialist, Deer, uneconomic, separate',
    label: 'Specialist, Deer, uneconomic, separate',
  },
  { value: "Lifestyle, 1980's, poor", label: "Lifestyle, 1980's, poor" },
  {
    value: 'Lifestyle, 2000/2009, poor',
    label: 'Lifestyle, 2000/2009, poor',
  },
  {
    value: "Lifestyle, 1950's, superior",
    label: "Lifestyle, 1950's, superior",
  },
  {
    value: "Lifestyle, 1970's, superior",
    label: "Lifestyle, 1970's, superior",
  },
  {
    value: 'Dairying, Factory, fair economic',
    label: 'Dairying, Factory, fair economic',
  },
  {
    value: 'Horticulture, Vines, uneconomic, not separate',
    label: 'Horticulture, Vines, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Kiwifruit, uneconomic, not separate',
    label: 'Horticulture, Kiwifruit, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Citrus, uneconomic, separate',
    label: 'Horticulture, Citrus, uneconomic, separate',
  },
  { value: 'Forestry, Protected', label: 'Forestry, Protected' },
  {
    value: 'Horticulture, Kiwifruit, excellent, economic',
    label: 'Horticulture, Kiwifruit, excellent, economic',
  },
  {
    value: 'Lifestyle, Prior 1914, superior',
    label: 'Lifestyle, Prior 1914, superior',
  },
  {
    value: 'Pastoral, Fattening, excellent economic',
    label: 'Pastoral, Fattening, excellent economic',
  },
  {
    value: 'Horticulture, Vines, unimproved economic',
    label: 'Horticulture, Vines, unimproved economic',
  },
  { value: "Lifestyle, 1970's'poor", label: "Lifestyle, 1970's'poor" },
  {
    value: 'Specialist, Horses, uneconomic, not separate',
    label: 'Specialist, Horses, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Kiwifruit, unimproved economic',
    label: 'Horticulture, Kiwifruit, unimproved economic',
  },
  {
    value: 'Dairying, Factory, unimproved, economic',
    label: 'Dairying, Factory, unimproved, economic',
  },
  {
    value: 'Horticulture, Glasshouse, fair, economic',
    label: 'Horticulture, Glasshouse, fair, economic',
  },
  {
    value: 'Horticulture, Vines, excellent economic',
    label: 'Horticulture, Vines, excellent economic',
  },
  {
    value: 'Horticulture, Berry, fair economic',
    label: 'Horticulture, Berry, fair economic',
  },
  {
    value: 'Horticulture, Vines, fair economic',
    label: 'Horticulture, Vines, fair economic',
  },
  {
    value: 'Horticulture, Citrus, fair economic',
    label: 'Horticulture, Citrus, fair economic',
  },
  {
    value: 'Arable, Not Irrigated, uneconomic, separate',
    label: 'Arable, Not Irrigated, uneconomic, separate',
  },
  {
    value: 'Lifestyle, Mixed Age, superior',
    label: 'Lifestyle, Mixed Age, superior',
  },
  {
    value: 'Lifestyle, Mixed Age, poor',
    label: 'Lifestyle, Mixed Age, poor',
  },
  {
    value: 'Horticulture, Market Garden, fair economic',
    label: 'Horticulture, Market Garden, fair economic',
  },
  {
    value: 'Arable, Irrigated, uneconomic, Separate',
    label: 'Arable, Irrigated, uneconomic, Separate',
  },
  {
    value: "Lifestyle, 1930's, superior",
    label: "Lifestyle, 1930's, superior",
  },
  {
    value: 'Horticulture, Kiwifruit, uneconomic, separate',
    label: 'Horticulture, Kiwifruit, uneconomic, separate',
  },
  {
    value: 'Specialist, Poultry, uneconomic, separate',
    label: 'Specialist, Poultry, uneconomic, separate',
  },
  {
    value: 'Horticulture, Citrus, average economic',
    label: 'Horticulture, Citrus, average economic',
  },
  {
    value: 'Horticulture, Berry, uneconomic, separate',
    label: 'Horticulture, Berry, uneconomic, separate',
  },
  {
    value: 'Specialist, Horses, average economic',
    label: 'Specialist, Horses, average economic',
  },
  {
    value: 'Horticulture, Vines, uneconomic, separate',
    label: 'Horticulture, Vines, uneconomic, separate',
  },
  {
    value: 'Horticulture, Glasshouse, average economic',
    label: 'Horticulture, Glasshouse, average economic',
  },
  {
    value: 'Horticulture, Citrus, excellent economic',
    label: 'Horticulture, Citrus, excellent economic',
  },
  {
    value: 'Specialist, Other, average economic',
    label: 'Specialist, Other, average economic',
  },
  {
    value: 'Lifestyle, 1914/1929, superior',
    label: 'Lifestyle, 1914/1929, superior',
  },
  {
    value: 'Horticulture, Pipfruit, average economic',
    label: 'Horticulture, Pipfruit, average economic',
  },
  {
    value: 'Horticulture, Pipfruit, uneconomic, not separate',
    label: 'Horticulture, Pipfruit, uneconomic, not separate',
  },
  {
    value: 'Specialist, Horses, unimproved economic',
    label: 'Specialist, Horses, unimproved economic',
  },
  {
    value: 'Horticulture, Stonefruit, uneconomic separate',
    label: 'Horticulture, Stonefruit, uneconomic separate',
  },
  {
    value: 'Horticulture, Market Garden, average economic',
    label: 'Horticulture, Market Garden, average economic',
  },
  {
    value: 'Pastoral, Grazing, excellent economic',
    label: 'Pastoral, Grazing, excellent economic',
  },
  {
    value: 'Specialist, Deer, fair economic',
    label: 'Specialist, Deer, fair economic',
  },
  {
    value: 'Arable, Irrigated, excellent economic',
    label: 'Arable, Irrigated, excellent economic',
  },
  {
    value: 'Specialist, Other, unimproved economic',
    label: 'Specialist, Other, unimproved economic',
  },
  {
    value: 'Horticulture, Citrus, unimproved economic',
    label: 'Horticulture, Citrus, unimproved economic',
  },
  {
    value: 'Horticulture, Market Garden, uneconomic, separate',
    label: 'Horticulture, Market Garden, uneconomic, separate',
  },
  {
    value: 'Pastoral, Fattening, unimproved economic',
    label: 'Pastoral, Fattening, unimproved economic',
  },
  {
    value: 'Pastoral, Grazing, fair economic',
    label: 'Pastoral, Grazing, fair economic',
  },
  {
    value: 'Horticulture, Glasshouse, uneconomic, separate',
    label: 'Horticulture, Glasshouse, uneconomic, separate',
  },
  {
    value: 'Pastoral run, average economic',
    label: 'Pastoral run, average economic',
  },
  {
    value: 'Horticulture, Pipfruit, uneconomic, separate',
    label: 'Horticulture, Pipfruit, uneconomic, separate',
  },
  {
    value: 'Arable, Not Irrigated, fair economic',
    label: 'Arable, Not Irrigated, fair economic',
  },
  {
    value: 'Horticulture, Pipfruit, unimproved economic',
    label: 'Horticulture, Pipfruit, unimproved economic',
  },
  {
    value: 'Horticulture, Glasshouse, uneconomic, not separate',
    label: 'Horticulture, Glasshouse, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Flower, uneconomic, separate',
    label: 'Horticulture, Flower, uneconomic, separate',
  },
  {
    value: 'Horticulture, Flower, fair economic',
    label: 'Horticulture, Flower, fair economic',
  },
  {
    value: 'Specialist, Poultry, average economic',
    label: 'Specialist, Poultry, average economic',
  },
  {
    value: 'Horticulture, Stonefruit, excellent economic',
    label: 'Horticulture, Stonefruit, excellent economic',
  },
  {
    value: "Lifestyle, 1940's, superior",
    label: "Lifestyle, 1940's, superior",
  },
  {
    value: 'Horticulture, Glasshouse, unimproved economic',
    label: 'Horticulture, Glasshouse, unimproved economic',
  },
  { value: 'Forestry, Vacant', label: 'Forestry, Vacant' },
  {
    value: 'Pastoral run, fair economic',
    label: 'Pastoral run, fair economic',
  },
  {
    value: 'Horticulture, Pipfruit, fair economic',
    label: 'Horticulture, Pipfruit, fair economic',
  },
  {
    value: 'Horticulture, Berry, average economic',
    label: 'Horticulture, Berry, average economic',
  },
  {
    value: 'Horticulture, Flower, uneconomic, not separate',
    label: 'Horticulture, Flower, uneconomic, not separate',
  },
  {
    value: 'Arable, Irrigated, uneconomic, not separate',
    label: 'Arable, Irrigated, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Stonefruit, uneconomic, not separate',
    label: 'Horticulture, Stonefruit, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Flower, excellent economic',
    label: 'Horticulture, Flower, excellent economic',
  },
  {
    value: 'Specialist, Pigs, average economic',
    label: 'Specialist, Pigs, average economic',
  },
  {
    value: 'Arable, Not Irrigated, average economic',
    label: 'Arable, Not Irrigated, average economic',
  },
  {
    value: 'Arable, Irrigated, fair economic',
    label: 'Arable, Irrigated, fair economic',
  },
  {
    value: 'Horticulture, Flower, average economic',
    label: 'Horticulture, Flower, average economic',
  },
  {
    value: 'Horticulture, Market Garden, unimproved economic',
    label: 'Horticulture, Market Garden, unimproved economic',
  },
  {
    value: 'Specialist, Horses, excellent economic',
    label: 'Specialist, Horses, excellent economic',
  },
  {
    value: 'Horticulture, Pipfruit, excellent economic',
    label: 'Horticulture, Pipfruit, excellent economic',
  },
  {
    value: 'Specialist, Horses, fair economic',
    label: 'Specialist, Horses, fair economic',
  },
  {
    value: 'Specialist, Pigs, fair economic',
    label: 'Specialist, Pigs, fair economic',
  },
  {
    value: 'Horticulture, Stonefruit, unimproved economic',
    label: 'Horticulture, Stonefruit, unimproved economic',
  },
  {
    value: 'Specialist, Other, uneconomic, separate',
    label: 'Specialist, Other, uneconomic, separate',
  },
  {
    value: 'Pastoral, Grazing, unimproved economic',
    label: 'Pastoral, Grazing, unimproved economic',
  },
  {
    value: 'Horticulture, Berry, excellent economic',
    label: 'Horticulture, Berry, excellent economic',
  },
  {
    value: 'Specialist, Poultry, fair economic',
    label: 'Specialist, Poultry, fair economic',
  },
  {
    value: 'Pastoral run, uneconomic, separate',
    label: 'Pastoral run, uneconomic, separate',
  },
  {
    value: 'Pastoral run, uneconomic, not separate',
    label: 'Pastoral run, uneconomic, not separate',
  },
  {
    value: 'Specialist, Deer, excellent economic',
    label: 'Specialist, Deer, excellent economic',
  },
  {
    value: 'Arable, Not Irrigated, unimproved economic',
    label: 'Arable, Not Irrigated, unimproved economic',
  },
  {
    value: 'Pastoral run, excellent economic',
    label: 'Pastoral run, excellent economic',
  },
  {
    value: 'Specialist, Poultry, uneconomic, not separate',
    label: 'Specialist, Poultry, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Berry, uneconomic, not separate',
    label: 'Horticulture, Berry, uneconomic, not separate',
  },
  {
    value: 'Specialist, Other, fair economic',
    label: 'Specialist, Other, fair economic',
  },
  {
    value: 'Horticulture, Glasshouse, excellent economic',
    label: 'Horticulture, Glasshouse, excellent economic',
  },
  {
    value: 'Specialist, Aquaculture, average economic',
    label: 'Specialist, Aquaculture, average economic',
  },
  {
    value: 'Horticulture, Other/Mixed, excellent economic',
    label: 'Horticulture, Other/Mixed, excellent economic',
  },
  {
    value: 'Specialist, Other, uneconomic, not separate',
    label: 'Specialist, Other, uneconomic, not separate',
  },
  {
    value: 'Horticulture, Market Garden, excellent economic',
    label: 'Horticulture, Market Garden, excellent economic',
  },
  {
    value: 'Specialist, Poultry, excellent economic',
    label: 'Specialist, Poultry, excellent economic',
  },
  { value: 'Lifestyle, Vacant run', label: 'Lifestyle, Vacant run' },
  { value: 'Lifestyle, mixed ages', label: 'Lifestyle, mixed ages' },
  {
    value: 'Specialist, Aquaculture, unimproved economic',
    label: 'Specialist, Aquaculture, unimproved economic',
  },
  {
    value: 'Specialist, Pigs, uneconomic, separate',
    label: 'Specialist, Pigs, uneconomic, separate',
  },
  {
    value: 'Specialist, Other, excellent economic',
    label: 'Specialist, Other, excellent economic',
  },
  {
    value: 'Arable, Not Irrigated, excellent economic',
    label: 'Arable, Not Irrigated, excellent economic',
  },
  {
    value: 'Specialist, Poultry, unimproved economic',
    label: 'Specialist, Poultry, unimproved economic',
  },
]

export const anzColors = ['#004165', '#006699']

export const siKeys: { [key: string]: keyof siPortfolioData } = {
  A: 'A',
  B: 'B',
  C: 'C',
  D: 'D',
  E: 'E',
  F: 'F',
  G: 'G',
  'N/A': 'N/A',
}

export const ccrKeys: string[] = [
  '0+',
  '0',
  '0-',
  '1+',
  '1',
  '1-',
  '2+',
  '2',
  '2-',
  '3+',
  '3',
  '3-',
  '4+',
  '4',
  '4-',
  '5+',
  '5',
  '5-',
  '6+',
  '6',
  '6-',
  '7+',
  '7',
  '7-',
  '8+',
  '8',
  '8-',
  '9+',
  '9',
  '9-',
  '10+',
  '10',
  '10-',
]

export const IRRIGATION_OPTIONS = [
  { label: 'IRRIGATED', value: 'IRRIGATED' },
  { label: 'PARTLY IRRIGATED', value: 'PARTLY IRRIGATED' },
  { label: 'NOT IRRIGATED', value: 'NOT IRRIGATED' },
]

export const IRRIGATION_WATER_COST_OPTIONS = [
  { label: 'LOW ($0-$350/ha)', value: 'LOW' },
  { label: 'MEDIUM ($350-$650/ha)', value: 'MEDIUM' },
  { label: 'HIGH ($650+/ha)', value: 'HIGH' },
]

export const IRRIGATION_SOURCE_OPTIONS = [
  { label: 'GROUNDWATER', value: 'GROUNDWATER' },
  { label: 'SURFACE WATER', value: 'SURFACE WATER' },
  { label: 'IRRIGATION SCHEME', value: 'IRRIGATION SCHEME' },
  { label: 'MIXED', value: 'MIXED' },
]

export const IRRIGATION_PRIMARY_APPLICATION = [
  { label: 'CENTRE PIVOT', value: 'CENTRE PIVOT' },
  { label: 'LATERAL', value: 'LATERAL' },
  { label: 'SPRAY', value: 'SPRAY' },
  { label: 'SPRINKLER', value: 'SPRINKLER' },
  { label: 'FLOOD', value: 'FLOOD' },
  { label: 'DRIPLINE', value: 'DRIPLINE' },
  { label: 'FERTIGATION', value: 'FERTIGATION' },
]

export const bestUseToColorMap: { [key: string]: string } = {
  Dairy: '#315dc1', // blue
  'Dairy Support': '#4483f2', // another blue
  'Dairy & Dairy Support': '#1f4196', // yet another blue

  'Breeding/Store': '#afb276', //
  'Drystock/Finishing/Deer': '#8a8362	', // browny
  Arable: '#5ec04f', // greenish
  'Goat Milking': '#3b3527', // greyey
  'Sheep Milking': '#96b09f', // greyey

  Viticulture: '#2d206f', // purpley

  Hops: '#eb9a8f', // grey-yellow

  Citrus: '#e3e23c', // yellowy

  Cherries: '#af3b2c', // dark red

  'Stone Fruit': '#e37963',

  'Market Gardening': '#7fe942', // light green

  'Chicken Broiler': '#e1a16c', // dark orange
  'Chicken Laying': '#b4782d', // other orange
  'Chicken Dual-Purpose': '#cb6221', // another orange

  Glasshouses: '#6660bd',
  'Other Enterprise': '#453e7d',

  Pipfruit: '#de732b', //

  Kiwifruit: '#9bb837', //

  Avocado: '#28412c', // dark green
}

export const bestUseToColor = (
  highestAndBestUseType: HighestAndBestUseType,
  transparency = 0.08
) => {
  const color = bestUseToColorMap[highestAndBestUseType.highestAndBestUse]
  if (color === undefined) return undefined

  let transparencyHex = Math.round(transparency * 255).toString(16)
  if (transparencyHex.length === 1) {
    transparencyHex = `0${transparencyHex}`
  }
  return `${color}${transparencyHex}`
}
