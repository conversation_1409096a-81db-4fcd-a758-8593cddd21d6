import { View } from '@react-pdf/renderer'
import { PdfHeading, PdfParagraph, PdfText } from '@components/pdf'
import type { ValuationsPvsSummary } from '@store/services/sdk'
import TitleReviews from './TitleReviews'

type Props = {
  valuation: ValuationsPvsSummary
}

const RemarksAndActions = ({ valuation }: Props) => {
  const assumptions = valuation.remarksAndActions

  return (
    <>
      {assumptions && valuation.remarksAndActionsReportInclude && (
        <PdfParagraph>{assumptions}</PdfParagraph>
      )}
      {(valuation.titleReviewComments || valuation.titleReviews.length) && (
        <>
          <PdfHeading size="l">Title Review</PdfHeading>
          <PdfParagraph>{valuation.titleReviewComments}</PdfParagraph>
          <TitleReviews reviews={valuation.titleReviews} />
        </>
      )}
      {valuation.waterSecurityReview && (
        <>
          <PdfHeading size="l">Water Security Review</PdfHeading>
          <PdfParagraph>
            The required Consents and/or Permit(s) in relation to irrigation
            water entitlements are NOT considered secured by the bank:
          </PdfParagraph>
          <PdfText>{valuation.waterSecurityReview}</PdfText>
        </>
      )}
    </>
  )
}

export default RemarksAndActions
