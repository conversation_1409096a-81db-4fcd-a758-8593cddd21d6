import { startCase } from 'lodash'
import { PdfHeading, PdfParagraph, PdfTable, SPACE } from '@components/pdf'
import type { ValuationsSavedTitleFeature } from '@store/services/sdk'
import { PLACEHOLDER_CHAR } from '@util/const'
import { pickFrom } from '@util/helpers'

interface Props {
  feature: ValuationsSavedTitleFeature
}

type Properties = NonNullable<ValuationsSavedTitleFeature['properties']>

const getFields = pickFrom<Omit<Properties, 'memorials'>>(
  'type',
  'landDistrict',
  'issueDate',
  'surveyArea'
  // 'mortgagee'
)

const weighting = 3

const titleColumns = [
  { key: 'key', title: '', isHeader: true },
  { key: 'value', title: '', weighting },
]

const memorialColumns = [
  { key: 'instrumentNumber', title: '', isHeader: true },
  { key: 'memorialText', title: '', weighting },
]

const TitleReport = ({ feature }: Props) => {
  const { memorials = [], ...properties } = feature.properties

  const titleRows = Object.entries(getFields(properties)).map(
    ([key, value]) => ({
      key: startCase(key),
      value,
    })
  )

  return (
    <>
      <PdfHeading style={{ marginTop: SPACE[4] }}>
        {properties.titleNo}
      </PdfHeading>
      <PdfParagraph>{properties.estateDescription}</PdfParagraph>
      <PdfTable
        striped
        placeholder={PLACEHOLDER_CHAR}
        columns={titleColumns}
        rows={titleRows}
      />
      {!!memorials.length && (
        <>
          <PdfHeading style={{ marginTop: SPACE[2] }}>
            Memorials and Encumbrances
          </PdfHeading>
          <PdfTable
            striped
            placeholder={PLACEHOLDER_CHAR}
            columns={memorialColumns}
            rows={memorials}
          />
        </>
      )}
    </>
  )
}

export default TitleReport
