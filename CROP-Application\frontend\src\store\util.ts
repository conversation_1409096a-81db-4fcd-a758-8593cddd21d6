import { type AnyAction, type Slice, isAnyOf } from '@reduxjs/toolkit'
import type { AppMiddleware } from './types'

const isActionOf = <T extends Slice>(slice: T) => {
  const [matcher, ...matchers] = Object.values(slice.actions).map(
    (action) => action.match
  )
  return isAnyOf(matcher, ...matchers)
}

/**
 * @deprecated Use redux-persist
 */
export function storeSliceState<T extends Slice>(slice: T) {
  const isSliceAction = isActionOf(slice)

  return function storeState(
    api: Parameters<AppMiddleware>[0],
    action: AnyAction,
    additionalState?: Partial<ReturnType<T['reducer']>>
  ) {
    if (isSliceAction(action)) {
      setTimeout(() => {
        localStorage.setItem(
          slice.name,
          JSON.stringify({
            ...api.getState()[
              slice.name as keyof ReturnType<typeof api.getState>
            ],
            ...additionalState,
          })
        )
      }, 0)
    }
  }
}

export const getStoredState = <T extends object>(name: string): Partial<T> => {
  const storedState = localStorage.getItem(name)
  if (!storedState) return {}
  return JSON.parse(storedState) as T
}
