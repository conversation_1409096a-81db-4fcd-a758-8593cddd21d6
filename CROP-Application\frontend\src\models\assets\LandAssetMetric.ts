import type { DiscountPoint } from '@store/services/sdk'
import { BN, fromBigNumber, toBigNumber } from '@types'

export interface SerializableLandAssetMetric {
  id: number
  landAsset?: number
  isProductive: boolean
  isCanopy: boolean
  isHorticulture: boolean
  landClassName: string

  nonTitledLandType?: string

  byPercentage?: boolean
  areaPercentage?: string
  areaM2?: string

  aepPerHectare?: string

  // set on (productive) horticulture assets
  licenceValueHa: string
  canopyPlantedValueHa: string
  vineStructuresHa: string
  totalValueHaAtMaturity: string

  // set on all other others
  dollarPerHectare: string

  // used for land classes where we need to define total value not as a function of area
  totalValue?: string

  plantingVariety?: number
  plantingVarietyName: string
  plantingRegime?: string
  coverType?: string
  orchardType?: string
  plantedYear?: string
  maturity?: string
  graftingMethod?: string
  yearsToMaturity?: number

  // dairy & dairy support inputs
  irrigation?: string
  irrigationSource?: string
  irrigationWaterCost?: string
  irrigationPrimaryApplication?: string

  // computed (on backend primarily - frontend when manipulating)
  totalArea: string

  totalHectares: string

  totalDollarPerHectare: string
  total_AEP: string
  total_LWB: string
  total_LWBBy_AEP: string

  discountPoints: DiscountPoint[]
  hasDiscountCurve: boolean
  discountAtValuation: string
}

export function deserializeLandAssetMetric(
  landAssetMetric: SerializableLandAssetMetric
) {
  const {
    areaM2,
    areaPercentage,
    aepPerHectare,
    licenceValueHa,
    canopyPlantedValueHa,
    vineStructuresHa,
    totalValueHaAtMaturity,
    dollarPerHectare,
    totalArea,
    totalHectares,
    totalDollarPerHectare,
    totalValue,
    total_AEP,
    total_LWB,
    total_LWBBy_AEP,
    discountAtValuation,
    ...rest
  } = landAssetMetric

  return {
    ...rest,
    areaM2: areaM2 ? new BN(areaM2) : undefined,
    areaPercentage: areaPercentage ? new BN(areaPercentage) : undefined,
    aepPerHectare: aepPerHectare ? new BN(aepPerHectare) : undefined,
    totalValue: totalValue ? new BN(totalValue) : undefined,
    ...toBigNumber({
      licenceValueHa,
      canopyPlantedValueHa,
      vineStructuresHa,
      dollarPerHectare,
      totalArea,
      totalHectares,
      totalDollarPerHectare,
      totalValueHaAtMaturity,
      total_AEP,
      total_LWB,
      total_LWBBy_AEP,
      discountAtValuation,
    }),
  }
}

export type LandAssetMetric = ReturnType<typeof deserializeLandAssetMetric>

export function serializeLandAssetMetric(
  landAssetMetric: LandAssetMetric
): SerializableLandAssetMetric {
  const {
    areaM2,
    areaPercentage,
    aepPerHectare,
    licenceValueHa,
    canopyPlantedValueHa,
    vineStructuresHa,
    dollarPerHectare,
    totalValue,
    totalArea,
    totalHectares,
    totalDollarPerHectare,
    totalValueHaAtMaturity,
    total_AEP,
    total_LWB,
    total_LWBBy_AEP,
    discountAtValuation,
    ...rest
  } = landAssetMetric

  return {
    ...rest,
    // bleh
    areaM2: areaM2 ? areaM2.toString() : undefined,
    areaPercentage: areaPercentage ? areaPercentage.toString() : undefined,
    aepPerHectare: aepPerHectare ? aepPerHectare.toString() : undefined,
    totalValue: totalValue ? totalValue.toString() : undefined,
    ...fromBigNumber({
      licenceValueHa,
      canopyPlantedValueHa,
      vineStructuresHa,
      dollarPerHectare,
      totalArea,
      totalHectares,
      totalDollarPerHectare,
      totalValueHaAtMaturity,
      total_AEP,
      total_LWB,
      total_LWBBy_AEP,
      discountAtValuation,
    }),
  }
}
