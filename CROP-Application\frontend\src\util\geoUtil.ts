import { type MultiPolygon, area, combine, featureCollection } from '@turf/turf'
import type {
  Feature,
  FeatureCollection,
  GeoJsonProperties,
  Geometry,
  GeometryCollection,
} from 'geojson'

// TODO: just get rid of these

export function createFeatureGroupFromMap(
  geoFeatures: Record<string, Feature<MultiPolygon>>
): Feature<MultiPolygon> {
  const combined = combine({
    type: 'FeatureCollection',
    features: Object.values(geoFeatures).map((geoFeature) => geoFeature),
  })

  return combined.features[0] as Feature<MultiPolygon>
}

export function emptyFeatureCollection<
  G extends Geometry | null = Geometry,
  T = GeoJsonProperties,
>(): FeatureCollection<G, T> {
  return {
    type: 'FeatureCollection',
    features: [],
  }
}

export function calculateApproxArea(
  geometries: Record<string, Feature>
): number {
  const areas: number[] = Object.values(geometries).map((feature: Feature) => {
    return area(feature.geometry as Exclude<Geometry, GeometryCollection>)
  })
  return areas.reduceRight((a, b) => a + b, 0)
}

export function createFeatureFromMap<T>(
  featureMap: Record<string, Feature>,
  properties: T,
  id?: string | number
): Feature<MultiPolygon, T> | undefined {
  const geoFeatures = featureCollection(
    Object.values(featureMap).filter(
      (feature): feature is Feature<Exclude<Geometry, GeometryCollection>> =>
        feature?.geometry?.type !== 'GeometryCollection'
    )
  )
  const combinedFeatures = combine(geoFeatures)
  const multiPolygon = combinedFeatures.features.find(
    (feature): feature is Feature<MultiPolygon, never> =>
      feature.geometry.type === 'MultiPolygon'
  )
  if (multiPolygon !== undefined) {
    return {
      ...multiPolygon,
      id,
      properties: properties,
    }
  }
  return undefined
}

export function mapFeatureCollection<
  R,
  G extends Geometry | null = Geometry,
  P = GeoJsonProperties,
>(
  featureCollection: FeatureCollection<G, P>,
  mapFn: (properties: P) => R
): FeatureCollection<G, R> {
  return {
    ...featureCollection,
    features: featureCollection.features.map((feature) =>
      mapFeature(feature, mapFn)
    ),
  }
}

export function mapFeature<
  R,
  G extends Geometry | null = Geometry,
  P = GeoJsonProperties,
>(feature: Feature<G, P>, mapFn: (properties: P) => R): Feature<G, R> {
  return {
    ...feature,
    properties: mapFn(feature.properties),
  }
}
