import requests
from django.conf import settings
from typing import Dict, Any, Optional, Tuple
import base64
import logging

logger = logging.getLogger(__name__)

class PingFederateClient:
    @staticmethod
    def get_token_endpoint() -> str:
        return f"{settings.PINGFEDERATE_BASE_URL}{settings.PINGFEDERATE_TOKEN_ENDPOINT}"
    @staticmethod
    def get_userinfo_endpoint() -> str:
        return f"{settings.PINGFEDERATE_BASE_URL}{settings.PINGFEDERATE_USERINFO_ENDPOINT}"
    @staticmethod
    def get_introspection_endpoint() -> str:
        return f"{settings.PINGFEDERATE_BASE_URL}{settings.PINGFEDERATE_INTROSPECTION_ENDPOINT}"
    @staticmethod
    def get_authorization_endpoint() -> str:
        return f"{settings.PINGFEDERATE_BASE_URL}{settings.PINGFEDERATE_AUTHORIZATION_ENDPOINT}"
    @staticmethod
    def get_jwks_endpoint() -> str:
        return f"{settings.PINGFEDERATE_BASE_URL}{settings.PINGFEDERATE_JWKS_ENDPOINT}"
    @staticmethod
    def get_client_credentials_token() -> Optional[str]:
        try:
            client_auth = base64.b64encode(f"{settings.PINGFEDERATE_CLIENT_ID}:{settings.PINGFEDERATE_CLIENT_SECRET}".encode()).decode()
            response = requests.post(PingFederateClient.get_token_endpoint(),
                                     headers={
                                         "Authorization": f"Basic {client_auth}",
                                         "Content-Type": "application/x-www-form-urlencoded"
                                     },
                                     data={"grant_type": "client_credentials"},
                                     timeout=10)
            if response.status_code == 200:
                return response.json().get("access_token")
            else:
                logger.error("Failed to get client credentials, Token Response: %s", str(response.text))
                return None
        except Exception as e:
            logger.error("Error getting client credentials token - %s", str(e))
            return None

    @staticmethod
    def validate_token(token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        try:
            client_token = PingFederateClient.get_client_credentials_token()
            if not client_token:
                return False, None
            response = requests.post(PingFederateClient.get_introspection_endpoint(),
                                    headers={
                                        "Authorization": f"Bearer {client_token}",
                                        "Content-Type": "application/x-www-form-urlencoded"},
                                    data={"token": token},
                                    timeout=10)
            if response.status_code == 200:
                token_data = response.json()
                if token_data.get("active", False):
                    return True, token_data
                else:
                    logger.warning("Token is not active - %s", str(token_data))
                    return False, None
            else:
                logger.error("Failed to validate token - %s", str(response.text))
                return False, None
        except Exception as e:
            logger.error("Error validating token - %s", str(e))
            return False, None

    @staticmethod
    def get_user_info(token: str) -> Optional[Dict[str, Any]]:
        try:
            response = requests.get(PingFederateClient.get_userinfo_endpoint(),
                                    headers={"Authorization": f"Bearer {token}"},
                                    timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error("Failed to get user info - %s", str(response.text))
                return None
        except Exception as e:
            logger.error("Error getting user info - %s", str(e))
            return None
    @staticmethod
    def get_authorization_url(state: str, scope: str = "openid profile email") -> str:
        params = {
            "client_id": settings.PINGFEDERATE_CLIENT_ID,
            "response_type": "code",
            "redirect_uri": settings.PINGFEDERATE_REDIRECT_URI,
            "scope": scope,
            "state": state
        }
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"{PingFederateClient.get_authorization_endpoint()}?{query_string}"
    @staticmethod
    def exchange_code_for_token(code: str) -> Optional[Dict[str, Any]]:
        try:
            client_auth = base64.b64encode(str({settings.PINGFEDERATE_CLIENT_ID}:{settings.PINGFEDERATE_CLEINT_SECRET}).encode()).decode()
            response = requests.post(PingFederateClient.get_token_endpoint(),
                                     headers={
                                         "Authorization": "Basic %s", str(client_auth),
                                         "Content-Type": "application/x-www-form-urlencoded"},
                                     data={"grant_type": "authorization_code",
                                           "code": code,
                                           "redirect_uri": settings.PINGFEDEARTE_REDIRECT_URI},
                                     timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                logger.error("Failed to exchange code for token - %s", str(response.text))
                return None
        except Exception as e:
            logger.error("Error exchanging code for token - %s", str(e))
            return None


