.new {
  --color-pacific-blue: #0572e6;
  --color-ui-blue: #006bde;
  --color-white: #ffffff;

  --color-coral-reef: #ff746a;
  --color-deep-sea: #1d164c;
  --color-clear-skies: #8adaf9;
  --color-sand: #d7ccc3;

  --color-grey-1: #f4f3f4;
  --color-grey-2: #e4e4e4;
  --color-grey-3: #d2d2d2;
  --color-grey-4: #b3b3b3;
  --color-grey-5: #6f6f6f;
  --color-grey-6: #424242;

  --color-high-noon: #fad971;
  --color-high-noon-soft: #fce8a7;
  --color-high-noon-ultrasoft: #fff6e0;
  --color-golden-hour: #fbb570;
  --color-golden-hour-soft: #fbd2a6;
  --color-golden-hour-ultrasoft: #ffefe1;
  --color-sun-kissed: #ff9271;
  --color-sun-kissed-soft: #ffc7b6;
  --color-sun-kissed-ultrasoft: #ffe2d9;
  --color-coral-reef: #ff746a;
  --color-coral-reef-soft: #ffb9b4;
  --color-coral-reef-ultrasoft: #ffdcda;
  --color-clear-skies: #8adaf9;
  --color-clear-skies-soft: #c5edfc;
  --color-clear-skies-ultrasoft: #e2f6fd;
  --color-aquamarine: #3cbbb6;
  --color-aquamarine-soft: #9bdcd9;
  --color-aquamarine-ultrasoft: #ccedeb;
  --color-seaweed: #2dab8e;
  --color-seaweed-soft: #94d4c5;
  --color-seaweed-ultrasoft: #c8e9e1;
  --color-deep-sea: #1d164c;
  --color-deep-sea-soft: #8e8aa5;
  --color-deep-sea-ultrasoft: #c6c5d2;
  --color-pink-sunset: #ff69a2;
  --color-pink-sunset-soft: #fca4c5;
  --color-pink-sunset-ultrasoft: #ffdfea;
  --color-twilight: #805fff;
  --color-twilight-soft: #bdadff;
  --color-twilight-ultrasoft: #ddd5ff;

  --color-semantic-green: #16725d;
  --color-semantic-red: #c12d23;
  --color-semantic-orange: #e58a00;

  --color-primary: var(--color-grey-6);
  --color-secondary: var(--color-grey-5);
  --color-highlight: var(--color-pacific-blue);
  --color-accent: var(--color-deep-sea);
  --color-background: var(--color-white);
  --color-border-blue: rgb(30, 128, 232);

  --color-dark: var(--color-pacific-blue);
  --color-inverse: var(--color-white);

  --color-success: var(--color-semantic-green);
  --color-error: var(--color-semantic-red);
  --color-warn: var(--color-semantic-orange);

  --color-interactive: var(--color-ui-blue);
  --color-disabled: var(--color-grey-4);

  --font-size-root: 16px;
  --font-size-small: 13px;

  /* Unconfirmed styles: */
  --border: 1px solid var(--color-grey-2);
  --border--divider: var(--border);
  --border-radius: 5px;

  /*a {*/
  /*  color: currentColor;*/
  /*}*/
}
