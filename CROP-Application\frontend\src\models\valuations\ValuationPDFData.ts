import type { Valuation } from '../../types'
import type { DistrictValuationRoll } from '../dvr/DistrictValuationRoll'
import type { PDFData } from '../generic/PDFData'
import type { AnzUnionFeatureCollection } from '../gis/AnzUnionFeatureCollection'
import type { TitleFeatureCollection } from '../title/TitleFeatureCollection'

export interface ValuationPDFData extends PDFData {
  valuation: Valuation
  titles: TitleFeatureCollection
  districtValuationRoll: Array<DistrictValuationRoll>
  anzUnion: AnzUnionFeatureCollection
  summary: {
    landDescription: string
    elevation: string
    serviceCentres: string
    anzUnion: {
      luc: { [key: string]: number }
      ps: { [key: string]: number }
      vegetation: { [key: string]: number }
    }
  }
}
