import { Descriptions, Form, Input, Space, message } from 'antd'
import { useForm } from 'antd/lib/form/Form'
import type React from 'react'
import { useCallback, useEffect, useState } from 'react'
import { AssessmentForm } from '@components/form/AssessmentForm'
import { Title } from '@components/typography'
import useFormSubmit from '@hooks/useFormSubmit'
import type { ValuationsSavedTitle } from '@store/services/sdk'
import sdk from '@store/services/sdk'
import { getQueryErrorMessage } from '@util/error'
import ValuationTitleMemorials from './ValuationTitleMemorials'

type Props = {
  title: ValuationsSavedTitle
}

const PAYLOAD_NAME = 'valuationsTitleReviewWritable'

const ValuationTitle = ({ title }: Props) => {
  const [form] = useForm()
  const [dirty, setDirty] = useState(false)

  const [selectedMemorials, setSelectedMemorials] = useState<React.Key[]>([])

  const { data, isLoading: titleLoading } =
    sdk.useValuationsTitlesRetrieveQuery({
      valuationPk: title.valuation,
      pk: title.id,
    })

  const review = data?.review
  const savedMemorials = review?.memorials.map((memorial) => memorial.fid)

  // Should be able to replace by using nested serializer
  const [create] = sdk.useValuationsTitlesReviewsCreateMutation()
  const [update] = sdk.useValuationsTitlesReviewsUpdateMutation()

  useEffect(() => {
    form.setFieldValue('memorials', selectedMemorials)
    const comments = form.getFieldValue('comments')
    if (
      comments &&
      selectedMemorials.length &&
      selectedMemorials !== savedMemorials
    ) {
      setDirty(true)
    }
  }, [form, savedMemorials, selectedMemorials])

  const submit = useCallback(
    (body) => {
      if (title.review) {
        return update({ pk: title.review.id, [PAYLOAD_NAME]: body }).unwrap()
      }
      return create({
        [PAYLOAD_NAME]: {
          ...body,
          title: title.id,
        },
      }).unwrap()
    },
    [create, title.id, title.review, update]
  )

  const { handleFinish, handleFinishFailed, loading } = useFormSubmit(submit)

  return (
    <Space direction="vertical" style={{ width: '100%', marginBottom: 24 }}>
      <Descriptions column={1} bordered size="small">
        <Descriptions.Item label="Owners">{title.owners}</Descriptions.Item>
        <Descriptions.Item label="Status">{title.status}</Descriptions.Item>
        <Descriptions.Item label="Guarantee Status">
          {title.guaranteeStatus}
        </Descriptions.Item>
      </Descriptions>
      <ValuationTitleMemorials
        key={JSON.stringify(savedMemorials)}
        defaultSelectedRowKeys={savedMemorials as React.Key[]}
        title={title}
        onChange={setSelectedMemorials}
      />
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <Title style={{ margin: '0.5em 0 0', padding: '0 8px' }}>
          {title.titleNo} Review
        </Title>
        <AssessmentForm
          key={JSON.stringify(review)}
          dirty={dirty}
          disabled={titleLoading}
          form={form}
          initialValues={review}
          loading={loading}
          onFinish={handleFinish}
          onFinishFailed={handleFinishFailed}
        >
          <Form.Item hidden initialValue={savedMemorials} name="memorials">
            <Input />
          </Form.Item>
        </AssessmentForm>
      </Space>
    </Space>
  )
}

export default ValuationTitle
