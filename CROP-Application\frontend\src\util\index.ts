import type { FormListFieldData } from 'antd/lib/form/FormList'
import BigNumber from 'bignumber.js'
import { format } from 'date-fns'

export function toURL(path: string) {
  if (process.env.NODE_ENV === 'development') {
    const baseURL = 'http://localhost:8000'
    return new URL(path, baseURL).href
  }
  return path
}

export const titleCase = (str: string) =>
  str.replace(/\b\S/g, (t) => t.toUpperCase())

const formatOptions = { style: 'currency' as const, currency: 'NZD' as const }
let currencyNumberFormat: Intl.NumberFormat
try {
  currencyNumberFormat = Intl.NumberFormat(undefined, {
    ...formatOptions,
    currencyDisplay: 'narrowSymbol',
    maximumFractionDigits: 0,
    minimumFractionDigits: 0,
  })
} catch (_) {
  currencyNumberFormat = Intl.NumberFormat(undefined, {
    ...formatOptions,
    maximumFractionDigits: 0,
    minimumFractionDigits: 0,
  })
}
export const formatDollarValue = (
  dollars: number | BigNumber | null,
  maximumFractionDigits?: number
) => {
  const numberFormat = maximumFractionDigits
    ? Intl.NumberFormat(undefined, {
        ...formatOptions,
        currencyDisplay: 'narrowSymbol',
        maximumFractionDigits,
        minimumFractionDigits: maximumFractionDigits,
      })
    : currencyNumberFormat
  if (
    dollars === undefined ||
    dollars === null ||
    (!BigNumber.isBigNumber(dollars) && Number.isNaN(dollars))
  ) {
    return numberFormat.format(0)
  }
  // BigNumber is valid input to NumberFormat.format
  return numberFormat.format(dollars as number)
}

export const formatPercentageValue = (
  decimal: number | string | null,
  decimalPlaces?: number
) => {
  if (!decimal) return
  const value = Number(decimal) * 100
  return `${value.toFixed(decimalPlaces ?? 2)}%`
}

const areaNumberFormat = Intl.NumberFormat(undefined, {
  maximumFractionDigits: 3,
  minimumFractionDigits: 0,
})

export const formatArea = (
  area: number | BigNumber,
  unit: 'm2' | 'ha'
): string => {
  const formattedArea = BigNumber.isBigNumber(area)
    ? area.toFixed(3)
    : areaNumberFormat.format(area)
  return `${formattedArea}\u00A0${unit}`
}

export const formatNumber = (
  value?: number | BigNumber,
  precision?: number
) => {
  if (BigNumber.isBigNumber(value)) {
    return !value.isNaN() ? value.toFixed(precision ?? 2) : '-'
  }
  return value !== undefined && value !== null && !Number.isNaN(value)
    ? `${value.toFixed(precision ?? 2)}`
    : '-'
}

export const formatDateFromIso = (value?: string) => {
  return (value || '').toString().slice(0, 10)
}

export const formatDate = (value?: string | Date) => {
  return value ? format(new Date(value), 'dd/MM/yyyy') : ''
}

export const toNamePath = (
  field: FormListFieldData | undefined,
  fieldName: string | string[]
) => {
  return ([] as string[])
    .concat(field?.name?.toString() ?? [])
    .concat(fieldName)
}

export const roundToNearestBN = (
  value: BigNumber,
  nearest?: number,
  dontRound?: boolean
): BigNumber => {
  if (nearest !== undefined && nearest !== null) {
    return value.div(nearest).decimalPlaces(0).multipliedBy(nearest)
  }
  if (!dontRound) {
    return value.integerValue()
  }
  return value
}

export const roundToNearest = (
  value: number,
  nearest?: number,
  dontRound?: boolean
): number => {
  if (nearest !== undefined && nearest !== null) {
    return Math.round(value / nearest) * nearest
  }
  if (!dontRound) {
    return Math.round(value)
  }
  return value
}

export const formatAEP = (value?: number | BigNumber, precision?: number) => {
  if (BigNumber.isBigNumber(value)) {
    return value.toFixed(precision ?? 0)
  }

  return value !== undefined && value !== null
    ? Intl.NumberFormat(undefined, {
        maximumFractionDigits: precision ?? 0,
        minimumFractionDigits: 0,
      }).format(value)
    : '-'
}

/**
 * @deprecated use lodash get?
 */
export function getPropertyValue<T, U>(
  object: T,
  key: string | string[] | ((entity: T) => U)
) {
  if (typeof key === 'function') {
    return key(object)
  }

  const path = typeof key === 'string' ? [key] : key
  const value = object
  // biome-ignore lint:
  let ret
  for (const current of path) {
    if (!value) {
      return undefined
    }
    ret = value?.[current as keyof T]
  }
  return ret
}

export const toOption = (value: string) => ({ value, label: value })
