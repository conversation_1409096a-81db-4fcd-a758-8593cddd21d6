import { isDate, parse } from 'date-fns'
import plural from 'pluralize'

export const andJoin = (values: unknown[] = [], word = 'and') => {
  const head = values.slice(0, -1).join(', ').trim()
  const tail = values.slice(-1).join('')

  if (!head) return tail
  return head.concat(` ${word} `, tail)
}

export const concatWith =
  (delimiter: string) =>
  (...strings: string[]) =>
    strings.join(delimiter)

// Duplicate date library, but getting the ball rolling on replacing Moment
export const getDateTextTime = (text?: string) => {
  if (!text) return 0
  // Strip delimiters so it doesn't matter
  const parsed = parse(text.replace(/\/|-/g, ''), 'ddMMyyyy', new Date())
  if (!isDate(parsed)) return 0
  return parsed.getTime()
}

export const nullishAsEmpty = (value: string | number | undefined) =>
  value ?? ''

export const pluralize = plural as (word: string, count?: number) => string

export const placeholder = (
  value: string | number | undefined,
  placeholder = '-'
) => value ?? placeholder
