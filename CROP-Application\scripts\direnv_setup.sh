#!/usr/bin/env bash
set -e

SERVICE_NAME=$1

if [ -z "$SERVICE_NAME" ]; then
  echo "Usage: $0 <service_name>"
  exit 1
fi

pushd "$SERVICE_NAME"

cat <<EOF>> .envrc
export DATABASE_USER=crop
export DATABASE_PASSWORD=crop
export DATABASE_NAME=crop
export DATABASE_HOST=db
export DATABASE_PORT=7654
export DATABASE_URL="postgresql://${DATABASE_USER}:${DATABASE_PASSWORD}@${DATABASE_HOST}:${DATABASE_PORT}/${DATABASE_NAME}"
export REDIS_COMMAND="redis-cli -h localhost -p 7480"
UV_PROJECT_ENVIRONMENT="./.venv"
source .venv/bin/activate
EOF
popd
