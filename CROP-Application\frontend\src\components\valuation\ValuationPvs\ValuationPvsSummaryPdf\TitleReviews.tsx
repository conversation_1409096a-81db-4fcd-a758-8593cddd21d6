import { Text } from '@react-pdf/renderer'
import React from 'react'
import { PdfParagraph, stylesheet } from '@components/pdf'
import type { ValuationsTitleReviewSummary } from '@store/services/sdk'
import { insertIf } from '@util/helpers'

interface Props {
  reviews: ValuationsTitleReviewSummary[]
}

const TitleReviews = ({ reviews }: Props) => {
  return (
    <>
      {reviews.map(({ id, comments, memorialNos, titleNo }) => {
        const lines: string = [
          comments,
          ...insertIf(!!memorialNos, `Concerning memorials: ${memorialNos}`),
        ].join('\n')

        return (
          <React.Fragment key={id}>
            <Text style={{ ...stylesheet.heading, marginTop: 0 }}>
              {titleNo}
            </Text>
            <PdfParagraph>{lines}</PdfParagraph>
          </React.Fragment>
        )
      })}
    </>
  )
}

export default TitleReviews
