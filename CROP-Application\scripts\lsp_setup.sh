#!/usr/bin/env bash
set -e

SERVICE_NAME=$1

if [ -z "$SERVICE_NAME" ]; then
  echo "Usage: $0 <service_name>"
  exit 1
fi

pushd "$SERVICE_NAME"

uv add "python-lsp-server[all]";
uv add pylsp-mypy pylsp-rope pytest python-lsp-black python-lsp-server;


cat >> pyproject.toml <<'EOF'
[tool.pytest.ini_options]
pythonpath = "."
minversion = "6.0"
addopts = "-n 4 -p no:warnings"
console_output_style = "progress"
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"


[tool.pylsp.plugins.rope_autoimport]
enabled = true

[tool.pylsp.plugins.rope_autoimport.completions]
enabled = true

[tool.pylsp.plugins.rope_autoimport.code_actions]
enabled = true

[tool.pylsp]
python-lsp-black.enabled = true

[tool.pylsp.plugins]
python_lsp_ruff.enabled = true
python_lsp_ruff.line-length = 100
python_lsp_ruff.fixable = true
python_lsp_ruff.select = [
    "E",  # Errors
    "F",  # Failures
    "W",  # Warnings
    "C",  # Complexity
    "B",  # Best practices
    "D",  # Docstrings
    "N",  # Naming conventions
    "S",  # Security
    "UP",  # Python upgrades
    "YTT",  # Type hints
    "SIM",  # Simplifications
    "RUF",  # Ruff-specific rules
    "I",  # Import sorting
]
python_lsp_ruff.ignore = [
    "FBT001", "FBT002",  # f-string issues
    "BLE001",            # Black compatibility (irrelevant now)
    "E501",              # Line too long (handled by Ruff config)
    "D100", "D101", "D102", "D103", "D104", "D401",  # Docstring relaxations
    "B008",              # Depends() false positives
    "S101",              # Naked asserts for tests
    "S608"               # SQLi warning ignored in tests
]
python_lsp_ruff.exclude = [
    "alembic/",
    "scripts/",
    "test/",
    "venv/",
    ".tox/",
    ".git/"
]
[tool.ruff.isort]
force-single-line = true
combine-as-imports = false
lines-between-types = 1
known-first-party = ["podcore"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

[tool.mypy]
python_version = "3.13"
ignore_missing_imports = true
explicit_package_bases = true
EOF
popd
