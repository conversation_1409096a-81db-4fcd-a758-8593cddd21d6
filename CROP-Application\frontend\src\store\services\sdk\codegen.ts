import { baseApi as api } from "../baseApi";
export const addTagTypes = [
  "addresses",
  "admin",
  "alert",
  "anzsic",
  "asset_pvs_export",
  "assets",
  "boundaries",
  "business-units",
  "ccra",
  "instruction_letter",
  "customer",
  "customer-group-search",
  "customer-groups",
  "customer-search",
  "customer_group",
  "customer_report_template",
  "data_dictionary",
  "dvr",
  "emission",
  "entity-group-search",
  "events",
  "explorer",
  "frontline-sales",
  "layers",
  "lineagez",
  "location",
  "lookup_solar_installers",
  "lookup_species",
  "loss_model",
  "news",
  "notifications",
  "panel_property_types",
  "panel_rural_specialisations",
  "panel_valuers",
  "panel_valuer_regions",
  "peril",
  "peril_category",
  "peril_source",
  "peril_type",
  "project",
  "project_approvals",
  "project_asset",
  "project_asset_category",
  "project_category",
  "project_files",
  "project_form_option",
  "project_stats",
  "project_subcategory",
  "valuations",
  "resource-consent-records",
  "sale_exists",
  "sales",
  "valocity_sales",
  "sales_bbox",
  "scenario_export",
  "smap_siblings",
  "title",
  "title_likely_address",
  "title_reverse_search",
  "title_search",
  "titles_bbox",
  "trading_group",
  "trading_group_region",
  "user",
  "user_options",
  "usernames",
  "valid_kpi_measures",
  "valocity_sales_bbox",
  "valuation_firms",
  "valuation_search",
] as const;
const injectedRtkApi = api
  .enhanceEndpoints({
    addTagTypes,
  })
  .injectEndpoints({
    endpoints: (build) => ({
      addressesList: build.query<AddressesListApiResponse, AddressesListApiArg>(
        {
          query: () => ({ url: `/api/addresses/` }),
          providesTags: ["addresses"],
        }
      ),
      addressesCreate: build.mutation<
        AddressesCreateApiResponse,
        AddressesCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/addresses/`,
          method: "POST",
          body: queryArg.addresses,
        }),
        invalidatesTags: ["addresses"],
      }),
      addressesRetrieve: build.query<
        AddressesRetrieveApiResponse,
        AddressesRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/addresses/${queryArg.pk}/` }),
        providesTags: ["addresses"],
      }),
      addressesUpdate: build.mutation<
        AddressesUpdateApiResponse,
        AddressesUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/addresses/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.addresses,
        }),
        invalidatesTags: ["addresses"],
      }),
      addressesPartialUpdate: build.mutation<
        AddressesPartialUpdateApiResponse,
        AddressesPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/addresses/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedAddresses,
        }),
        invalidatesTags: ["addresses"],
      }),
      addressesDestroy: build.mutation<
        AddressesDestroyApiResponse,
        AddressesDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/addresses/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["addresses"],
      }),
      addressesSummaryRetrieve: build.query<
        AddressesSummaryRetrieveApiResponse,
        AddressesSummaryRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/addresses/${queryArg.pk}/summary/`,
        }),
        providesTags: ["addresses"],
      }),
      addressesSummaryList: build.query<
        AddressesSummaryListApiResponse,
        AddressesSummaryListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/addresses/summary/`,
          params: { ids: queryArg.ids },
        }),
        providesTags: ["addresses"],
      }),
      adminProjectsList: build.query<
        AdminProjectsListApiResponse,
        AdminProjectsListApiArg
      >({
        query: () => ({ url: `/api/admin/projects/` }),
        providesTags: ["admin"],
      }),
      adminProjectsRetrieve: build.query<
        AdminProjectsRetrieveApiResponse,
        AdminProjectsRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/admin/projects/${queryArg.pk}/` }),
        providesTags: ["admin"],
      }),
      adminSalesList: build.query<
        AdminSalesListApiResponse,
        AdminSalesListApiArg
      >({
        query: () => ({ url: `/api/admin/sales/` }),
        providesTags: ["admin"],
      }),
      adminSalesRetrieve: build.query<
        AdminSalesRetrieveApiResponse,
        AdminSalesRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/admin/sales/${queryArg.pk}/` }),
        providesTags: ["admin"],
      }),
      adminSalesDeleteCreate: build.mutation<
        AdminSalesDeleteCreateApiResponse,
        AdminSalesDeleteCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/admin/sales/${queryArg.pk}/delete/`,
          method: "POST",
          body: queryArg.adminSale,
        }),
        invalidatesTags: ["admin"],
      }),
      adminTerritorialAuthorityAssignmentList: build.query<
        AdminTerritorialAuthorityAssignmentListApiResponse,
        AdminTerritorialAuthorityAssignmentListApiArg
      >({
        query: () => ({ url: `/api/admin/territorial_authority_assignment/` }),
        providesTags: ["admin"],
      }),
      adminTerritorialAuthorityAssignmentCreate: build.mutation<
        AdminTerritorialAuthorityAssignmentCreateApiResponse,
        AdminTerritorialAuthorityAssignmentCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/admin/territorial_authority_assignment/`,
          method: "POST",
          body: queryArg.adminTerroritorialAuthorityAssignment,
        }),
        invalidatesTags: ["admin"],
      }),
      adminTerritorialAuthorityAssignmentRetrieve: build.query<
        AdminTerritorialAuthorityAssignmentRetrieveApiResponse,
        AdminTerritorialAuthorityAssignmentRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/admin/territorial_authority_assignment/${queryArg.pk}/`,
        }),
        providesTags: ["admin"],
      }),
      adminTerritorialAuthorityAssignmentUpdate: build.mutation<
        AdminTerritorialAuthorityAssignmentUpdateApiResponse,
        AdminTerritorialAuthorityAssignmentUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/admin/territorial_authority_assignment/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.adminTerroritorialAuthorityAssignment,
        }),
        invalidatesTags: ["admin"],
      }),
      adminTerritorialAuthorityAssignmentPartialUpdate: build.mutation<
        AdminTerritorialAuthorityAssignmentPartialUpdateApiResponse,
        AdminTerritorialAuthorityAssignmentPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/admin/territorial_authority_assignment/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedAdminTerroritorialAuthorityAssignment,
        }),
        invalidatesTags: ["admin"],
      }),
      adminTerritorialAuthorityAssignmentDestroy: build.mutation<
        AdminTerritorialAuthorityAssignmentDestroyApiResponse,
        AdminTerritorialAuthorityAssignmentDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/admin/territorial_authority_assignment/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["admin"],
      }),
      adminUsageRetrieve: build.query<
        AdminUsageRetrieveApiResponse,
        AdminUsageRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/admin/usage/`,
          params: {
            end_date: queryArg.endDate,
            start_date: queryArg.startDate,
            time_unit: queryArg.timeUnit,
            users: queryArg.users,
          },
        }),
        providesTags: ["admin"],
      }),
      adminUsersList: build.query<
        AdminUsersListApiResponse,
        AdminUsersListApiArg
      >({
        query: () => ({ url: `/api/admin/users/` }),
        providesTags: ["admin"],
      }),
      alertList: build.query<AlertListApiResponse, AlertListApiArg>({
        query: () => ({ url: `/api/alert/` }),
        providesTags: ["alert"],
      }),
      alertCreate: build.mutation<AlertCreateApiResponse, AlertCreateApiArg>({
        query: (queryArg) => ({
          url: `/api/alert/`,
          method: "POST",
          body: queryArg.alert,
        }),
        invalidatesTags: ["alert"],
      }),
      alertRetrieve: build.query<AlertRetrieveApiResponse, AlertRetrieveApiArg>(
        {
          query: (queryArg) => ({ url: `/api/alert/${queryArg.pk}/` }),
          providesTags: ["alert"],
        }
      ),
      alertUpdate: build.mutation<AlertUpdateApiResponse, AlertUpdateApiArg>({
        query: (queryArg) => ({
          url: `/api/alert/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.alert,
        }),
        invalidatesTags: ["alert"],
      }),
      alertPartialUpdate: build.mutation<
        AlertPartialUpdateApiResponse,
        AlertPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/alert/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedAlert,
        }),
        invalidatesTags: ["alert"],
      }),
      alertDestroy: build.mutation<AlertDestroyApiResponse, AlertDestroyApiArg>(
        {
          query: (queryArg) => ({
            url: `/api/alert/${queryArg.pk}/`,
            method: "DELETE",
          }),
          invalidatesTags: ["alert"],
        }
      ),
      anzsicList: build.query<AnzsicListApiResponse, AnzsicListApiArg>({
        query: () => ({ url: `/api/anzsic/` }),
        providesTags: ["anzsic"],
      }),
      anzsicRetrieve: build.query<
        AnzsicRetrieveApiResponse,
        AnzsicRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/anzsic/${queryArg.pk}/` }),
        providesTags: ["anzsic"],
      }),
      assetPvsExportRetrieve: build.query<
        AssetPvsExportRetrieveApiResponse,
        AssetPvsExportRetrieveApiArg
      >({
        query: () => ({ url: `/api/asset_pvs_export/` }),
        providesTags: ["asset_pvs_export"],
      }),
      assetsRetrieve: build.query<
        AssetsRetrieveApiResponse,
        AssetsRetrieveApiArg
      >({
        query: () => ({ url: `/api/assets/` }),
        providesTags: ["assets"],
      }),
      assetsBulkCreate: build.mutation<
        AssetsBulkCreateApiResponse,
        AssetsBulkCreateApiArg
      >({
        query: () => ({ url: `/api/assets/bulk/`, method: "POST" }),
        invalidatesTags: ["assets"],
      }),
      assetsBulkEditCreate: build.mutation<
        AssetsBulkEditCreateApiResponse,
        AssetsBulkEditCreateApiArg
      >({
        query: () => ({ url: `/api/assets/bulk_edit/`, method: "POST" }),
        invalidatesTags: ["assets"],
      }),
      assetsGenerateDescriptionCreate: build.mutation<
        AssetsGenerateDescriptionCreateApiResponse,
        AssetsGenerateDescriptionCreateApiArg
      >({
        query: () => ({
          url: `/api/assets/generate_description/`,
          method: "POST",
        }),
        invalidatesTags: ["assets"],
      }),
      assetsRemainingGeometryCreate: build.mutation<
        AssetsRemainingGeometryCreateApiResponse,
        AssetsRemainingGeometryCreateApiArg
      >({
        query: () => ({
          url: `/api/assets/remaining_geometry/`,
          method: "POST",
        }),
        invalidatesTags: ["assets"],
      }),
      assetsTitleApportionmentRetrieve: build.query<
        AssetsTitleApportionmentRetrieveApiResponse,
        AssetsTitleApportionmentRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/assets/title_apportionment/`,
          params: { valuation_id: queryArg.valuationId },
        }),
        providesTags: ["assets"],
      }),
      boundariesList: build.query<
        BoundariesListApiResponse,
        BoundariesListApiArg
      >({
        query: () => ({ url: `/api/boundaries/` }),
        providesTags: ["boundaries"],
      }),
      boundariesRetrieve: build.query<
        BoundariesRetrieveApiResponse,
        BoundariesRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/boundaries/${queryArg.pk}/` }),
        providesTags: ["boundaries"],
      }),
      boundariesLookupList: build.query<
        BoundariesLookupListApiResponse,
        BoundariesLookupListApiArg
      >({
        query: () => ({ url: `/api/boundaries/lookup/` }),
        providesTags: ["boundaries"],
      }),
      businessUnitsList: build.query<
        BusinessUnitsListApiResponse,
        BusinessUnitsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/business-units/`,
          params: { all: queryArg.all },
        }),
        providesTags: ["business-units"],
      }),
      ccraCcraList: build.query<CcraCcraListApiResponse, CcraCcraListApiArg>({
        query: (queryArg) => ({
          url: `/api/ccra/ccra/`,
          params: { customer_id: queryArg.customerId },
        }),
        providesTags: ["ccra"],
      }),
      ccraCcraCreate: build.mutation<
        CcraCcraCreateApiResponse,
        CcraCcraCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/ccra/ccra/`,
          method: "POST",
          body: queryArg.ccra,
        }),
        invalidatesTags: ["ccra"],
      }),
      ccraCcraRetrieve: build.query<
        CcraCcraRetrieveApiResponse,
        CcraCcraRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/ccra/ccra/${queryArg.pk}/` }),
        providesTags: ["ccra"],
      }),
      ccraCcraUpdate: build.mutation<
        CcraCcraUpdateApiResponse,
        CcraCcraUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/ccra/ccra/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.ccra,
        }),
        invalidatesTags: ["ccra"],
      }),
      ccraCcraPartialUpdate: build.mutation<
        CcraCcraPartialUpdateApiResponse,
        CcraCcraPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/ccra/ccra/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedCcra,
        }),
        invalidatesTags: ["ccra"],
      }),
      ccraCcraDestroy: build.mutation<
        CcraCcraDestroyApiResponse,
        CcraCcraDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/ccra/ccra/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["ccra"],
      }),
      ccraCcraUploadFileCreate: build.mutation<
        CcraCcraUploadFileCreateApiResponse,
        CcraCcraUploadFileCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/ccra/ccra/${queryArg.pk}/upload_file/`,
          method: "POST",
          body: queryArg.upload,
        }),
        invalidatesTags: ["ccra"],
      }),
      ccraCcraReportTypesList: build.query<
        CcraCcraReportTypesListApiResponse,
        CcraCcraReportTypesListApiArg
      >({
        query: () => ({ url: `/api/ccra/ccra/report_types/` }),
        providesTags: ["ccra"],
      }),
      ccraFilesList: build.query<CcraFilesListApiResponse, CcraFilesListApiArg>(
        {
          query: (queryArg) => ({
            url: `/api/ccra/files/`,
            params: { ccra_id: queryArg.ccraId },
          }),
          providesTags: ["ccra"],
        }
      ),
      ccraFilesRetrieve: build.query<
        CcraFilesRetrieveApiResponse,
        CcraFilesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/ccra/files/${queryArg.pk}/`,
          params: { ccra_id: queryArg.ccraId },
        }),
        providesTags: ["ccra"],
      }),
      ccraFilesDestroy: build.mutation<
        CcraFilesDestroyApiResponse,
        CcraFilesDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/ccra/files/${queryArg.pk}/`,
          method: "DELETE",
          params: { ccra_id: queryArg.ccraId },
        }),
        invalidatesTags: ["ccra"],
      }),
      ccraFormOptionsList: build.query<
        CcraFormOptionsListApiResponse,
        CcraFormOptionsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/ccra/form-options/`,
          params: { optionType: queryArg.optionType },
        }),
        providesTags: ["ccra"],
      }),
      commercialInstructionLetterList: build.query<
        CommercialInstructionLetterListApiResponse,
        CommercialInstructionLetterListApiArg
      >({
        query: () => ({ url: `/api/commercial_instruction_letter/` }),
        providesTags: ["instruction_letter"],
      }),
      commercialInstructionLetterCreate: build.mutation<
        CommercialInstructionLetterCreateApiResponse,
        CommercialInstructionLetterCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/commercial_instruction_letter/`,
          method: "POST",
          body: queryArg.commercialInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      commercialInstructionLetterRetrieve: build.query<
        CommercialInstructionLetterRetrieveApiResponse,
        CommercialInstructionLetterRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/commercial_instruction_letter/${queryArg.pk}/`,
        }),
        providesTags: ["instruction_letter"],
      }),
      commercialInstructionLetterUpdate: build.mutation<
        CommercialInstructionLetterUpdateApiResponse,
        CommercialInstructionLetterUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/commercial_instruction_letter/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.commercialInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      commercialInstructionLetterPartialUpdate: build.mutation<
        CommercialInstructionLetterPartialUpdateApiResponse,
        CommercialInstructionLetterPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/commercial_instruction_letter/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedCommercialInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      commercialInstructionLetterDestroy: build.mutation<
        CommercialInstructionLetterDestroyApiResponse,
        CommercialInstructionLetterDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/commercial_instruction_letter/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      commercialInstructionLetterPdfDataRetrieve: build.query<
        CommercialInstructionLetterPdfDataRetrieveApiResponse,
        CommercialInstructionLetterPdfDataRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/commercial_instruction_letter/${queryArg.pk}/pdf_data/`,
        }),
        providesTags: ["instruction_letter"],
      }),
      customerList: build.query<CustomerListApiResponse, CustomerListApiArg>({
        query: (queryArg) => ({
          url: `/api/customer/`,
          params: {
            customer_set_code: queryArg.customerSetCode,
            format: queryArg.format,
            match: queryArg.match,
            officer_code: queryArg.officerCode,
            page: queryArg.page,
            segment: queryArg.segment,
            size: queryArg.size,
          },
        }),
        providesTags: ["customer"],
      }),
      customerGroupSearchList: build.query<
        CustomerGroupSearchListApiResponse,
        CustomerGroupSearchListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer-group-search/`,
          params: {
            customer_set_code: queryArg.customerSetCode,
            match: queryArg.match,
            page: queryArg.page,
            segment: queryArg.segment,
            size: queryArg.size,
          },
        }),
        providesTags: ["customer-group-search"],
      }),
      customerGroupSearchRetrieve: build.query<
        CustomerGroupSearchRetrieveApiResponse,
        CustomerGroupSearchRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer-group-search/${queryArg.pk}/`,
        }),
        providesTags: ["customer-group-search"],
      }),
      customerGroupsList: build.query<
        CustomerGroupsListApiResponse,
        CustomerGroupsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer-groups/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["customer-groups"],
      }),
      customerGroupsRetrieve: build.query<
        CustomerGroupsRetrieveApiResponse,
        CustomerGroupsRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/customer-groups/${queryArg.pk}/` }),
        providesTags: ["customer-groups"],
      }),
      customerGroupsLendingList: build.query<
        CustomerGroupsLendingListApiResponse,
        CustomerGroupsLendingListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer-groups/${queryArg.pk}/lending/`,
        }),
        providesTags: ["customer-groups"],
      }),
      customerSearchList: build.query<
        CustomerSearchListApiResponse,
        CustomerSearchListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer-search/`,
          params: {
            customer_set_code: queryArg.customerSetCode,
            ids: queryArg.ids,
            match: queryArg.match,
            page: queryArg.page,
            segment: queryArg.segment,
            size: queryArg.size,
          },
        }),
        providesTags: ["customer-search"],
      }),
      customerDvrList: build.query<
        CustomerDvrListApiResponse,
        CustomerDvrListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.customerPk}/dvr/`,
        }),
        providesTags: ["customer"],
      }),
      customerDvrAdd: build.mutation<
        CustomerDvrAddApiResponse,
        CustomerDvrAddApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.customerPk}/dvr/add/`,
          method: "PUT",
          body: queryArg.customerDistrictValuationRoll,
        }),
        invalidatesTags: ["customer"],
      }),
      customerDvrRemove: build.mutation<
        CustomerDvrRemoveApiResponse,
        CustomerDvrRemoveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.customerPk}/dvr/remove/`,
          method: "PUT",
          body: queryArg.customerDistrictValuationRoll,
        }),
        invalidatesTags: ["customer"],
      }),
      customerDvrSet: build.mutation<
        CustomerDvrSetApiResponse,
        CustomerDvrSetApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.customerPk}/dvr/set/`,
          method: "PUT",
          body: queryArg.customerDistrictValuationRoll,
        }),
        invalidatesTags: ["customer"],
      }),
      customerKpiList: build.query<
        CustomerKpiListApiResponse,
        CustomerKpiListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.customerPk}/kpi/`,
        }),
        providesTags: ["customer"],
      }),
      customerKpiBenchmarkList: build.query<
        CustomerKpiBenchmarkListApiResponse,
        CustomerKpiBenchmarkListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.customerPk}/kpi/benchmark/`,
          params: {
            anzsic: queryArg.anzsic,
            denominator: queryArg.denominator,
            measure: queryArg.measure,
            region: queryArg.region,
          },
        }),
        providesTags: ["customer"],
      }),
      customerRetrieve: build.query<
        CustomerRetrieveApiResponse,
        CustomerRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/customer/${queryArg.pk}/` }),
        providesTags: ["customer"],
      }),
      customerBalancesList: build.query<
        CustomerBalancesListApiResponse,
        CustomerBalancesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.pk}/balances/`,
        }),
        providesTags: ["customer"],
      }),
      customerEmissionsRetrieve: build.query<
        CustomerEmissionsRetrieveApiResponse,
        CustomerEmissionsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.pk}/emissions/`,
        }),
        providesTags: ["customer"],
      }),
      customerGroupRetrieve: build.query<
        CustomerGroupRetrieveApiResponse,
        CustomerGroupRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/customer/${queryArg.pk}/group/` }),
        providesTags: ["customer"],
      }),
      customerGroupLendingList: build.query<
        CustomerGroupLendingListApiResponse,
        CustomerGroupLendingListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.pk}/group_lending/`,
        }),
        providesTags: ["customer"],
      }),
      customerLendingList: build.query<
        CustomerLendingListApiResponse,
        CustomerLendingListApiArg
      >({
        query: (queryArg) => ({ url: `/api/customer/${queryArg.pk}/lending/` }),
        providesTags: ["customer"],
      }),
      customerProxyEmissionsList: build.query<
        CustomerProxyEmissionsListApiResponse,
        CustomerProxyEmissionsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.pk}/proxy_emissions/`,
        }),
        providesTags: ["customer"],
      }),
      customerValuationsList: build.query<
        CustomerValuationsListApiResponse,
        CustomerValuationsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer/${queryArg.pk}/valuations/`,
        }),
        providesTags: ["customer"],
      }),
      customerGroupList: build.query<
        CustomerGroupListApiResponse,
        CustomerGroupListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer_group/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["customer_group"],
      }),
      customerGroupRetrieve2: build.query<
        CustomerGroupRetrieve2ApiResponse,
        CustomerGroupRetrieve2ApiArg
      >({
        query: (queryArg) => ({ url: `/api/customer_group/${queryArg.pk}/` }),
        providesTags: ["customer_group"],
      }),
      customerGroupLendingList2: build.query<
        CustomerGroupLendingList2ApiResponse,
        CustomerGroupLendingList2ApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer_group/${queryArg.pk}/lending/`,
        }),
        providesTags: ["customer_group"],
      }),
      customerReportTemplateList: build.query<
        CustomerReportTemplateListApiResponse,
        CustomerReportTemplateListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer_report_template/`,
          params: { segment: queryArg.segment },
        }),
        providesTags: ["customer_report_template"],
      }),
      customerReportTemplateCreate: build.mutation<
        CustomerReportTemplateCreateApiResponse,
        CustomerReportTemplateCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer_report_template/`,
          method: "POST",
          body: queryArg.customerReportTemplate,
        }),
        invalidatesTags: ["customer_report_template"],
      }),
      customerReportTemplateRetrieve: build.query<
        CustomerReportTemplateRetrieveApiResponse,
        CustomerReportTemplateRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer_report_template/${queryArg.pk}/`,
        }),
        providesTags: ["customer_report_template"],
      }),
      customerReportTemplateUpdate: build.mutation<
        CustomerReportTemplateUpdateApiResponse,
        CustomerReportTemplateUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer_report_template/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.customerReportTemplate,
        }),
        invalidatesTags: ["customer_report_template"],
      }),
      customerReportTemplatePartialUpdate: build.mutation<
        CustomerReportTemplatePartialUpdateApiResponse,
        CustomerReportTemplatePartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer_report_template/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedCustomerReportTemplate,
        }),
        invalidatesTags: ["customer_report_template"],
      }),
      customerReportTemplateDestroy: build.mutation<
        CustomerReportTemplateDestroyApiResponse,
        CustomerReportTemplateDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/customer_report_template/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["customer_report_template"],
      }),
      dataSchema: build.query<DataSchemaApiResponse, DataSchemaApiArg>({
        query: () => ({ url: `/api/data_dictionary/` }),
        providesTags: ["data_dictionary"],
      }),
      formTemplate: build.query<FormTemplateApiResponse, FormTemplateApiArg>({
        query: (queryArg) => ({
          url: `/api/data_dictionary/form_template/`,
          params: { model: queryArg.model },
        }),
        providesTags: ["data_dictionary"],
      }),
      dvrList: build.query<DvrListApiResponse, DvrListApiArg>({
        query: (queryArg) => ({
          url: `/api/dvr/`,
          params: { match: queryArg.match, page: queryArg.page },
        }),
        providesTags: ["dvr"],
      }),
      emissionList: build.query<EmissionListApiResponse, EmissionListApiArg>({
        query: () => ({ url: `/api/emission/` }),
        providesTags: ["emission"],
      }),
      emissionCreate: build.mutation<
        EmissionCreateApiResponse,
        EmissionCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/emission/`,
          method: "POST",
          body: queryArg.emission,
        }),
        invalidatesTags: ["emission"],
      }),
      emissionRetrieve: build.query<
        EmissionRetrieveApiResponse,
        EmissionRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/emission/${queryArg.pk}/` }),
        providesTags: ["emission"],
      }),
      emissionUpdate: build.mutation<
        EmissionUpdateApiResponse,
        EmissionUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/emission/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.emission,
        }),
        invalidatesTags: ["emission"],
      }),
      emissionPartialUpdate: build.mutation<
        EmissionPartialUpdateApiResponse,
        EmissionPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/emission/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedEmission,
        }),
        invalidatesTags: ["emission"],
      }),
      emissionDestroy: build.mutation<
        EmissionDestroyApiResponse,
        EmissionDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/emission/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["emission"],
      }),
      emissionBenchmarkingRetrieve: build.query<
        EmissionBenchmarkingRetrieveApiResponse,
        EmissionBenchmarkingRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/emission/benchmarking/`,
          params: { customer_id: queryArg.customerId },
        }),
        providesTags: ["emission"],
      }),
      emissionFormOptionsList: build.query<
        EmissionFormOptionsListApiResponse,
        EmissionFormOptionsListApiArg
      >({
        query: () => ({ url: `/api/emission/form_options/` }),
        providesTags: ["emission"],
      }),
      emissionScatterList: build.query<
        EmissionScatterListApiResponse,
        EmissionScatterListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/emission/scatter/`,
          params: { customer_id: queryArg.customerId },
        }),
        providesTags: ["emission"],
      }),
      entityGroupSearchList: build.query<
        EntityGroupSearchListApiResponse,
        EntityGroupSearchListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/entity-group-search/`,
          params: { match: queryArg.match },
        }),
        providesTags: ["entity-group-search"],
      }),
      entityGroupSearchGroupCustomersList: build.query<
        EntityGroupSearchGroupCustomersListApiResponse,
        EntityGroupSearchGroupCustomersListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/entity-group-search/${queryArg.pk}/group_customers/`,
          params: { group_type: queryArg.groupType },
        }),
        providesTags: ["entity-group-search"],
      }),
      eventsCreate: build.mutation<EventsCreateApiResponse, EventsCreateApiArg>(
        {
          query: (queryArg) => ({
            url: `/api/events/`,
            method: "POST",
            body: queryArg.event,
          }),
          invalidatesTags: ["events"],
        }
      ),
      eventsErrorCreate: build.mutation<
        EventsErrorCreateApiResponse,
        EventsErrorCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/events/error/`,
          method: "POST",
          body: queryArg.frontendErrorEvent,
        }),
        invalidatesTags: ["events"],
      }),
      eventsPageLoadCreate: build.mutation<
        EventsPageLoadCreateApiResponse,
        EventsPageLoadCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/events/page_load/`,
          method: "POST",
          body: queryArg.pageLoadEvent,
        }),
        invalidatesTags: ["events"],
      }),
      explorerAddressesRetrieve: build.query<
        ExplorerAddressesRetrieveApiResponse,
        ExplorerAddressesRetrieveApiArg
      >({
        query: () => ({ url: `/api/explorer/addresses/` }),
        providesTags: ["explorer"],
      }),
      explorerTitlesList: build.query<
        ExplorerTitlesListApiResponse,
        ExplorerTitlesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/explorer/titles`,
          params: {
            bounds: queryArg.bounds,
            min_area: queryArg.minArea,
            zoom: queryArg.zoom,
          },
        }),
        providesTags: ["explorer"],
      }),
      explorerTlaListingsRetrieve: build.query<
        ExplorerTlaListingsRetrieveApiResponse,
        ExplorerTlaListingsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/explorer/tla_listings/`,
          params: { tla: queryArg.tla },
        }),
        providesTags: ["explorer"],
      }),
      explorerTlasRetrieve: build.query<
        ExplorerTlasRetrieveApiResponse,
        ExplorerTlasRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/explorer/tlas/`,
          params: { bounds: queryArg.bounds, center: queryArg.center },
        }),
        providesTags: ["explorer"],
      }),
      facilitiesUpdate: build.mutation<
        FacilitiesUpdateApiResponse,
        FacilitiesUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/facilities/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.writableLoan,
          params: { type: queryArg["type"] },
        }),
        invalidatesTags: ["customer"],
      }),
      facilitiesPartialUpdate: build.mutation<
        FacilitiesPartialUpdateApiResponse,
        FacilitiesPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/facilities/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedWritableLoan,
          params: { type: queryArg["type"] },
        }),
        invalidatesTags: ["customer"],
      }),
      frontlineSalesList: build.query<
        FrontlineSalesListApiResponse,
        FrontlineSalesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/frontline-sales/`,
          params: { ids: queryArg.ids },
        }),
        providesTags: ["frontline-sales"],
      }),
      frontlineSalesCreate: build.mutation<
        FrontlineSalesCreateApiResponse,
        FrontlineSalesCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/frontline-sales/`,
          method: "POST",
          body: queryArg.frontlineSale,
        }),
        invalidatesTags: ["frontline-sales"],
      }),
      frontlineSalesRetrieve: build.query<
        FrontlineSalesRetrieveApiResponse,
        FrontlineSalesRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/frontline-sales/${queryArg.pk}/` }),
        providesTags: ["frontline-sales"],
      }),
      frontlineSalesUpdate: build.mutation<
        FrontlineSalesUpdateApiResponse,
        FrontlineSalesUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/frontline-sales/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.frontlineSale,
        }),
        invalidatesTags: ["frontline-sales"],
      }),
      frontlineSalesPartialUpdate: build.mutation<
        FrontlineSalesPartialUpdateApiResponse,
        FrontlineSalesPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/frontline-sales/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedFrontlineSale,
        }),
        invalidatesTags: ["frontline-sales"],
      }),
      frontlineSalesDestroy: build.mutation<
        FrontlineSalesDestroyApiResponse,
        FrontlineSalesDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/frontline-sales/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["frontline-sales"],
      }),
      instructionLetterList: build.query<
        InstructionLetterListApiResponse,
        InstructionLetterListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/instruction_letter/`,
          params: { valuer_type: queryArg.valuerType },
        }),
        providesTags: ["instruction_letter"],
      }),
      instructionLetterCreate: build.mutation<
        InstructionLetterCreateApiResponse,
        InstructionLetterCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/instruction_letter/`,
          method: "POST",
          body: queryArg.readonlyInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      instructionLetterRetrieve: build.query<
        InstructionLetterRetrieveApiResponse,
        InstructionLetterRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/instruction_letter/${queryArg.pk}/`,
        }),
        providesTags: ["instruction_letter"],
      }),
      instructionLetterUpdate: build.mutation<
        InstructionLetterUpdateApiResponse,
        InstructionLetterUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/instruction_letter/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.readonlyInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      instructionLetterPartialUpdate: build.mutation<
        InstructionLetterPartialUpdateApiResponse,
        InstructionLetterPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/instruction_letter/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedReadonlyInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      instructionLetterDestroy: build.mutation<
        InstructionLetterDestroyApiResponse,
        InstructionLetterDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/instruction_letter/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      layersList: build.query<LayersListApiResponse, LayersListApiArg>({
        query: () => ({ url: `/api/layers/` }),
        providesTags: ["layers"],
      }),
      layersCreate: build.mutation<LayersCreateApiResponse, LayersCreateApiArg>(
        {
          query: (queryArg) => ({
            url: `/api/layers/`,
            method: "POST",
            body: queryArg.anzUnion,
          }),
          invalidatesTags: ["layers"],
        }
      ),
      layersRetrieve: build.query<
        LayersRetrieveApiResponse,
        LayersRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/layers/${queryArg.pk}/` }),
        providesTags: ["layers"],
      }),
      getDataLineage: build.query<
        GetDataLineageApiResponse,
        GetDataLineageApiArg
      >({
        query: () => ({ url: `/api/lineagez/` }),
        providesTags: ["lineagez"],
      }),
      locationList: build.query<LocationListApiResponse, LocationListApiArg>({
        query: () => ({ url: `/api/location/` }),
        providesTags: ["location"],
      }),
      locationCreate: build.mutation<
        LocationCreateApiResponse,
        LocationCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/location/`,
          method: "POST",
          body: queryArg.location,
        }),
        invalidatesTags: ["location"],
      }),
      locationRetrieve: build.query<
        LocationRetrieveApiResponse,
        LocationRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/location/${queryArg.pk}/` }),
        providesTags: ["location"],
      }),
      locationUpdate: build.mutation<
        LocationUpdateApiResponse,
        LocationUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/location/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.location,
        }),
        invalidatesTags: ["location"],
      }),
      locationPartialUpdate: build.mutation<
        LocationPartialUpdateApiResponse,
        LocationPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/location/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedLocation,
        }),
        invalidatesTags: ["location"],
      }),
      locationDestroy: build.mutation<
        LocationDestroyApiResponse,
        LocationDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/location/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["location"],
      }),
      locationOptionsList: build.query<
        LocationOptionsListApiResponse,
        LocationOptionsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/location/options/`,
          params: { attribute: queryArg.attribute },
        }),
        providesTags: ["location"],
      }),
      locationSearchList: build.query<
        LocationSearchListApiResponse,
        LocationSearchListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/location/search/`,
          params: { match: queryArg.match },
        }),
        providesTags: ["location"],
      }),
      lookupSolarInstallersList: build.query<
        LookupSolarInstallersListApiResponse,
        LookupSolarInstallersListApiArg
      >({
        query: () => ({ url: `/api/lookup_solar_installers/` }),
        providesTags: ["lookup_solar_installers"],
      }),
      lookupSpeciesList: build.query<
        LookupSpeciesListApiResponse,
        LookupSpeciesListApiArg
      >({
        query: () => ({ url: `/api/lookup_species/` }),
        providesTags: ["lookup_species"],
      }),
      lossModelList: build.query<LossModelListApiResponse, LossModelListApiArg>(
        {
          query: (queryArg) => ({
            url: `/api/loss_model/`,
            params: { category: queryArg.category },
          }),
          providesTags: ["loss_model"],
        }
      ),
      lossModelCreate: build.mutation<
        LossModelCreateApiResponse,
        LossModelCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/`,
          method: "POST",
          body: queryArg.lossModel,
        }),
        invalidatesTags: ["loss_model"],
      }),
      lossModelStepList: build.query<
        LossModelStepListApiResponse,
        LossModelStepListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.lossModelPk}/step/`,
        }),
        providesTags: ["loss_model"],
      }),
      lossModelStepCreate: build.mutation<
        LossModelStepCreateApiResponse,
        LossModelStepCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.lossModelPk}/step/`,
          method: "POST",
          body: queryArg.lossModelStep,
        }),
        invalidatesTags: ["loss_model"],
      }),
      lossModelStepRetrieve: build.query<
        LossModelStepRetrieveApiResponse,
        LossModelStepRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.lossModelPk}/step/${queryArg.pk}/`,
        }),
        providesTags: ["loss_model"],
      }),
      lossModelStepUpdate: build.mutation<
        LossModelStepUpdateApiResponse,
        LossModelStepUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.lossModelPk}/step/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.lossModelStep,
        }),
        invalidatesTags: ["loss_model"],
      }),
      lossModelStepPartialUpdate: build.mutation<
        LossModelStepPartialUpdateApiResponse,
        LossModelStepPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.lossModelPk}/step/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedLossModelStep,
        }),
        invalidatesTags: ["loss_model"],
      }),
      lossModelStepDestroy: build.mutation<
        LossModelStepDestroyApiResponse,
        LossModelStepDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.lossModelPk}/step/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["loss_model"],
      }),
      lossModelRetrieve: build.query<
        LossModelRetrieveApiResponse,
        LossModelRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/loss_model/${queryArg.pk}/` }),
        providesTags: ["loss_model"],
      }),
      lossModelUpdate: build.mutation<
        LossModelUpdateApiResponse,
        LossModelUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.lossModel,
        }),
        invalidatesTags: ["loss_model"],
      }),
      lossModelPartialUpdate: build.mutation<
        LossModelPartialUpdateApiResponse,
        LossModelPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedLossModel,
        }),
        invalidatesTags: ["loss_model"],
      }),
      lossModelDestroy: build.mutation<
        LossModelDestroyApiResponse,
        LossModelDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["loss_model"],
      }),
      lossModelCloneCreate: build.mutation<
        LossModelCloneCreateApiResponse,
        LossModelCloneCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/loss_model/${queryArg.pk}/clone/`,
          method: "POST",
        }),
        invalidatesTags: ["loss_model"],
      }),
      newsList: build.query<NewsListApiResponse, NewsListApiArg>({
        query: () => ({ url: `/api/news/` }),
        providesTags: ["news"],
      }),
      newsCreate: build.mutation<NewsCreateApiResponse, NewsCreateApiArg>({
        query: (queryArg) => ({
          url: `/api/news/`,
          method: "POST",
          body: queryArg.news,
        }),
        invalidatesTags: ["news"],
      }),
      newsRetrieve: build.query<NewsRetrieveApiResponse, NewsRetrieveApiArg>({
        query: (queryArg) => ({ url: `/api/news/${queryArg.pk}/` }),
        providesTags: ["news"],
      }),
      newsUpdate: build.mutation<NewsUpdateApiResponse, NewsUpdateApiArg>({
        query: (queryArg) => ({
          url: `/api/news/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.news,
        }),
        invalidatesTags: ["news"],
      }),
      newsPartialUpdate: build.mutation<
        NewsPartialUpdateApiResponse,
        NewsPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/news/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedNews,
        }),
        invalidatesTags: ["news"],
      }),
      newsDestroy: build.mutation<NewsDestroyApiResponse, NewsDestroyApiArg>({
        query: (queryArg) => ({
          url: `/api/news/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["news"],
      }),
      notificationsList: build.query<
        NotificationsListApiResponse,
        NotificationsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/notifications/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["notifications"],
      }),
      notificationsRetrieve: build.query<
        NotificationsRetrieveApiResponse,
        NotificationsRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/notifications/${queryArg.pk}/` }),
        providesTags: ["notifications"],
      }),
      notificationsDestroy: build.mutation<
        NotificationsDestroyApiResponse,
        NotificationsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/notifications/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["notifications"],
      }),
      notificationsPinnedUpdate: build.mutation<
        NotificationsPinnedUpdateApiResponse,
        NotificationsPinnedUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/notifications/${queryArg.pk}/pinned/`,
          method: "PUT",
        }),
        invalidatesTags: ["notifications"],
      }),
      notificationsClearCreate: build.mutation<
        NotificationsClearCreateApiResponse,
        NotificationsClearCreateApiArg
      >({
        query: () => ({ url: `/api/notifications/clear/`, method: "POST" }),
        invalidatesTags: ["notifications"],
      }),
      notificationsCountRetrieve: build.query<
        NotificationsCountRetrieveApiResponse,
        NotificationsCountRetrieveApiArg
      >({
        query: () => ({ url: `/api/notifications/count/` }),
        providesTags: ["notifications"],
      }),
      notificationsReadCreate: build.mutation<
        NotificationsReadCreateApiResponse,
        NotificationsReadCreateApiArg
      >({
        query: () => ({ url: `/api/notifications/read/`, method: "POST" }),
        invalidatesTags: ["notifications"],
      }),
      panelPropertyTypesList: build.query<
        PanelPropertyTypesListApiResponse,
        PanelPropertyTypesListApiArg
      >({
        query: () => ({ url: `/api/panel_property_types/` }),
        providesTags: ["panel_property_types"],
      }),
      panelPropertyTypesRetrieve: build.query<
        PanelPropertyTypesRetrieveApiResponse,
        PanelPropertyTypesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_property_types/${queryArg.pk}/`,
        }),
        providesTags: ["panel_property_types"],
      }),
      panelRuralSpecialisationsList: build.query<
        PanelRuralSpecialisationsListApiResponse,
        PanelRuralSpecialisationsListApiArg
      >({
        query: () => ({ url: `/api/panel_rural_specialisations/` }),
        providesTags: ["panel_rural_specialisations"],
      }),
      panelRuralSpecialisationsRetrieve: build.query<
        PanelRuralSpecialisationsRetrieveApiResponse,
        PanelRuralSpecialisationsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_rural_specialisations/${queryArg.pk}/`,
        }),
        providesTags: ["panel_rural_specialisations"],
      }),
      panelValuerAttachmentsRetrieve: build.query<
        PanelValuerAttachmentsRetrieveApiResponse,
        PanelValuerAttachmentsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuer_attachments/${queryArg.pk}/`,
        }),
        providesTags: ["panel_valuers"],
      }),
      panelValuerAttachmentsDestroy: build.mutation<
        PanelValuerAttachmentsDestroyApiResponse,
        PanelValuerAttachmentsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuer_attachments/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["panel_valuers"],
      }),
      panelValuerRegionsList: build.query<
        PanelValuerRegionsListApiResponse,
        PanelValuerRegionsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuer_regions/`,
          params: { valuer_type: queryArg.valuerType },
        }),
        providesTags: ["panel_valuer_regions"],
      }),
      panelValuerRegionsRetrieve: build.query<
        PanelValuerRegionsRetrieveApiResponse,
        PanelValuerRegionsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuer_regions/${queryArg.pk}/`,
        }),
        providesTags: ["panel_valuer_regions"],
      }),
      panelValuersList: build.query<
        PanelValuersListApiResponse,
        PanelValuersListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuers/`,
          params: {
            address_match: queryArg.addressMatch,
            classification: queryArg.classification,
            commercial_specialisations: queryArg.commercialSpecialisations,
            competence_regions: queryArg.competenceRegions,
            deleted: queryArg.deleted,
            keyword_match: queryArg.keywordMatch,
            name: queryArg.name,
            office_address_text: queryArg.officeAddressText,
            order_by: queryArg.orderBy,
            postal_address_text: queryArg.postalAddressText,
            rural_specialisations: queryArg.ruralSpecialisations,
            state: queryArg.state,
            valuation_firm: queryArg.valuationFirm,
            valuer_type: queryArg.valuerType,
          },
        }),
        providesTags: ["panel_valuers"],
      }),
      panelValuersCreate: build.mutation<
        PanelValuersCreateApiResponse,
        PanelValuersCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuers/`,
          method: "POST",
          body: queryArg.panelValuer,
        }),
        invalidatesTags: ["panel_valuers"],
      }),
      panelValuersRetrieve: build.query<
        PanelValuersRetrieveApiResponse,
        PanelValuersRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/panel_valuers/${queryArg.pk}/` }),
        providesTags: ["panel_valuers"],
      }),
      panelValuersUpdate: build.mutation<
        PanelValuersUpdateApiResponse,
        PanelValuersUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuers/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.panelValuer,
        }),
        invalidatesTags: ["panel_valuers"],
      }),
      panelValuersPartialUpdate: build.mutation<
        PanelValuersPartialUpdateApiResponse,
        PanelValuersPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuers/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedPanelValuer,
        }),
        invalidatesTags: ["panel_valuers"],
      }),
      panelValuersExportRetrieve: build.query<
        PanelValuersExportRetrieveApiResponse,
        PanelValuersExportRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/panel_valuers/export/`,
          params: {
            address_match: queryArg.addressMatch,
            classification: queryArg.classification,
            commercial_specialisations: queryArg.commercialSpecialisations,
            competence_regions: queryArg.competenceRegions,
            deleted: queryArg.deleted,
            keyword_match: queryArg.keywordMatch,
            name: queryArg.name,
            office_address_text: queryArg.officeAddressText,
            order_by: queryArg.orderBy,
            postal_address_text: queryArg.postalAddressText,
            rural_specialisations: queryArg.ruralSpecialisations,
            state: queryArg.state,
            valuation_firm: queryArg.valuationFirm,
            valuer_type: queryArg.valuerType,
          },
        }),
        providesTags: ["panel_valuers"],
      }),
      perilList: build.query<PerilListApiResponse, PerilListApiArg>({
        query: () => ({ url: `/api/peril/` }),
        providesTags: ["peril"],
      }),
      perilRetrieve: build.query<PerilRetrieveApiResponse, PerilRetrieveApiArg>(
        {
          query: (queryArg) => ({ url: `/api/peril/${queryArg.pk}/` }),
          providesTags: ["peril"],
        }
      ),
      perilAnzsicList: build.query<
        PerilAnzsicListApiResponse,
        PerilAnzsicListApiArg
      >({
        query: () => ({ url: `/api/peril/anzsic/` }),
        providesTags: ["peril"],
      }),
      perilCategoryStatisticsList: build.query<
        PerilCategoryStatisticsListApiResponse,
        PerilCategoryStatisticsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/category_statistics/`,
          params: {
            anz_property_class: queryArg.anzPropertyClass,
            anzsic: queryArg.anzsic,
            asset_class: queryArg.assetClass,
            asset_portfolio: queryArg.assetPortfolio,
            ccr: queryArg.ccr,
            customer_segment: queryArg.customerSegment,
            dimension: queryArg.dimension,
            loss_model: queryArg.lossModel,
            peril_type: queryArg.perilType,
            property_status: queryArg.propertyStatus,
            property_type: queryArg.propertyType,
            property_zoning: queryArg.propertyZoning,
            roof_construction: queryArg.roofConstruction,
            si: queryArg.si,
            territorial_unit_0: queryArg.territorialUnit0,
            territorial_unit_1: queryArg.territorialUnit1,
            territorial_unit_2: queryArg.territorialUnit2,
            territorial_unit_3: queryArg.territorialUnit3,
            territorial_unit_4: queryArg.territorialUnit4,
            territorial_unit_5: queryArg.territorialUnit5,
            valocity_property_class: queryArg.valocityPropertyClass,
            wall_construction: queryArg.wallConstruction,
          },
        }),
        providesTags: ["peril"],
      }),
      perilHistogramList: build.query<
        PerilHistogramListApiResponse,
        PerilHistogramListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/histogram/`,
          params: { peril_type: queryArg.perilType },
        }),
        providesTags: ["peril"],
      }),
      perilImpactedList: build.query<
        PerilImpactedListApiResponse,
        PerilImpactedListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/impacted/`,
          params: {
            anz_property_class: queryArg.anzPropertyClass,
            anzsic: queryArg.anzsic,
            asset_class: queryArg.assetClass,
            asset_portfolio: queryArg.assetPortfolio,
            ccr: queryArg.ccr,
            customer_segment: queryArg.customerSegment,
            loss_model: queryArg.lossModel,
            order_by: queryArg.orderBy,
            page: queryArg.page,
            peril_type: queryArg.perilType,
            property_status: queryArg.propertyStatus,
            property_type: queryArg.propertyType,
            property_zoning: queryArg.propertyZoning,
            roof_construction: queryArg.roofConstruction,
            si: queryArg.si,
            size: queryArg.size,
            territorial_unit_0: queryArg.territorialUnit0,
            territorial_unit_1: queryArg.territorialUnit1,
            territorial_unit_2: queryArg.territorialUnit2,
            territorial_unit_3: queryArg.territorialUnit3,
            territorial_unit_4: queryArg.territorialUnit4,
            territorial_unit_5: queryArg.territorialUnit5,
            valocity_property_class: queryArg.valocityPropertyClass,
            wall_construction: queryArg.wallConstruction,
          },
        }),
        providesTags: ["peril"],
      }),
      perilImpactedCustomersList: build.query<
        PerilImpactedCustomersListApiResponse,
        PerilImpactedCustomersListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/impacted_customers/`,
          params: {
            anz_property_class: queryArg.anzPropertyClass,
            anzsic: queryArg.anzsic,
            asset_class: queryArg.assetClass,
            asset_portfolio: queryArg.assetPortfolio,
            ccr: queryArg.ccr,
            customer_segment: queryArg.customerSegment,
            loss_model: queryArg.lossModel,
            order_by: queryArg.orderBy,
            page: queryArg.page,
            peril_type: queryArg.perilType,
            property_status: queryArg.propertyStatus,
            property_type: queryArg.propertyType,
            property_zoning: queryArg.propertyZoning,
            roof_construction: queryArg.roofConstruction,
            si: queryArg.si,
            size: queryArg.size,
            territorial_unit_0: queryArg.territorialUnit0,
            territorial_unit_1: queryArg.territorialUnit1,
            territorial_unit_2: queryArg.territorialUnit2,
            territorial_unit_3: queryArg.territorialUnit3,
            territorial_unit_4: queryArg.territorialUnit4,
            territorial_unit_5: queryArg.territorialUnit5,
            valocity_property_class: queryArg.valocityPropertyClass,
            wall_construction: queryArg.wallConstruction,
          },
        }),
        providesTags: ["peril"],
      }),
      perilLocationRetrieve: build.query<
        PerilLocationRetrieveApiResponse,
        PerilLocationRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/location/`,
          params: { location_id: queryArg.locationId },
        }),
        providesTags: ["peril"],
      }),
      perilLocationLayerList: build.query<
        PerilLocationLayerListApiResponse,
        PerilLocationLayerListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/location_layer/`,
          params: {
            anz_property_class: queryArg.anzPropertyClass,
            anzsic: queryArg.anzsic,
            asset_class: queryArg.assetClass,
            asset_portfolio: queryArg.assetPortfolio,
            ccr: queryArg.ccr,
            customer_segment: queryArg.customerSegment,
            property_status: queryArg.propertyStatus,
            property_type: queryArg.propertyType,
            property_zoning: queryArg.propertyZoning,
            roof_construction: queryArg.roofConstruction,
            si: queryArg.si,
            territorial_unit_0: queryArg.territorialUnit0,
            territorial_unit_1: queryArg.territorialUnit1,
            territorial_unit_2: queryArg.territorialUnit2,
            territorial_unit_3: queryArg.territorialUnit3,
            territorial_unit_4: queryArg.territorialUnit4,
            territorial_unit_5: queryArg.territorialUnit5,
            valocity_property_class: queryArg.valocityPropertyClass,
            wall_construction: queryArg.wallConstruction,
          },
        }),
        providesTags: ["peril"],
      }),
      perilMermaidRetrieve: build.query<
        PerilMermaidRetrieveApiResponse,
        PerilMermaidRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/mermaid/`,
          params: { group_id: queryArg.groupId },
        }),
        providesTags: ["peril"],
      }),
      perilMvtStatsRetrieve: build.query<
        PerilMvtStatsRetrieveApiResponse,
        PerilMvtStatsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/mvt_stats/`,
          params: { peril_type: queryArg.perilType },
        }),
        providesTags: ["peril"],
      }),
      perilSummaryRetrieve: build.query<
        PerilSummaryRetrieveApiResponse,
        PerilSummaryRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/peril/summary/`,
          params: {
            anz_property_class: queryArg.anzPropertyClass,
            anzsic: queryArg.anzsic,
            asset_class: queryArg.assetClass,
            asset_portfolio: queryArg.assetPortfolio,
            ccr: queryArg.ccr,
            customer_segment: queryArg.customerSegment,
            dimension: queryArg.dimension,
            loss_model: queryArg.lossModel,
            peril_type: queryArg.perilType,
            property_status: queryArg.propertyStatus,
            property_type: queryArg.propertyType,
            property_zoning: queryArg.propertyZoning,
            roof_construction: queryArg.roofConstruction,
            si: queryArg.si,
            territorial_unit_0: queryArg.territorialUnit0,
            territorial_unit_1: queryArg.territorialUnit1,
            territorial_unit_2: queryArg.territorialUnit2,
            territorial_unit_3: queryArg.territorialUnit3,
            territorial_unit_4: queryArg.territorialUnit4,
            territorial_unit_5: queryArg.territorialUnit5,
            valocity_property_class: queryArg.valocityPropertyClass,
            wall_construction: queryArg.wallConstruction,
          },
        }),
        providesTags: ["peril"],
      }),
      perilCategoryList: build.query<
        PerilCategoryListApiResponse,
        PerilCategoryListApiArg
      >({
        query: () => ({ url: `/api/peril_category/` }),
        providesTags: ["peril_category"],
      }),
      perilCategoryRetrieve: build.query<
        PerilCategoryRetrieveApiResponse,
        PerilCategoryRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/peril_category/${queryArg.pk}/` }),
        providesTags: ["peril_category"],
      }),
      perilSourceList: build.query<
        PerilSourceListApiResponse,
        PerilSourceListApiArg
      >({
        query: () => ({ url: `/api/peril_source/` }),
        providesTags: ["peril_source"],
      }),
      perilSourceRetrieve: build.query<
        PerilSourceRetrieveApiResponse,
        PerilSourceRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/peril_source/${queryArg.pk}/` }),
        providesTags: ["peril_source"],
      }),
      perilTypeList: build.query<PerilTypeListApiResponse, PerilTypeListApiArg>(
        {
          query: (queryArg) => ({
            url: `/api/peril_type/`,
            params: { category: queryArg.category },
          }),
          providesTags: ["peril_type"],
        }
      ),
      perilTypeRetrieve: build.query<
        PerilTypeRetrieveApiResponse,
        PerilTypeRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/peril_type/${queryArg.pk}/` }),
        providesTags: ["peril_type"],
      }),
      projectList: build.query<ProjectListApiResponse, ProjectListApiArg>({
        query: (queryArg) => ({
          url: `/api/project/`,
          params: {
            address_match: queryArg.addressMatch,
            asset_categories: queryArg.assetCategories,
            categories: queryArg.categories,
            creators: queryArg.creators,
            customer_match: queryArg.customerMatch,
            order_by: queryArg.orderBy,
            order_direction: queryArg.orderDirection,
            page: queryArg.page,
            size: queryArg.size,
            status: queryArg.status,
            subcategories: queryArg.subcategories,
          },
        }),
        providesTags: ["project"],
      }),
      projectCreate: build.mutation<
        ProjectCreateApiResponse,
        ProjectCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/`,
          method: "POST",
          body: queryArg.greenProject,
        }),
        invalidatesTags: ["project"],
      }),
      projectRetrieve: build.query<
        ProjectRetrieveApiResponse,
        ProjectRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/project/${queryArg.pk}/` }),
        providesTags: ["project"],
      }),
      projectUpdate: build.mutation<
        ProjectUpdateApiResponse,
        ProjectUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.greenProject,
        }),
        invalidatesTags: ["project"],
      }),
      projectPartialUpdate: build.mutation<
        ProjectPartialUpdateApiResponse,
        ProjectPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedGreenProject,
        }),
        invalidatesTags: ["project"],
      }),
      projectDestroy: build.mutation<
        ProjectDestroyApiResponse,
        ProjectDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["project"],
      }),
      projectComplianceList: build.query<
        ProjectComplianceListApiResponse,
        ProjectComplianceListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.pk}/compliance/`,
        }),
        providesTags: ["project"],
      }),
      projectHistoryList: build.query<
        ProjectHistoryListApiResponse,
        ProjectHistoryListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.pk}/history/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["project"],
      }),
      projectUploadFileCreate: build.mutation<
        ProjectUploadFileCreateApiResponse,
        ProjectUploadFileCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.pk}/upload_file/`,
          method: "POST",
          body: queryArg.upload,
        }),
        invalidatesTags: ["project"],
      }),
      projectAnnualReportingList: build.query<
        ProjectAnnualReportingListApiResponse,
        ProjectAnnualReportingListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.projectPk}/annual_reporting/`,
        }),
        providesTags: ["project"],
      }),
      projectAnnualReportingCreate: build.mutation<
        ProjectAnnualReportingCreateApiResponse,
        ProjectAnnualReportingCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.projectPk}/annual_reporting/`,
          method: "POST",
          body: queryArg.greenAnnualReporting,
        }),
        invalidatesTags: ["project"],
      }),
      projectAnnualReportingRetrieve: build.query<
        ProjectAnnualReportingRetrieveApiResponse,
        ProjectAnnualReportingRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.projectPk}/annual_reporting/${queryArg.pk}/`,
        }),
        providesTags: ["project"],
      }),
      projectAnnualReportingUpdate: build.mutation<
        ProjectAnnualReportingUpdateApiResponse,
        ProjectAnnualReportingUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.projectPk}/annual_reporting/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.greenAnnualReporting,
        }),
        invalidatesTags: ["project"],
      }),
      projectAnnualReportingPartialUpdate: build.mutation<
        ProjectAnnualReportingPartialUpdateApiResponse,
        ProjectAnnualReportingPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.projectPk}/annual_reporting/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedGreenAnnualReporting,
        }),
        invalidatesTags: ["project"],
      }),
      projectAnnualReportingDestroy: build.mutation<
        ProjectAnnualReportingDestroyApiResponse,
        ProjectAnnualReportingDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/${queryArg.projectPk}/annual_reporting/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["project"],
      }),
      addressTitleList: build.query<
        AddressTitleListApiResponse,
        AddressTitleListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/address_titles/`,
          params: { addresses: queryArg.addresses },
        }),
        providesTags: ["project"],
      }),
      projectFeaturesList: build.query<
        ProjectFeaturesListApiResponse,
        ProjectFeaturesListApiArg
      >({
        query: () => ({ url: `/api/project/features/` }),
        providesTags: ["project"],
      }),
      projectFormTemplateRetrieve: build.query<
        ProjectFormTemplateRetrieveApiResponse,
        ProjectFormTemplateRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/form_template/`,
          params: {
            asset_categories: queryArg.assetCategories,
            category_id: queryArg.categoryId,
          },
        }),
        providesTags: ["project"],
      }),
      projectImpactSummaryRetrieve: build.query<
        ProjectImpactSummaryRetrieveApiResponse,
        ProjectImpactSummaryRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/impact_summary/`,
          params: { project_id: queryArg.projectId },
        }),
        providesTags: ["project"],
      }),
      projectLoansList: build.query<
        ProjectLoansListApiResponse,
        ProjectLoansListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project/loans/`,
          params: { project_id: queryArg.projectId },
        }),
        providesTags: ["project"],
      }),
      projectApprovalsList: build.query<
        ProjectApprovalsListApiResponse,
        ProjectApprovalsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_approvals/`,
          params: { project_id: queryArg.projectId },
        }),
        providesTags: ["project_approvals"],
      }),
      projectApprovalsCreate: build.mutation<
        ProjectApprovalsCreateApiResponse,
        ProjectApprovalsCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_approvals/`,
          method: "POST",
          body: queryArg.greenProjectApproval,
        }),
        invalidatesTags: ["project_approvals"],
      }),
      projectAssetList: build.query<
        ProjectAssetListApiResponse,
        ProjectAssetListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_asset/`,
          params: { project_id: queryArg.projectId },
        }),
        providesTags: ["project_asset"],
      }),
      projectAssetCreate: build.mutation<
        ProjectAssetCreateApiResponse,
        ProjectAssetCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_asset/`,
          method: "POST",
          body: queryArg.greenAssetWritable,
        }),
        invalidatesTags: ["project_asset"],
      }),
      projectAssetRetrieve: build.query<
        ProjectAssetRetrieveApiResponse,
        ProjectAssetRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/project_asset/${queryArg.pk}/` }),
        providesTags: ["project_asset"],
      }),
      projectAssetUpdate: build.mutation<
        ProjectAssetUpdateApiResponse,
        ProjectAssetUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_asset/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.greenAssetWritable,
        }),
        invalidatesTags: ["project_asset"],
      }),
      projectAssetPartialUpdate: build.mutation<
        ProjectAssetPartialUpdateApiResponse,
        ProjectAssetPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_asset/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedGreenAssetWritable,
        }),
        invalidatesTags: ["project_asset"],
      }),
      projectAssetDestroy: build.mutation<
        ProjectAssetDestroyApiResponse,
        ProjectAssetDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_asset/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["project_asset"],
      }),
      projectAssetCategoryList: build.query<
        ProjectAssetCategoryListApiResponse,
        ProjectAssetCategoryListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_asset_category/`,
          params: {
            categories: queryArg.categories,
            subcategories: queryArg.subcategories,
          },
        }),
        providesTags: ["project_asset_category"],
      }),
      projectCategoryList: build.query<
        ProjectCategoryListApiResponse,
        ProjectCategoryListApiArg
      >({
        query: () => ({ url: `/api/project_category/` }),
        providesTags: ["project_category"],
      }),
      projectFilesList: build.query<
        ProjectFilesListApiResponse,
        ProjectFilesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_files/`,
          params: {
            file_type: queryArg.fileType,
            project_id: queryArg.projectId,
          },
        }),
        providesTags: ["project_files"],
      }),
      projectFilesRetrieve: build.query<
        ProjectFilesRetrieveApiResponse,
        ProjectFilesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_files/${queryArg.pk}/`,
          params: {
            file_type: queryArg.fileType,
            project_id: queryArg.projectId,
          },
        }),
        providesTags: ["project_files"],
      }),
      projectFilesDestroy: build.mutation<
        ProjectFilesDestroyApiResponse,
        ProjectFilesDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_files/${queryArg.pk}/`,
          method: "DELETE",
          params: {
            file_type: queryArg.fileType,
            project_id: queryArg.projectId,
          },
        }),
        invalidatesTags: ["project_files"],
      }),
      projectFormOptionList: build.query<
        ProjectFormOptionListApiResponse,
        ProjectFormOptionListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_form_option/`,
          params: {
            categories: queryArg.categories,
            field_name: queryArg.fieldName,
            subcategories: queryArg.subcategories,
          },
        }),
        providesTags: ["project_form_option"],
      }),
      projectStatsRegionalList: build.query<
        ProjectStatsRegionalListApiResponse,
        ProjectStatsRegionalListApiArg
      >({
        query: () => ({ url: `/api/project_stats/regional/` }),
        providesTags: ["project_stats"],
      }),
      projectStatsSroiList: build.query<
        ProjectStatsSroiListApiResponse,
        ProjectStatsSroiListApiArg
      >({
        query: () => ({ url: `/api/project_stats/sroi/` }),
        providesTags: ["project_stats"],
      }),
      projectStatsSummaryList: build.query<
        ProjectStatsSummaryListApiResponse,
        ProjectStatsSummaryListApiArg
      >({
        query: () => ({ url: `/api/project_stats/summary/` }),
        providesTags: ["project_stats"],
      }),
      projectSubcategoryList: build.query<
        ProjectSubcategoryListApiResponse,
        ProjectSubcategoryListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/project_subcategory/`,
          params: { category: queryArg.category },
        }),
        providesTags: ["project_subcategory"],
      }),
      resourceConsentRecordsList: build.query<
        ResourceConsentRecordsListApiResponse,
        ResourceConsentRecordsListApiArg
      >({
        query: () => ({ url: `/api/resource-consent-records/` }),
        providesTags: ["valuations", "resource-consent-records"],
      }),
      resourceConsentRecordsCreate: build.mutation<
        ResourceConsentRecordsCreateApiResponse,
        ResourceConsentRecordsCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/resource-consent-records/`,
          method: "POST",
          body: queryArg.valuationsResourceConsentRecordAttachment,
        }),
        invalidatesTags: ["valuations", "resource-consent-records"],
      }),
      resourceConsentRecordsRetrieve: build.query<
        ResourceConsentRecordsRetrieveApiResponse,
        ResourceConsentRecordsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/resource-consent-records/${queryArg.pk}/`,
        }),
        providesTags: ["valuations", "resource-consent-records"],
      }),
      resourceConsentRecordsUpdate: build.mutation<
        ResourceConsentRecordsUpdateApiResponse,
        ResourceConsentRecordsUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/resource-consent-records/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.valuationsResourceConsentRecordAttachment,
        }),
        invalidatesTags: ["valuations", "resource-consent-records"],
      }),
      resourceConsentRecordsPartialUpdate: build.mutation<
        ResourceConsentRecordsPartialUpdateApiResponse,
        ResourceConsentRecordsPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/resource-consent-records/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedValuationsResourceConsentRecordAttachment,
        }),
        invalidatesTags: ["valuations", "resource-consent-records"],
      }),
      resourceConsentRecordsDestroy: build.mutation<
        ResourceConsentRecordsDestroyApiResponse,
        ResourceConsentRecordsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/resource-consent-records/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["valuations", "resource-consent-records"],
      }),
      ruralInstructionLetterList: build.query<
        RuralInstructionLetterListApiResponse,
        RuralInstructionLetterListApiArg
      >({
        query: () => ({ url: `/api/rural_instruction_letter/` }),
        providesTags: ["instruction_letter"],
      }),
      ruralInstructionLetterCreate: build.mutation<
        RuralInstructionLetterCreateApiResponse,
        RuralInstructionLetterCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/rural_instruction_letter/`,
          method: "POST",
          body: queryArg.ruralInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      ruralInstructionLetterRetrieve: build.query<
        RuralInstructionLetterRetrieveApiResponse,
        RuralInstructionLetterRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/rural_instruction_letter/${queryArg.pk}/`,
        }),
        providesTags: ["instruction_letter"],
      }),
      ruralInstructionLetterUpdate: build.mutation<
        RuralInstructionLetterUpdateApiResponse,
        RuralInstructionLetterUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/rural_instruction_letter/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.ruralInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      ruralInstructionLetterPartialUpdate: build.mutation<
        RuralInstructionLetterPartialUpdateApiResponse,
        RuralInstructionLetterPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/rural_instruction_letter/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedRuralInstructionLetterRequest,
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      ruralInstructionLetterDestroy: build.mutation<
        RuralInstructionLetterDestroyApiResponse,
        RuralInstructionLetterDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/rural_instruction_letter/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["instruction_letter"],
      }),
      ruralInstructionLetterPdfDataRetrieve: build.query<
        RuralInstructionLetterPdfDataRetrieveApiResponse,
        RuralInstructionLetterPdfDataRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/rural_instruction_letter/${queryArg.pk}/pdf_data/`,
        }),
        providesTags: ["instruction_letter"],
      }),
      saleAttachmentsRetrieve: build.query<
        SaleAttachmentsRetrieveApiResponse,
        SaleAttachmentsRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/sale-attachments/${queryArg.pk}/` }),
        providesTags: ["frontline-sales"],
      }),
      saleAttachmentsDestroy: build.mutation<
        SaleAttachmentsDestroyApiResponse,
        SaleAttachmentsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sale-attachments/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["frontline-sales"],
      }),
      saleExistsList: build.query<
        SaleExistsListApiResponse,
        SaleExistsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sale_exists/`,
          params: {
            address_id: queryArg.addressId,
            sale_date: queryArg.saleDate,
          },
        }),
        providesTags: ["sale_exists"],
      }),
      salesList: build.query<SalesListApiResponse, SalesListApiArg>({
        query: (queryArg) => ({
          url: `/api/sales/`,
          params: {
            address_match: queryArg.addressMatch,
            average_efficient_production__gte:
              queryArg.averageEfficientProductionGte,
            average_efficient_production__lte:
              queryArg.averageEfficientProductionLte,
            bonafide: queryArg.bonafide,
            chattels_stock_other__gte: queryArg.chattelsStockOtherGte,
            chattels_stock_other__lte: queryArg.chattelsStockOtherLte,
            creator__in: queryArg.creatorIn,
            distance_from: queryArg.distanceFrom,
            effective_ha__gte: queryArg.effectiveHaGte,
            effective_ha__lte: queryArg.effectiveHaLte,
            gross_sales_price__gte: queryArg.grossSalesPriceGte,
            gross_sales_price__lte: queryArg.grossSalesPriceLte,
            improvements_value__gte: queryArg.improvementsValueGte,
            improvements_value__lte: queryArg.improvementsValueLte,
            is_ls: queryArg.isLs,
            market_circumstance__in: queryArg.marketCircumstanceIn,
            match: queryArg.match,
            notional_site_value__gte: queryArg.notionalSiteValueGte,
            notional_site_value__lte: queryArg.notionalSiteValueLte,
            order_by: queryArg.orderBy,
            page: queryArg.page,
            proceed_use__in: queryArg.proceedUseIn,
            purchaser_bank__in: queryArg.purchaserBankIn,
            region__in: queryArg.regionIn,
            sale_date__gte: queryArg.saleDateGte,
            sale_date__lte: queryArg.saleDateLte,
            size: queryArg.size,
            source__in: queryArg.sourceIn,
            status__in: queryArg.statusIn,
            tenure__in: queryArg.tenureIn,
            total_ha__gte: queryArg.totalHaGte,
            total_ha__lte: queryArg.totalHaLte,
            type: queryArg["type"],
            valuation__assigned_highest_and_best_use_types__highest_and_best_use__in:
              queryArg.valuationAssignedHighestAndBestUseTypesHighestAndBestUseIn,
            vendor_bank__in: queryArg.vendorBankIn,
            vetted: queryArg.vetted,
          },
        }),
        providesTags: ["sales", "valocity_sales"],
      }),
      salesCreate: build.mutation<SalesCreateApiResponse, SalesCreateApiArg>({
        query: (queryArg) => ({
          url: `/api/sales/`,
          method: "POST",
          body: queryArg.baseSale,
        }),
        invalidatesTags: ["sales", "valocity_sales"],
      }),
      salesRetrieve: build.query<SalesRetrieveApiResponse, SalesRetrieveApiArg>(
        {
          query: (queryArg) => ({ url: `/api/sales/${queryArg.pk}/` }),
          providesTags: ["sales", "valocity_sales"],
        }
      ),
      salesUpdate: build.mutation<SalesUpdateApiResponse, SalesUpdateApiArg>({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.baseSale,
        }),
        invalidatesTags: ["sales", "valocity_sales"],
      }),
      salesPartialUpdate: build.mutation<
        SalesPartialUpdateApiResponse,
        SalesPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedBaseSale,
        }),
        invalidatesTags: ["sales", "valocity_sales"],
      }),
      salesAddressList: build.query<
        SalesAddressListApiResponse,
        SalesAddressListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/address/`,
        }),
        providesTags: ["sales"],
      }),
      salesAddressCreate: build.mutation<
        SalesAddressCreateApiResponse,
        SalesAddressCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/address/`,
          method: "POST",
          body: queryArg.address,
        }),
        invalidatesTags: ["sales"],
      }),
      salesAddressRetrieve: build.query<
        SalesAddressRetrieveApiResponse,
        SalesAddressRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/address/${queryArg.pk}/`,
        }),
        providesTags: ["sales"],
      }),
      salesAddressUpdate: build.mutation<
        SalesAddressUpdateApiResponse,
        SalesAddressUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/address/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.address,
        }),
        invalidatesTags: ["sales"],
      }),
      salesAddressPartialUpdate: build.mutation<
        SalesAddressPartialUpdateApiResponse,
        SalesAddressPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/address/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedAddress,
        }),
        invalidatesTags: ["sales"],
      }),
      salesAddressDestroy: build.mutation<
        SalesAddressDestroyApiResponse,
        SalesAddressDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/address/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["sales"],
      }),
      salesDescriptionList: build.query<
        SalesDescriptionListApiResponse,
        SalesDescriptionListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/description/`,
        }),
        providesTags: ["sales"],
      }),
      salesDescriptionCreate: build.mutation<
        SalesDescriptionCreateApiResponse,
        SalesDescriptionCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/description/`,
          method: "POST",
          body: queryArg.salePropertyDescription,
        }),
        invalidatesTags: ["sales"],
      }),
      salesDescriptionRetrieve: build.query<
        SalesDescriptionRetrieveApiResponse,
        SalesDescriptionRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/description/${queryArg.sale}/`,
        }),
        providesTags: ["sales"],
      }),
      salesDescriptionUpdate: build.mutation<
        SalesDescriptionUpdateApiResponse,
        SalesDescriptionUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/description/${queryArg.sale}/`,
          method: "PUT",
          body: queryArg.salePropertyDescription,
        }),
        invalidatesTags: ["sales"],
      }),
      salesElevationList: build.query<
        SalesElevationListApiResponse,
        SalesElevationListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/elevation/`,
        }),
        providesTags: ["sales"],
      }),
      salesElevationCreate: build.mutation<
        SalesElevationCreateApiResponse,
        SalesElevationCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/elevation/`,
          method: "POST",
          body: queryArg.elevation,
        }),
        invalidatesTags: ["sales"],
      }),
      salesElevationRetrieve: build.query<
        SalesElevationRetrieveApiResponse,
        SalesElevationRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/elevation/${queryArg.pk}/`,
        }),
        providesTags: ["sales"],
      }),
      salesElevationUpdate: build.mutation<
        SalesElevationUpdateApiResponse,
        SalesElevationUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/elevation/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.elevation,
        }),
        invalidatesTags: ["sales"],
      }),
      salesElevationPartialUpdate: build.mutation<
        SalesElevationPartialUpdateApiResponse,
        SalesElevationPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/elevation/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedElevation,
        }),
        invalidatesTags: ["sales"],
      }),
      salesElevationDestroy: build.mutation<
        SalesElevationDestroyApiResponse,
        SalesElevationDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/elevation/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["sales"],
      }),
      salesFilesList: build.query<
        SalesFilesListApiResponse,
        SalesFilesListApiArg
      >({
        query: (queryArg) => ({ url: `/api/sales/${queryArg.salePk}/files/` }),
        providesTags: ["sales"],
      }),
      salesFilesCreate: build.mutation<
        SalesFilesCreateApiResponse,
        SalesFilesCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/files/`,
          method: "POST",
          body: queryArg.saleFile,
        }),
        invalidatesTags: ["sales"],
      }),
      salesFilesRetrieve: build.query<
        SalesFilesRetrieveApiResponse,
        SalesFilesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/files/${queryArg.pk}/`,
        }),
        providesTags: ["sales"],
      }),
      salesFilesUpdate: build.mutation<
        SalesFilesUpdateApiResponse,
        SalesFilesUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/files/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.saleFile,
        }),
        invalidatesTags: ["sales"],
      }),
      salesFilesDestroy: build.mutation<
        SalesFilesDestroyApiResponse,
        SalesFilesDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/files/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["sales"],
      }),
      salesNeighboursList: build.query<
        SalesNeighboursListApiResponse,
        SalesNeighboursListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/neighbours/`,
        }),
        providesTags: ["sales"],
      }),
      salesNeighboursCreate: build.mutation<
        SalesNeighboursCreateApiResponse,
        SalesNeighboursCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/neighbours/`,
          method: "POST",
          body: queryArg.neighbour,
        }),
        invalidatesTags: ["sales"],
      }),
      salesNeighboursRetrieve: build.query<
        SalesNeighboursRetrieveApiResponse,
        SalesNeighboursRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/neighbours/${queryArg.pk}/`,
        }),
        providesTags: ["sales"],
      }),
      salesNeighboursUpdate: build.mutation<
        SalesNeighboursUpdateApiResponse,
        SalesNeighboursUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/neighbours/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.neighbour,
        }),
        invalidatesTags: ["sales"],
      }),
      salesNeighboursPartialUpdate: build.mutation<
        SalesNeighboursPartialUpdateApiResponse,
        SalesNeighboursPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/neighbours/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedNeighbour,
        }),
        invalidatesTags: ["sales"],
      }),
      salesNeighboursDestroy: build.mutation<
        SalesNeighboursDestroyApiResponse,
        SalesNeighboursDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/neighbours/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["sales"],
      }),
      salesPdfList: build.query<SalesPdfListApiResponse, SalesPdfListApiArg>({
        query: (queryArg) => ({ url: `/api/sales/${queryArg.salePk}/pdf/` }),
        providesTags: ["sales"],
      }),
      salesPdfCreate: build.mutation<
        SalesPdfCreateApiResponse,
        SalesPdfCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/pdf/`,
          method: "POST",
          body: queryArg.salePdfData,
        }),
        invalidatesTags: ["sales"],
      }),
      salesPdfRetrieve: build.query<
        SalesPdfRetrieveApiResponse,
        SalesPdfRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/pdf/${queryArg.pk}/`,
        }),
        providesTags: ["sales"],
      }),
      salesPdfUpdate: build.mutation<
        SalesPdfUpdateApiResponse,
        SalesPdfUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/pdf/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.salePdfData,
        }),
        invalidatesTags: ["sales"],
      }),
      salesPdfPartialUpdate: build.mutation<
        SalesPdfPartialUpdateApiResponse,
        SalesPdfPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/pdf/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedSalePdfData,
        }),
        invalidatesTags: ["sales"],
      }),
      salesPdfDestroy: build.mutation<
        SalesPdfDestroyApiResponse,
        SalesPdfDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/pdf/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["sales"],
      }),
      salesSmapFamilyList: build.query<
        SalesSmapFamilyListApiResponse,
        SalesSmapFamilyListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/smap_family/`,
        }),
        providesTags: ["sales"],
      }),
      salesSmapFamilyCreate: build.mutation<
        SalesSmapFamilyCreateApiResponse,
        SalesSmapFamilyCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/smap_family/`,
          method: "POST",
          body: queryArg.smapFamily,
        }),
        invalidatesTags: ["sales"],
      }),
      salesSmapFamilyRetrieve: build.query<
        SalesSmapFamilyRetrieveApiResponse,
        SalesSmapFamilyRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/smap_family/${queryArg.pk}/`,
        }),
        providesTags: ["sales"],
      }),
      salesSmapFamilyUpdate: build.mutation<
        SalesSmapFamilyUpdateApiResponse,
        SalesSmapFamilyUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/smap_family/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.smapFamily,
        }),
        invalidatesTags: ["sales"],
      }),
      salesSmapFamilyPartialUpdate: build.mutation<
        SalesSmapFamilyPartialUpdateApiResponse,
        SalesSmapFamilyPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/smap_family/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedSmapFamily,
        }),
        invalidatesTags: ["sales"],
      }),
      salesSmapFamilyDestroy: build.mutation<
        SalesSmapFamilyDestroyApiResponse,
        SalesSmapFamilyDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/smap_family/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["sales"],
      }),
      salesTitlesList: build.query<
        SalesTitlesListApiResponse,
        SalesTitlesListApiArg
      >({
        query: (queryArg) => ({ url: `/api/sales/${queryArg.salePk}/titles/` }),
        providesTags: ["sales"],
      }),
      salesTitlesUpdate: build.mutation<
        SalesTitlesUpdateApiResponse,
        SalesTitlesUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/titles/`,
          method: "PUT",
          body: queryArg.title,
        }),
        invalidatesTags: ["sales"],
      }),
      salesTitlesRetrieve: build.query<
        SalesTitlesRetrieveApiResponse,
        SalesTitlesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/titles/${queryArg.valuation}/`,
        }),
        providesTags: ["sales"],
      }),
      salesTitlesUpdate2: build.mutation<
        SalesTitlesUpdate2ApiResponse,
        SalesTitlesUpdate2ApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/titles/${queryArg.valuation}/`,
          method: "PUT",
          body: queryArg.title,
        }),
        invalidatesTags: ["sales"],
      }),
      salesUnionList: build.query<
        SalesUnionListApiResponse,
        SalesUnionListApiArg
      >({
        query: (queryArg) => ({ url: `/api/sales/${queryArg.salePk}/union/` }),
        providesTags: ["sales"],
      }),
      salesUnionCreate: build.mutation<
        SalesUnionCreateApiResponse,
        SalesUnionCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/union/`,
          method: "POST",
          body: queryArg.anzUnion,
        }),
        invalidatesTags: ["sales"],
      }),
      salesUnionRetrieve: build.query<
        SalesUnionRetrieveApiResponse,
        SalesUnionRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/union/${queryArg.pk}/`,
        }),
        providesTags: ["sales"],
      }),
      salesUnionUpdate: build.mutation<
        SalesUnionUpdateApiResponse,
        SalesUnionUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/union/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.anzUnion,
        }),
        invalidatesTags: ["sales"],
      }),
      salesUnionPartialUpdate: build.mutation<
        SalesUnionPartialUpdateApiResponse,
        SalesUnionPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/union/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedAnzUnion,
        }),
        invalidatesTags: ["sales"],
      }),
      salesUnionDestroy: build.mutation<
        SalesUnionDestroyApiResponse,
        SalesUnionDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/sales/${queryArg.salePk}/union/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["sales"],
      }),
      salesExportRetrieve: build.query<
        SalesExportRetrieveApiResponse,
        SalesExportRetrieveApiArg
      >({
        query: () => ({ url: `/api/sales/export/` }),
        providesTags: ["sales", "valocity_sales"],
      }),
      salesBboxRetrieve: build.query<
        SalesBboxRetrieveApiResponse,
        SalesBboxRetrieveApiArg
      >({
        query: () => ({ url: `/api/sales_bbox/` }),
        providesTags: ["sales_bbox"],
      }),
      scenarioExportList: build.query<
        ScenarioExportListApiResponse,
        ScenarioExportListApiArg
      >({
        query: () => ({ url: `/api/scenario_export/` }),
        providesTags: ["scenario_export"],
      }),
      scenarioExportCreate: build.mutation<
        ScenarioExportCreateApiResponse,
        ScenarioExportCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/scenario_export/`,
          method: "POST",
          body: queryArg.riskRadarExport,
        }),
        invalidatesTags: ["scenario_export"],
      }),
      scenarioExportRetrieve: build.query<
        ScenarioExportRetrieveApiResponse,
        ScenarioExportRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/scenario_export/${queryArg.pk}/` }),
        providesTags: ["scenario_export"],
      }),
      smapSiblingsList: build.query<
        SmapSiblingsListApiResponse,
        SmapSiblingsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/smap_siblings/`,
          params: { smu: queryArg.smu },
        }),
        providesTags: ["smap_siblings"],
      }),
      smapSiblingsRetrieve: build.query<
        SmapSiblingsRetrieveApiResponse,
        SmapSiblingsRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/smap_siblings/${queryArg.pk}/` }),
        providesTags: ["smap_siblings"],
      }),
      titleList: build.query<TitleListApiResponse, TitleListApiArg>({
        query: (queryArg) => ({
          url: `/api/title/`,
          params: {
            exclude: queryArg.exclude,
            match: queryArg.match,
            page: queryArg.page,
            size: queryArg.size,
          },
        }),
        providesTags: ["title"],
      }),
      titleRetrieve: build.query<TitleRetrieveApiResponse, TitleRetrieveApiArg>(
        {
          query: (queryArg) => ({ url: `/api/title/${queryArg.pk}/` }),
          providesTags: ["title"],
        }
      ),
      selectedTitleDvrList: build.query<
        SelectedTitleDvrListApiResponse,
        SelectedTitleDvrListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title/dvrs/`,
          params: { title_ids: queryArg.titleIds },
        }),
        providesTags: ["title"],
      }),
      selectedTitleExportPdf: build.query<
        SelectedTitleExportPdfApiResponse,
        SelectedTitleExportPdfApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title/export_pdf/`,
          params: { title_ids: queryArg.titleIds },
        }),
        providesTags: ["title"],
      }),
      selectedTitleList: build.query<
        SelectedTitleListApiResponse,
        SelectedTitleListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title/list_selected/`,
          params: { title_ids: queryArg.titleIds },
        }),
        providesTags: ["title"],
      }),
      selectedTitleMemorialList: build.query<
        SelectedTitleMemorialListApiResponse,
        SelectedTitleMemorialListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title/memorials/`,
          params: { title_ids: queryArg.titleIds },
        }),
        providesTags: ["title"],
      }),
      titleRetrieveByNumberRetrieve: build.query<
        TitleRetrieveByNumberRetrieveApiResponse,
        TitleRetrieveByNumberRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title/retrieve_by_number/`,
          params: { title_number: queryArg.titleNumber },
        }),
        providesTags: ["title"],
      }),
      titleLikelyAddressList: build.query<
        TitleLikelyAddressListApiResponse,
        TitleLikelyAddressListApiArg
      >({
        query: () => ({ url: `/api/title_likely_address/` }),
        providesTags: ["title_likely_address"],
      }),
      titleLikelyAddressRetrieve: build.query<
        TitleLikelyAddressRetrieveApiResponse,
        TitleLikelyAddressRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title_likely_address/${queryArg.pk}/`,
        }),
        providesTags: ["title_likely_address"],
      }),
      titleReverseSearchList: build.query<
        TitleReverseSearchListApiResponse,
        TitleReverseSearchListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title_reverse_search/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["title_reverse_search"],
      }),
      titleReverseSearchRetrieve: build.query<
        TitleReverseSearchRetrieveApiResponse,
        TitleReverseSearchRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title_reverse_search/${queryArg.pk}/`,
        }),
        providesTags: ["title_reverse_search"],
      }),
      titleSearchList: build.query<
        TitleSearchListApiResponse,
        TitleSearchListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/title_search/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["title_search"],
      }),
      titleSearchRetrieve: build.query<
        TitleSearchRetrieveApiResponse,
        TitleSearchRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/title_search/${queryArg.pk}/` }),
        providesTags: ["title_search"],
      }),
      titlesBboxRetrieve: build.query<
        TitlesBboxRetrieveApiResponse,
        TitlesBboxRetrieveApiArg
      >({
        query: () => ({ url: `/api/titles_bbox/` }),
        providesTags: ["titles_bbox"],
      }),
      tradingGroupList: build.query<
        TradingGroupListApiResponse,
        TradingGroupListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/`,
          params: {
            match: queryArg.match,
            page: queryArg.page,
            size: queryArg.size,
          },
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupRetrieve: build.query<
        TradingGroupRetrieveApiResponse,
        TradingGroupRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/trading_group/${queryArg.pk}/` }),
        providesTags: ["trading_group"],
      }),
      tradingGroupAddressesList: build.query<
        TradingGroupAddressesListApiResponse,
        TradingGroupAddressesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.pk}/addresses/`,
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupBenchmarkList: build.query<
        TradingGroupBenchmarkListApiResponse,
        TradingGroupBenchmarkListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.pk}/benchmark/`,
          params: { measure: queryArg.measure },
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupCustomersList: build.query<
        TradingGroupCustomersListApiResponse,
        TradingGroupCustomersListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.pk}/customers/`,
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupEmissionsBenchmarkRetrieve: build.query<
        TradingGroupEmissionsBenchmarkRetrieveApiResponse,
        TradingGroupEmissionsBenchmarkRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.pk}/emissions_benchmark/`,
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupLendingList: build.query<
        TradingGroupLendingListApiResponse,
        TradingGroupLendingListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.pk}/lending/`,
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupSequestrationRetrieve: build.query<
        TradingGroupSequestrationRetrieveApiResponse,
        TradingGroupSequestrationRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.pk}/sequestration/`,
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupValuationsList: build.query<
        TradingGroupValuationsListApiResponse,
        TradingGroupValuationsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.pk}/valuations/`,
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupAddressesList2: build.query<
        TradingGroupAddressesList2ApiResponse,
        TradingGroupAddressesList2ApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.tradingGroupPk}/addresses/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupAddressesCreate: build.mutation<
        TradingGroupAddressesCreateApiResponse,
        TradingGroupAddressesCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.tradingGroupPk}/addresses/`,
          method: "POST",
          body: queryArg.tradingGroupAddress,
        }),
        invalidatesTags: ["trading_group"],
      }),
      tradingGroupAddressesRetrieve: build.query<
        TradingGroupAddressesRetrieveApiResponse,
        TradingGroupAddressesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.tradingGroupPk}/addresses/${queryArg.pk}/`,
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupAddressesUpdate: build.mutation<
        TradingGroupAddressesUpdateApiResponse,
        TradingGroupAddressesUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.tradingGroupPk}/addresses/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.tradingGroupAddress,
        }),
        invalidatesTags: ["trading_group"],
      }),
      tradingGroupAddressesPartialUpdate: build.mutation<
        TradingGroupAddressesPartialUpdateApiResponse,
        TradingGroupAddressesPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.tradingGroupPk}/addresses/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedTradingGroupAddress,
        }),
        invalidatesTags: ["trading_group"],
      }),
      tradingGroupAddressesDestroy: build.mutation<
        TradingGroupAddressesDestroyApiResponse,
        TradingGroupAddressesDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.tradingGroupPk}/addresses/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["trading_group"],
      }),
      tradingGroupPropertiesList: build.query<
        TradingGroupPropertiesListApiResponse,
        TradingGroupPropertiesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.tradingGroupPk}/properties/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupPropertiesRetrieve: build.query<
        TradingGroupPropertiesRetrieveApiResponse,
        TradingGroupPropertiesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/${queryArg.tradingGroupPk}/properties/${queryArg.pk}/`,
        }),
        providesTags: ["trading_group"],
      }),
      tradingGroupLinkAddressCreate: build.mutation<
        TradingGroupLinkAddressCreateApiResponse,
        TradingGroupLinkAddressCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group/link_address/`,
          method: "POST",
          body: queryArg.tradingGroupAddress,
        }),
        invalidatesTags: ["trading_group"],
      }),
      tradingGroupRegionList: build.query<
        TradingGroupRegionListApiResponse,
        TradingGroupRegionListApiArg
      >({
        query: () => ({ url: `/api/trading_group_region/` }),
        providesTags: ["trading_group_region"],
      }),
      tradingGroupRegionRetrieve: build.query<
        TradingGroupRegionRetrieveApiResponse,
        TradingGroupRegionRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/trading_group_region/${queryArg.pk}/`,
        }),
        providesTags: ["trading_group_region"],
      }),
      userCurrentRetrieve: build.query<
        UserCurrentRetrieveApiResponse,
        UserCurrentRetrieveApiArg
      >({
        query: () => ({ url: `/api/user/current/` }),
        providesTags: ["user"],
      }),
      userUserSettingsUpdate: build.mutation<
        UserUserSettingsUpdateApiResponse,
        UserUserSettingsUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/user/user_settings/`,
          method: "PUT",
          body: queryArg.userSettings,
        }),
      }),
      userOptionsList: build.query<
        UserOptionsListApiResponse,
        UserOptionsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/user_options/`,
          params: {
            match: queryArg.match,
            page: queryArg.page,
            size: queryArg.size,
          },
        }),
        providesTags: ["user_options"],
      }),
      usernamesList: build.query<UsernamesListApiResponse, UsernamesListApiArg>(
        {
          query: (queryArg) => ({
            url: `/api/usernames/`,
            params: { username: queryArg.username },
          }),
          providesTags: ["usernames"],
        }
      ),
      usernamesRetrieve: build.query<
        UsernamesRetrieveApiResponse,
        UsernamesRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/usernames/${queryArg.pk}/` }),
        providesTags: ["usernames"],
      }),
      validKpiMeasuresList: build.query<
        ValidKpiMeasuresListApiResponse,
        ValidKpiMeasuresListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valid_kpi_measures/`,
          params: { segment: queryArg.segment },
        }),
        providesTags: ["valid_kpi_measures"],
      }),
      validKpiMeasuresCascaderOptions: build.query<
        ValidKpiMeasuresCascaderOptionsApiResponse,
        ValidKpiMeasuresCascaderOptionsApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valid_kpi_measures/cascader_options/`,
          params: { segment: queryArg.segment },
        }),
        providesTags: ["valid_kpi_measures"],
      }),
      valocitySalesList: build.query<
        ValocitySalesListApiResponse,
        ValocitySalesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/`,
          params: {
            address_match: queryArg.addressMatch,
            effective_ha__gte: queryArg.effectiveHaGte,
            effective_ha__lte: queryArg.effectiveHaLte,
            gross_sales_price__gte: queryArg.grossSalesPriceGte,
            gross_sales_price__lte: queryArg.grossSalesPriceLte,
            improvements_value__gte: queryArg.improvementsValueGte,
            improvements_value__lte: queryArg.improvementsValueLte,
            irrelevant_date__isnull: queryArg.irrelevantDateIsnull,
            land_uses: queryArg.landUses,
            linked: queryArg.linked,
            match: queryArg.match,
            notional_site_value__gte: queryArg.notionalSiteValueGte,
            notional_site_value__lte: queryArg.notionalSiteValueLte,
            order_by: queryArg.orderBy,
            page: queryArg.page,
            region__in: queryArg.regionIn,
            sale_date__gte: queryArg.saleDateGte,
            sale_date__lte: queryArg.saleDateLte,
            size: queryArg.size,
            source__in: queryArg.sourceIn,
            total_ha__gte: queryArg.totalHaGte,
            total_ha__lte: queryArg.totalHaLte,
            value_of_stock__gte: queryArg.valueOfStockGte,
            value_of_stock__lte: queryArg.valueOfStockLte,
            vetted__isnull: queryArg.vettedIsnull,
          },
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesRetrieve: build.query<
        ValocitySalesRetrieveApiResponse,
        ValocitySalesRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/valocity_sales/${queryArg.pk}/` }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesUpdate: build.mutation<
        ValocitySalesUpdateApiResponse,
        ValocitySalesUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.valocitySaleUpdate,
        }),
        invalidatesTags: ["valocity_sales"],
      }),
      valocitySalesAddressList: build.query<
        ValocitySalesAddressListApiResponse,
        ValocitySalesAddressListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/address/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesAddressRetrieve: build.query<
        ValocitySalesAddressRetrieveApiResponse,
        ValocitySalesAddressRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/address/${queryArg.pk}/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesElevationList: build.query<
        ValocitySalesElevationListApiResponse,
        ValocitySalesElevationListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/elevation/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesElevationRetrieve: build.query<
        ValocitySalesElevationRetrieveApiResponse,
        ValocitySalesElevationRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/elevation/${queryArg.pk}/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesNeighboursList: build.query<
        ValocitySalesNeighboursListApiResponse,
        ValocitySalesNeighboursListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/neighbours/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesNeighboursRetrieve: build.query<
        ValocitySalesNeighboursRetrieveApiResponse,
        ValocitySalesNeighboursRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/neighbours/${queryArg.pk}/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesSmapFamilyList: build.query<
        ValocitySalesSmapFamilyListApiResponse,
        ValocitySalesSmapFamilyListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/smap_family/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesSmapFamilyRetrieve: build.query<
        ValocitySalesSmapFamilyRetrieveApiResponse,
        ValocitySalesSmapFamilyRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/smap_family/${queryArg.pk}/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesTitlesList: build.query<
        ValocitySalesTitlesListApiResponse,
        ValocitySalesTitlesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/titles/`,
          params: { page: queryArg.page, size: queryArg.size },
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesTitlesRetrieve: build.query<
        ValocitySalesTitlesRetrieveApiResponse,
        ValocitySalesTitlesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/titles/${queryArg.pk}/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesUnionList: build.query<
        ValocitySalesUnionListApiResponse,
        ValocitySalesUnionListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/union/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesUnionRetrieve: build.query<
        ValocitySalesUnionRetrieveApiResponse,
        ValocitySalesUnionRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valocity_sales/${queryArg.salePk}/union/${queryArg.pk}/`,
        }),
        providesTags: ["valocity_sales"],
      }),
      valocitySalesBboxRetrieve: build.query<
        ValocitySalesBboxRetrieveApiResponse,
        ValocitySalesBboxRetrieveApiArg
      >({
        query: () => ({ url: `/api/valocity_sales_bbox/` }),
        providesTags: ["valocity_sales_bbox"],
      }),
      valuationFirmsList: build.query<
        ValuationFirmsListApiResponse,
        ValuationFirmsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuation_firms/`,
          params: { valuer_type: queryArg.valuerType },
        }),
        providesTags: ["valuation_firms"],
      }),
      valuationFirmsRetrieve: build.query<
        ValuationFirmsRetrieveApiResponse,
        ValuationFirmsRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/valuation_firms/${queryArg.pk}/` }),
        providesTags: ["valuation_firms"],
      }),
      valuationSearchList: build.query<
        ValuationSearchListApiResponse,
        ValuationSearchListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuation_search/`,
          params: {
            address_match: queryArg.addressMatch,
            creators: queryArg.creators,
            current_user: queryArg.currentUser,
            highest_and_best_uses: queryArg.highestAndBestUses,
            match: queryArg.match,
            max_completed_date: queryArg.maxCompletedDate,
            max_created_date: queryArg.maxCreatedDate,
            min_completed_date: queryArg.minCompletedDate,
            min_created_date: queryArg.minCreatedDate,
            page: queryArg.page,
            size: queryArg.size,
            tla: queryArg.tla,
            valuation_state: queryArg.valuationState,
          },
        }),
        providesTags: ["valuation_search"],
      }),
      valuationSearchCreate: build.mutation<
        ValuationSearchCreateApiResponse,
        ValuationSearchCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuation_search/`,
          method: "POST",
          body: queryArg.valuationSearch,
        }),
        invalidatesTags: ["valuation_search"],
      }),
      valuationSearchRetrieve: build.query<
        ValuationSearchRetrieveApiResponse,
        ValuationSearchRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/valuation_search/${queryArg.pk}/` }),
        providesTags: ["valuation_search"],
      }),
      valuationSearchUpdate: build.mutation<
        ValuationSearchUpdateApiResponse,
        ValuationSearchUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuation_search/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.valuationSearch,
        }),
        invalidatesTags: ["valuation_search"],
      }),
      valuationSearchPartialUpdate: build.mutation<
        ValuationSearchPartialUpdateApiResponse,
        ValuationSearchPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuation_search/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedValuationSearch,
        }),
        invalidatesTags: ["valuation_search"],
      }),
      valuationSearchDestroy: build.mutation<
        ValuationSearchDestroyApiResponse,
        ValuationSearchDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuation_search/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["valuation_search"],
      }),
      valuationsList: build.query<
        ValuationsListApiResponse,
        ValuationsListApiArg
      >({
        query: () => ({ url: `/api/valuations/` }),
        providesTags: ["valuations"],
      }),
      valuationsCreate: build.mutation<
        ValuationsCreateApiResponse,
        ValuationsCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/`,
          method: "POST",
          body: queryArg.valuationsWritable,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsRetrieve: build.query<
        ValuationsRetrieveApiResponse,
        ValuationsRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/valuations/${queryArg.pk}/` }),
        providesTags: ["valuations"],
      }),
      valuationsUpdate: build.mutation<
        ValuationsUpdateApiResponse,
        ValuationsUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.valuationsWritable,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsPartialUpdate: build.mutation<
        ValuationsPartialUpdateApiResponse,
        ValuationsPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedValuationsWritable,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsDestroy: build.mutation<
        ValuationsDestroyApiResponse,
        ValuationsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsPvsSummaryRetrieve: build.query<
        ValuationsPvsSummaryRetrieveApiResponse,
        ValuationsPvsSummaryRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.pk}/pvs_summary/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsCombinedResourceConsentsList: build.query<
        ValuationsCombinedResourceConsentsListApiResponse,
        ValuationsCombinedResourceConsentsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/combined-resource-consents/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsCombinedResourceConsentsRetrieve: build.query<
        ValuationsCombinedResourceConsentsRetrieveApiResponse,
        ValuationsCombinedResourceConsentsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/combined-resource-consents/${queryArg.pk}/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsProjectedValuationSummariesList: build.query<
        ValuationsProjectedValuationSummariesListApiResponse,
        ValuationsProjectedValuationSummariesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/projected-valuation-summaries/`,
        }),
        providesTags: ["assets"],
      }),
      valuationsProjectedValuationSummariesRetrieve: build.query<
        ValuationsProjectedValuationSummariesRetrieveApiResponse,
        ValuationsProjectedValuationSummariesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/projected-valuation-summaries/${queryArg.pk}/`,
        }),
        providesTags: ["assets"],
      }),
      valuationsResourceConsentRecordsList: build.query<
        ValuationsResourceConsentRecordsListApiResponse,
        ValuationsResourceConsentRecordsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/resource-consent-records/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsCreate: build.mutation<
        ValuationsResourceConsentRecordsCreateApiResponse,
        ValuationsResourceConsentRecordsCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/resource-consent-records/`,
          method: "POST",
          body: queryArg.valuationsResourceConsentRecord,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsRetrieve: build.query<
        ValuationsResourceConsentRecordsRetrieveApiResponse,
        ValuationsResourceConsentRecordsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/resource-consent-records/${queryArg.pk}/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsUpdate: build.mutation<
        ValuationsResourceConsentRecordsUpdateApiResponse,
        ValuationsResourceConsentRecordsUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/resource-consent-records/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.valuationsResourceConsentRecord,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsPartialUpdate: build.mutation<
        ValuationsResourceConsentRecordsPartialUpdateApiResponse,
        ValuationsResourceConsentRecordsPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/resource-consent-records/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedValuationsResourceConsentRecord,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsDestroy: build.mutation<
        ValuationsResourceConsentRecordsDestroyApiResponse,
        ValuationsResourceConsentRecordsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/resource-consent-records/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsTitlesList: build.query<
        ValuationsTitlesListApiResponse,
        ValuationsTitlesListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/titles/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsTitlesCreate: build.mutation<
        ValuationsTitlesCreateApiResponse,
        ValuationsTitlesCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/titles/`,
          method: "POST",
          body: queryArg.valuationsSavedTitle,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsTitlesRetrieve: build.query<
        ValuationsTitlesRetrieveApiResponse,
        ValuationsTitlesRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/titles/${queryArg.pk}/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsTitlesUpdate: build.mutation<
        ValuationsTitlesUpdateApiResponse,
        ValuationsTitlesUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/titles/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.valuationsSavedTitle,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsTitlesPartialUpdate: build.mutation<
        ValuationsTitlesPartialUpdateApiResponse,
        ValuationsTitlesPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/titles/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedValuationsSavedTitle,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsTitlesDestroy: build.mutation<
        ValuationsTitlesDestroyApiResponse,
        ValuationsTitlesDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/${queryArg.valuationPk}/titles/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsAttachmentsList: build.query<
        ValuationsResourceConsentRecordsAttachmentsListApiResponse,
        ValuationsResourceConsentRecordsAttachmentsListApiArg
      >({
        query: () => ({
          url: `/api/valuations/resource-consent-records/attachments/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsAttachmentsCreate: build.mutation<
        ValuationsResourceConsentRecordsAttachmentsCreateApiResponse,
        ValuationsResourceConsentRecordsAttachmentsCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/resource-consent-records/attachments/`,
          method: "POST",
          body: queryArg.valuationsResourceConsentRecordAttachment,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsAttachmentsRetrieve: build.query<
        ValuationsResourceConsentRecordsAttachmentsRetrieveApiResponse,
        ValuationsResourceConsentRecordsAttachmentsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/resource-consent-records/attachments/${queryArg.pk}/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsAttachmentsUpdate: build.mutation<
        ValuationsResourceConsentRecordsAttachmentsUpdateApiResponse,
        ValuationsResourceConsentRecordsAttachmentsUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/resource-consent-records/attachments/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.valuationsResourceConsentRecordAttachment,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsAttachmentsPartialUpdate: build.mutation<
        ValuationsResourceConsentRecordsAttachmentsPartialUpdateApiResponse,
        ValuationsResourceConsentRecordsAttachmentsPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/resource-consent-records/attachments/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedValuationsResourceConsentRecordAttachment,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsResourceConsentRecordsAttachmentsDestroy: build.mutation<
        ValuationsResourceConsentRecordsAttachmentsDestroyApiResponse,
        ValuationsResourceConsentRecordsAttachmentsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/resource-consent-records/attachments/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsTitlesReviewsList: build.query<
        ValuationsTitlesReviewsListApiResponse,
        ValuationsTitlesReviewsListApiArg
      >({
        query: () => ({ url: `/api/valuations/titles/reviews/` }),
        providesTags: ["valuations"],
      }),
      valuationsTitlesReviewsCreate: build.mutation<
        ValuationsTitlesReviewsCreateApiResponse,
        ValuationsTitlesReviewsCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/titles/reviews/`,
          method: "POST",
          body: queryArg.valuationsTitleReviewWritable,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsTitlesReviewsRetrieve: build.query<
        ValuationsTitlesReviewsRetrieveApiResponse,
        ValuationsTitlesReviewsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/titles/reviews/${queryArg.pk}/`,
        }),
        providesTags: ["valuations"],
      }),
      valuationsTitlesReviewsUpdate: build.mutation<
        ValuationsTitlesReviewsUpdateApiResponse,
        ValuationsTitlesReviewsUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/titles/reviews/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.valuationsTitleReviewWritable,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsTitlesReviewsPartialUpdate: build.mutation<
        ValuationsTitlesReviewsPartialUpdateApiResponse,
        ValuationsTitlesReviewsPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/titles/reviews/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedValuationsTitleReviewWritable,
        }),
        invalidatesTags: ["valuations"],
      }),
      valuationsTitlesReviewsDestroy: build.mutation<
        ValuationsTitlesReviewsDestroyApiResponse,
        ValuationsTitlesReviewsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/valuations/titles/reviews/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["valuations"],
      }),
    }),
    overrideExisting: false,
  });
export { injectedRtkApi as enhancedApi };
export type AddressesListApiResponse = /** status 200  */ Addresses[];
export type AddressesListApiArg = void;
export type AddressesCreateApiResponse = /** status 201  */ Addresses;
export type AddressesCreateApiArg = {
  addresses: Addresses;
};
export type AddressesRetrieveApiResponse = /** status 200  */ Addresses;
export type AddressesRetrieveApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
};
export type AddressesUpdateApiResponse = /** status 200  */ Addresses;
export type AddressesUpdateApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  addresses: Addresses;
};
export type AddressesPartialUpdateApiResponse = /** status 200  */ Addresses;
export type AddressesPartialUpdateApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  patchedAddresses: PatchedAddresses;
};
export type AddressesDestroyApiResponse = unknown;
export type AddressesDestroyApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
};
export type AddressesSummaryRetrieveApiResponse =
  /** status 200  */ AddressLine;
export type AddressesSummaryRetrieveApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
};
export type AddressesSummaryListApiResponse = /** status 200  */ AddressLine[];
export type AddressesSummaryListApiArg = {
  ids?: string[];
};
export type AdminProjectsListApiResponse =
  /** status 200  */ AdminGreenProject[];
export type AdminProjectsListApiArg = void;
export type AdminProjectsRetrieveApiResponse =
  /** status 200  */ AdminGreenProject;
export type AdminProjectsRetrieveApiArg = {
  /** A unique integer value identifying this Project. */
  pk: number;
};
export type AdminSalesListApiResponse = /** status 200  */ AdminSale[];
export type AdminSalesListApiArg = void;
export type AdminSalesRetrieveApiResponse = /** status 200  */ AdminSale;
export type AdminSalesRetrieveApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
};
export type AdminSalesDeleteCreateApiResponse = /** status 200  */ AdminSale;
export type AdminSalesDeleteCreateApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  adminSale: AdminSale;
};
export type AdminTerritorialAuthorityAssignmentListApiResponse =
  /** status 200  */ AdminTerroritorialAuthorityAssignment[];
export type AdminTerritorialAuthorityAssignmentListApiArg = void;
export type AdminTerritorialAuthorityAssignmentCreateApiResponse =
  /** status 201  */ AdminTerroritorialAuthorityAssignment;
export type AdminTerritorialAuthorityAssignmentCreateApiArg = {
  adminTerroritorialAuthorityAssignment: AdminTerroritorialAuthorityAssignment;
};
export type AdminTerritorialAuthorityAssignmentRetrieveApiResponse =
  /** status 200  */ AdminTerroritorialAuthorityAssignment;
export type AdminTerritorialAuthorityAssignmentRetrieveApiArg = {
  /** A unique integer value identifying this territorial authority assignment. */
  pk: number;
};
export type AdminTerritorialAuthorityAssignmentUpdateApiResponse =
  /** status 200  */ AdminTerroritorialAuthorityAssignment;
export type AdminTerritorialAuthorityAssignmentUpdateApiArg = {
  /** A unique integer value identifying this territorial authority assignment. */
  pk: number;
  adminTerroritorialAuthorityAssignment: AdminTerroritorialAuthorityAssignment;
};
export type AdminTerritorialAuthorityAssignmentPartialUpdateApiResponse =
  /** status 200  */ AdminTerroritorialAuthorityAssignment;
export type AdminTerritorialAuthorityAssignmentPartialUpdateApiArg = {
  /** A unique integer value identifying this territorial authority assignment. */
  pk: number;
  patchedAdminTerroritorialAuthorityAssignment: PatchedAdminTerroritorialAuthorityAssignment;
};
export type AdminTerritorialAuthorityAssignmentDestroyApiResponse = unknown;
export type AdminTerritorialAuthorityAssignmentDestroyApiArg = {
  /** A unique integer value identifying this territorial authority assignment. */
  pk: number;
};
export type AdminUsageRetrieveApiResponse = /** status 200  */ Usage;
export type AdminUsageRetrieveApiArg = {
  endDate?: string;
  startDate?: string;
  timeUnit?: string;
  users?: number[];
};
export type AdminUsersListApiResponse = /** status 200  */ UserAdmin[];
export type AdminUsersListApiArg = void;
export type AlertListApiResponse = /** status 200  */ Alert[];
export type AlertListApiArg = void;
export type AlertCreateApiResponse = /** status 201  */ Alert;
export type AlertCreateApiArg = {
  alert: Alert;
};
export type AlertRetrieveApiResponse = /** status 200  */ Alert;
export type AlertRetrieveApiArg = {
  /** A unique integer value identifying this alert. */
  pk: number;
};
export type AlertUpdateApiResponse = /** status 200  */ Alert;
export type AlertUpdateApiArg = {
  /** A unique integer value identifying this alert. */
  pk: number;
  alert: Alert;
};
export type AlertPartialUpdateApiResponse = /** status 200  */ Alert;
export type AlertPartialUpdateApiArg = {
  /** A unique integer value identifying this alert. */
  pk: number;
  patchedAlert: PatchedAlert;
};
export type AlertDestroyApiResponse = unknown;
export type AlertDestroyApiArg = {
  /** A unique integer value identifying this alert. */
  pk: number;
};
export type AnzsicListApiResponse = /** status 200  */ Anzsic[];
export type AnzsicListApiArg = void;
export type AnzsicRetrieveApiResponse = /** status 200  */ Anzsic;
export type AnzsicRetrieveApiArg = {
  /** A unique integer value identifying this anzsic. */
  pk: number;
};
export type AssetPvsExportRetrieveApiResponse = unknown;
export type AssetPvsExportRetrieveApiArg = void;
export type AssetsRetrieveApiResponse = unknown;
export type AssetsRetrieveApiArg = void;
export type AssetsBulkCreateApiResponse = unknown;
export type AssetsBulkCreateApiArg = void;
export type AssetsBulkEditCreateApiResponse = unknown;
export type AssetsBulkEditCreateApiArg = void;
export type AssetsGenerateDescriptionCreateApiResponse = unknown;
export type AssetsGenerateDescriptionCreateApiArg = void;
export type AssetsRemainingGeometryCreateApiResponse = unknown;
export type AssetsRemainingGeometryCreateApiArg = void;
export type AssetsTitleApportionmentRetrieveApiResponse =
  /** status 200  */ ValuationMortgageTitleApportionment;
export type AssetsTitleApportionmentRetrieveApiArg = {
  valuationId?: string;
};
export type BoundariesListApiResponse = /** status 200  */ TerritorialUnitList;
export type BoundariesListApiArg = void;
export type BoundariesRetrieveApiResponse = /** status 200  */ TerritorialUnit;
export type BoundariesRetrieveApiArg = {
  /** A unique integer value identifying this territorial unit. */
  pk: number;
};
export type BoundariesLookupListApiResponse = /** status 200  */ SelectOption[];
export type BoundariesLookupListApiArg = void;
export type BusinessUnitsListApiResponse = /** status 200  */ SelectOption[];
export type BusinessUnitsListApiArg = {
  all?: boolean;
};
export type CcraCcraListApiResponse = /** status 200  */ Ccra[];
export type CcraCcraListApiArg = {
  customerId?: number;
};
export type CcraCcraCreateApiResponse = /** status 201  */ Ccra;
export type CcraCcraCreateApiArg = {
  ccra: Ccra;
};
export type CcraCcraRetrieveApiResponse = /** status 200  */ Ccra;
export type CcraCcraRetrieveApiArg = {
  /** A unique integer value identifying this CCRA. */
  pk: number;
};
export type CcraCcraUpdateApiResponse = /** status 200  */ Ccra;
export type CcraCcraUpdateApiArg = {
  /** A unique integer value identifying this CCRA. */
  pk: number;
  ccra: Ccra;
};
export type CcraCcraPartialUpdateApiResponse = /** status 200  */ Ccra;
export type CcraCcraPartialUpdateApiArg = {
  /** A unique integer value identifying this CCRA. */
  pk: number;
  patchedCcra: PatchedCcra;
};
export type CcraCcraDestroyApiResponse = unknown;
export type CcraCcraDestroyApiArg = {
  /** A unique integer value identifying this CCRA. */
  pk: number;
};
export type CcraCcraUploadFileCreateApiResponse = /** status 200  */ Upload;
export type CcraCcraUploadFileCreateApiArg = {
  /** A unique integer value identifying this CCRA. */
  pk: number;
  upload: Upload;
};
export type CcraCcraReportTypesListApiResponse =
  /** status 200  */ CcraEmissionsReportMetricMetadata[];
export type CcraCcraReportTypesListApiArg = void;
export type CcraFilesListApiResponse = /** status 200  */ CcraAttachment[];
export type CcraFilesListApiArg = {
  ccraId?: number;
};
export type CcraFilesRetrieveApiResponse = /** status 200  */ CcraAttachment;
export type CcraFilesRetrieveApiArg = {
  ccraId?: number;
  /** A unique integer value identifying this ccra attachment. */
  pk: number;
};
export type CcraFilesDestroyApiResponse = unknown;
export type CcraFilesDestroyApiArg = {
  ccraId?: number;
  /** A unique integer value identifying this ccra attachment. */
  pk: number;
};
export type CcraFormOptionsListApiResponse =
  /** status 200  */ CcraFormOption[];
export type CcraFormOptionsListApiArg = {
  optionType?: string;
};
export type CommercialInstructionLetterListApiResponse =
  /** status 200  */ ReadonlyInstructionLetterRequest[];
export type CommercialInstructionLetterListApiArg = void;
export type CommercialInstructionLetterCreateApiResponse =
  /** status 201  */ CommercialInstructionLetterRequest;
export type CommercialInstructionLetterCreateApiArg = {
  commercialInstructionLetterRequest: CommercialInstructionLetterRequest;
};
export type CommercialInstructionLetterRetrieveApiResponse =
  /** status 200  */ CommercialInstructionLetterRequest;
export type CommercialInstructionLetterRetrieveApiArg = {
  /** A unique value identifying this commercial instruction letter request. */
  pk: number;
};
export type CommercialInstructionLetterUpdateApiResponse =
  /** status 200  */ CommercialInstructionLetterRequest;
export type CommercialInstructionLetterUpdateApiArg = {
  /** A unique value identifying this commercial instruction letter request. */
  pk: number;
  commercialInstructionLetterRequest: CommercialInstructionLetterRequest;
};
export type CommercialInstructionLetterPartialUpdateApiResponse =
  /** status 200  */ CommercialInstructionLetterRequest;
export type CommercialInstructionLetterPartialUpdateApiArg = {
  /** A unique value identifying this commercial instruction letter request. */
  pk: number;
  patchedCommercialInstructionLetterRequest: PatchedCommercialInstructionLetterRequest;
};
export type CommercialInstructionLetterDestroyApiResponse = unknown;
export type CommercialInstructionLetterDestroyApiArg = {
  /** A unique value identifying this commercial instruction letter request. */
  pk: number;
};
export type CommercialInstructionLetterPdfDataRetrieveApiResponse =
  /** status 200  */ CommercialInstructionLetterRequestPdf;
export type CommercialInstructionLetterPdfDataRetrieveApiArg = {
  /** A unique value identifying this commercial instruction letter request. */
  pk: number;
};
export type CustomerListApiResponse = /** status 200  */ PaginatedCustomerList;
export type CustomerListApiArg = {
  customerSetCode?: string;
  format?: string;
  match?: string;
  officerCode?: string;
  /** A page number within the paginated result set. */
  page?: number;
  segment?: string;
  /** Number of results to return per page. */
  size?: number;
};
export type CustomerGroupSearchListApiResponse =
  /** status 200  */ PaginatedCustomerGroupList;
export type CustomerGroupSearchListApiArg = {
  customerSetCode?: string;
  match?: string;
  /** A page number within the paginated result set. */
  page?: number;
  segment?: string;
  /** Number of results to return per page. */
  size?: number;
};
export type CustomerGroupSearchRetrieveApiResponse =
  /** status 200  */ CustomerGroup;
export type CustomerGroupSearchRetrieveApiArg = {
  /** A unique integer value identifying this customer group. */
  pk: number;
};
export type CustomerGroupsListApiResponse =
  /** status 200  */ PaginatedCustomerGroupList;
export type CustomerGroupsListApiArg = {
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
};
export type CustomerGroupsRetrieveApiResponse =
  /** status 200  */ CustomerGroup;
export type CustomerGroupsRetrieveApiArg = {
  /** A unique integer value identifying this customer group. */
  pk: number;
};
export type CustomerGroupsLendingListApiResponse =
  /** status 200  */ Facility[];
export type CustomerGroupsLendingListApiArg = {
  /** A unique integer value identifying this customer group. */
  pk: number;
};
export type CustomerSearchListApiResponse =
  /** status 200  */ PaginatedCustomerList;
export type CustomerSearchListApiArg = {
  customerSetCode?: string;
  ids?: string;
  match?: string;
  /** A page number within the paginated result set. */
  page?: number;
  segment?: string;
  /** Number of results to return per page. */
  size?: number;
};
export type CustomerDvrListApiResponse =
  /** status 200  */ BaseDistrictValuationRoll[];
export type CustomerDvrListApiArg = {
  /** A unique value identifying this customer. */
  customerPk: number;
};
export type CustomerDvrAddApiResponse =
  /** status 200  */ CustomerDistrictValuationRoll;
export type CustomerDvrAddApiArg = {
  /** A unique value identifying this customer. */
  customerPk: number;
  customerDistrictValuationRoll: CustomerDistrictValuationRoll;
};
export type CustomerDvrRemoveApiResponse =
  /** status 200  */ CustomerDistrictValuationRoll;
export type CustomerDvrRemoveApiArg = {
  /** A unique value identifying this customer. */
  customerPk: number;
  customerDistrictValuationRoll: CustomerDistrictValuationRoll;
};
export type CustomerDvrSetApiResponse =
  /** status 200  */ CustomerDistrictValuationRoll;
export type CustomerDvrSetApiArg = {
  /** A unique value identifying this customer. */
  customerPk: number;
  customerDistrictValuationRoll: CustomerDistrictValuationRoll;
};
export type CustomerKpiListApiResponse = /** status 200  */ Kpi[];
export type CustomerKpiListApiArg = {
  customerPk: number;
};
export type CustomerKpiBenchmarkListApiResponse =
  /** status 200  */ Benchmark[];
export type CustomerKpiBenchmarkListApiArg = {
  anzsic?: string;
  customerPk: number;
  denominator?: string;
  measure?: string;
  region?: number;
};
export type CustomerRetrieveApiResponse = /** status 200  */ Customer;
export type CustomerRetrieveApiArg = {
  /** A unique value identifying this customer. */
  pk: number;
};
export type CustomerBalancesListApiResponse =
  /** status 200  */ CustomerBalance[];
export type CustomerBalancesListApiArg = {
  /** A unique value identifying this customer. */
  pk: number;
};
export type CustomerEmissionsRetrieveApiResponse = /** status 200  */ Emission;
export type CustomerEmissionsRetrieveApiArg = {
  /** A unique value identifying this customer. */
  pk: number;
};
export type CustomerGroupRetrieveApiResponse = /** status 200  */ CustomerGroup;
export type CustomerGroupRetrieveApiArg = {
  /** A unique value identifying this customer. */
  pk: number;
};
export type CustomerGroupLendingListApiResponse = /** status 200  */ Facility[];
export type CustomerGroupLendingListApiArg = {
  /** A unique value identifying this customer. */
  pk: number;
};
export type CustomerLendingListApiResponse = /** status 200  */ Facility[];
export type CustomerLendingListApiArg = {
  /** A unique value identifying this customer. */
  pk: number;
};
export type CustomerProxyEmissionsListApiResponse =
  /** status 200  */ TradingGroupEmission[];
export type CustomerProxyEmissionsListApiArg = {
  /** A unique value identifying this customer. */
  pk: number;
};
export type CustomerValuationsListApiResponse = /** status 200  */ Valuation[];
export type CustomerValuationsListApiArg = {
  /** A unique value identifying this customer. */
  pk: number;
};
export type CustomerGroupListApiResponse =
  /** status 200  */ PaginatedCustomerGroupList;
export type CustomerGroupListApiArg = {
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
};
export type CustomerGroupRetrieve2ApiResponse =
  /** status 200  */ CustomerGroup;
export type CustomerGroupRetrieve2ApiArg = {
  /** A unique integer value identifying this customer group. */
  pk: number;
};
export type CustomerGroupLendingList2ApiResponse =
  /** status 200  */ Facility[];
export type CustomerGroupLendingList2ApiArg = {
  /** A unique integer value identifying this customer group. */
  pk: number;
};
export type CustomerReportTemplateListApiResponse =
  /** status 200  */ CustomerReportTemplate[];
export type CustomerReportTemplateListApiArg = {
  segment?: string;
};
export type CustomerReportTemplateCreateApiResponse =
  /** status 201  */ CustomerReportTemplate;
export type CustomerReportTemplateCreateApiArg = {
  customerReportTemplate: CustomerReportTemplate;
};
export type CustomerReportTemplateRetrieveApiResponse =
  /** status 200  */ CustomerReportTemplate;
export type CustomerReportTemplateRetrieveApiArg = {
  /** A unique integer value identifying this customer report template. */
  pk: number;
};
export type CustomerReportTemplateUpdateApiResponse =
  /** status 200  */ CustomerReportTemplate;
export type CustomerReportTemplateUpdateApiArg = {
  /** A unique integer value identifying this customer report template. */
  pk: number;
  customerReportTemplate: CustomerReportTemplate;
};
export type CustomerReportTemplatePartialUpdateApiResponse =
  /** status 200  */ CustomerReportTemplate;
export type CustomerReportTemplatePartialUpdateApiArg = {
  /** A unique integer value identifying this customer report template. */
  pk: number;
  patchedCustomerReportTemplate: PatchedCustomerReportTemplate;
};
export type CustomerReportTemplateDestroyApiResponse = unknown;
export type CustomerReportTemplateDestroyApiArg = {
  /** A unique integer value identifying this customer report template. */
  pk: number;
};
export type DataSchemaApiResponse = /** status 200  */ ModelDefinition[];
export type DataSchemaApiArg = void;
export type FormTemplateApiResponse = /** status 200  */ FormField[];
export type FormTemplateApiArg = {
  model?: string;
};
export type DvrListApiResponse = /** status 200  */ BaseDistrictValuationRoll[];
export type DvrListApiArg = {
  match?: string;
  page?: number;
};
export type EmissionListApiResponse = /** status 200  */ Emission[];
export type EmissionListApiArg = void;
export type EmissionCreateApiResponse = /** status 201  */ Emission;
export type EmissionCreateApiArg = {
  emission: Emission;
};
export type EmissionRetrieveApiResponse = /** status 200  */ Emission;
export type EmissionRetrieveApiArg = {
  /** A unique integer value identifying this emission. */
  pk: number;
};
export type EmissionUpdateApiResponse = /** status 200  */ Emission;
export type EmissionUpdateApiArg = {
  /** A unique integer value identifying this emission. */
  pk: number;
  emission: Emission;
};
export type EmissionPartialUpdateApiResponse = /** status 200  */ Emission;
export type EmissionPartialUpdateApiArg = {
  /** A unique integer value identifying this emission. */
  pk: number;
  patchedEmission: PatchedEmission;
};
export type EmissionDestroyApiResponse = unknown;
export type EmissionDestroyApiArg = {
  /** A unique integer value identifying this emission. */
  pk: number;
};
export type EmissionBenchmarkingRetrieveApiResponse =
  /** status 200  */ EmissionPercentile;
export type EmissionBenchmarkingRetrieveApiArg = {
  customerId?: number;
};
export type EmissionFormOptionsListApiResponse =
  /** status 200  */ EmissionFormField[];
export type EmissionFormOptionsListApiArg = void;
export type EmissionScatterListApiResponse =
  /** status 200  */ PartialEmission[];
export type EmissionScatterListApiArg = {
  customerId?: number;
};
export type EntityGroupSearchListApiResponse = /** status 200  */ EntityGroup[];
export type EntityGroupSearchListApiArg = {
  match?: string;
};
export type EntityGroupSearchGroupCustomersListApiResponse =
  /** status 200  */ Customer[];
export type EntityGroupSearchGroupCustomersListApiArg = {
  groupType?: string;
  pk: string;
};
export type EventsCreateApiResponse = /** status 201  */ Event;
export type EventsCreateApiArg = {
  event: Event;
};
export type EventsErrorCreateApiResponse = /** status 200  */ Event;
export type EventsErrorCreateApiArg = {
  frontendErrorEvent: FrontendErrorEvent;
};
export type EventsPageLoadCreateApiResponse = /** status 200  */ Event;
export type EventsPageLoadCreateApiArg = {
  pageLoadEvent: PageLoadEvent;
};
export type ExplorerAddressesRetrieveApiResponse = unknown;
export type ExplorerAddressesRetrieveApiArg = void;
export type ExplorerTitlesListApiResponse =
  /** status 200  */ MapTitleFeatureList;
export type ExplorerTitlesListApiArg = {
  bounds: string;
  minArea?: number;
  zoom?: number;
};
export type ExplorerTlaListingsRetrieveApiResponse = unknown;
export type ExplorerTlaListingsRetrieveApiArg = {
  tla?: string;
};
export type ExplorerTlasRetrieveApiResponse = unknown;
export type ExplorerTlasRetrieveApiArg = {
  bounds: string;
  center: string;
};
export type FacilitiesUpdateApiResponse = /** status 200  */ WritableLoan;
export type FacilitiesUpdateApiArg = {
  /** A unique value identifying this loan. */
  pk: number;
  type?: "loan" | "odlimit";
  writableLoan: WritableLoan;
};
export type FacilitiesPartialUpdateApiResponse =
  /** status 200  */ WritableLoan;
export type FacilitiesPartialUpdateApiArg = {
  /** A unique value identifying this loan. */
  pk: number;
  type?: "loan" | "odlimit";
  patchedWritableLoan: PatchedWritableLoan;
};
export type FrontlineSalesListApiResponse = /** status 200  */ FrontlineSale[];
export type FrontlineSalesListApiArg = {
  ids?: string;
};
export type FrontlineSalesCreateApiResponse = /** status 201  */ FrontlineSale;
export type FrontlineSalesCreateApiArg = {
  frontlineSale: FrontlineSale;
};
export type FrontlineSalesRetrieveApiResponse =
  /** status 200  */ FrontlineSale;
export type FrontlineSalesRetrieveApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
};
export type FrontlineSalesUpdateApiResponse = /** status 200  */ FrontlineSale;
export type FrontlineSalesUpdateApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  frontlineSale: FrontlineSale;
};
export type FrontlineSalesPartialUpdateApiResponse =
  /** status 200  */ FrontlineSale;
export type FrontlineSalesPartialUpdateApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  patchedFrontlineSale: PatchedFrontlineSale;
};
export type FrontlineSalesDestroyApiResponse = unknown;
export type FrontlineSalesDestroyApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
};
export type InstructionLetterListApiResponse =
  /** status 200  */ ReadonlyInstructionLetterRequest[];
export type InstructionLetterListApiArg = {
  /** * `commercial` - Commercial
   * `rural` - Rural */
  valuerType?: "commercial" | "rural";
};
export type InstructionLetterCreateApiResponse =
  /** status 201  */ ReadonlyInstructionLetterRequest;
export type InstructionLetterCreateApiArg = {
  readonlyInstructionLetterRequest: ReadonlyInstructionLetterRequest;
};
export type InstructionLetterRetrieveApiResponse =
  /** status 200  */ ReadonlyInstructionLetterRequest;
export type InstructionLetterRetrieveApiArg = {
  /** A unique integer value identifying this instruction letter request. */
  pk: number;
};
export type InstructionLetterUpdateApiResponse =
  /** status 200  */ ReadonlyInstructionLetterRequest;
export type InstructionLetterUpdateApiArg = {
  /** A unique integer value identifying this instruction letter request. */
  pk: number;
  readonlyInstructionLetterRequest: ReadonlyInstructionLetterRequest;
};
export type InstructionLetterPartialUpdateApiResponse =
  /** status 200  */ ReadonlyInstructionLetterRequest;
export type InstructionLetterPartialUpdateApiArg = {
  /** A unique integer value identifying this instruction letter request. */
  pk: number;
  patchedReadonlyInstructionLetterRequest: PatchedReadonlyInstructionLetterRequest;
};
export type InstructionLetterDestroyApiResponse = unknown;
export type InstructionLetterDestroyApiArg = {
  /** A unique integer value identifying this instruction letter request. */
  pk: number;
};
export type LayersListApiResponse = /** status 200  */ AnzUnionList;
export type LayersListApiArg = void;
export type LayersCreateApiResponse = /** status 201  */ AnzUnion;
export type LayersCreateApiArg = {
  anzUnion: AnzUnion;
};
export type LayersRetrieveApiResponse = /** status 200  */ AnzUnion;
export type LayersRetrieveApiArg = {
  pk: string;
};
export type GetDataLineageApiResponse = /** status 200  */ DataLineage[];
export type GetDataLineageApiArg = void;
export type LocationListApiResponse = /** status 200  */ Location[];
export type LocationListApiArg = void;
export type LocationCreateApiResponse = /** status 201  */ Location;
export type LocationCreateApiArg = {
  location: Location;
};
export type LocationRetrieveApiResponse = /** status 200  */ Location;
export type LocationRetrieveApiArg = {
  pk: number;
};
export type LocationUpdateApiResponse = /** status 200  */ Location;
export type LocationUpdateApiArg = {
  pk: number;
  location: Location;
};
export type LocationPartialUpdateApiResponse = /** status 200  */ Location;
export type LocationPartialUpdateApiArg = {
  pk: number;
  patchedLocation: PatchedLocation;
};
export type LocationDestroyApiResponse = unknown;
export type LocationDestroyApiArg = {
  pk: number;
};
export type LocationOptionsListApiResponse =
  /** status 200  */ LocationAttribute[];
export type LocationOptionsListApiArg = {
  attribute?: string;
};
export type LocationSearchListApiResponse = /** status 200  */ LocationSearch[];
export type LocationSearchListApiArg = {
  match?: string;
};
export type LookupSolarInstallersListApiResponse =
  /** status 200  */ SolarInstaller[];
export type LookupSolarInstallersListApiArg = void;
export type LookupSpeciesListApiResponse = /** status 200  */ CarbonAssetType[];
export type LookupSpeciesListApiArg = void;
export type LossModelListApiResponse = /** status 200  */ LossModel[];
export type LossModelListApiArg = {
  category?: number;
};
export type LossModelCreateApiResponse = /** status 201  */ LossModel;
export type LossModelCreateApiArg = {
  lossModel: LossModel;
};
export type LossModelStepListApiResponse = /** status 200  */ LossModelStep[];
export type LossModelStepListApiArg = {
  lossModelPk: string;
};
export type LossModelStepCreateApiResponse = /** status 201  */ LossModelStep;
export type LossModelStepCreateApiArg = {
  lossModelPk: string;
  lossModelStep: LossModelStep;
};
export type LossModelStepRetrieveApiResponse = /** status 200  */ LossModelStep;
export type LossModelStepRetrieveApiArg = {
  lossModelPk: string;
  /** A unique integer value identifying this loss model step. */
  pk: number;
};
export type LossModelStepUpdateApiResponse = /** status 200  */ LossModelStep;
export type LossModelStepUpdateApiArg = {
  lossModelPk: string;
  /** A unique integer value identifying this loss model step. */
  pk: number;
  lossModelStep: LossModelStep;
};
export type LossModelStepPartialUpdateApiResponse =
  /** status 200  */ LossModelStep;
export type LossModelStepPartialUpdateApiArg = {
  lossModelPk: string;
  /** A unique integer value identifying this loss model step. */
  pk: number;
  patchedLossModelStep: PatchedLossModelStep;
};
export type LossModelStepDestroyApiResponse = unknown;
export type LossModelStepDestroyApiArg = {
  lossModelPk: string;
  /** A unique integer value identifying this loss model step. */
  pk: number;
};
export type LossModelRetrieveApiResponse = /** status 200  */ LossModel;
export type LossModelRetrieveApiArg = {
  /** A unique integer value identifying this loss model. */
  pk: number;
};
export type LossModelUpdateApiResponse = /** status 200  */ LossModel;
export type LossModelUpdateApiArg = {
  /** A unique integer value identifying this loss model. */
  pk: number;
  lossModel: LossModel;
};
export type LossModelPartialUpdateApiResponse = /** status 200  */ LossModel;
export type LossModelPartialUpdateApiArg = {
  /** A unique integer value identifying this loss model. */
  pk: number;
  patchedLossModel: PatchedLossModel;
};
export type LossModelDestroyApiResponse = unknown;
export type LossModelDestroyApiArg = {
  /** A unique integer value identifying this loss model. */
  pk: number;
};
export type LossModelCloneCreateApiResponse = /** status 200  */ LossModel;
export type LossModelCloneCreateApiArg = {
  /** A unique integer value identifying this loss model. */
  pk: number;
};
export type NewsListApiResponse = /** status 200  */ News[];
export type NewsListApiArg = void;
export type NewsCreateApiResponse = /** status 201  */ News;
export type NewsCreateApiArg = {
  news: News;
};
export type NewsRetrieveApiResponse = /** status 200  */ News;
export type NewsRetrieveApiArg = {
  /** A unique integer value identifying this News. */
  pk: number;
};
export type NewsUpdateApiResponse = /** status 200  */ News;
export type NewsUpdateApiArg = {
  /** A unique integer value identifying this News. */
  pk: number;
  news: News;
};
export type NewsPartialUpdateApiResponse = /** status 200  */ News;
export type NewsPartialUpdateApiArg = {
  /** A unique integer value identifying this News. */
  pk: number;
  patchedNews: PatchedNews;
};
export type NewsDestroyApiResponse = unknown;
export type NewsDestroyApiArg = {
  /** A unique integer value identifying this News. */
  pk: number;
};
export type NotificationsListApiResponse =
  /** status 200  */ PaginatedNotificationList;
export type NotificationsListApiArg = {
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
};
export type NotificationsRetrieveApiResponse = /** status 200  */ Notification;
export type NotificationsRetrieveApiArg = {
  pk: string;
};
export type NotificationsDestroyApiResponse = unknown;
export type NotificationsDestroyApiArg = {
  pk: string;
};
export type NotificationsPinnedUpdateApiResponse = unknown;
export type NotificationsPinnedUpdateApiArg = {
  pk: string;
};
export type NotificationsClearCreateApiResponse = unknown;
export type NotificationsClearCreateApiArg = void;
export type NotificationsCountRetrieveApiResponse = /** status 200  */ number;
export type NotificationsCountRetrieveApiArg = void;
export type NotificationsReadCreateApiResponse = unknown;
export type NotificationsReadCreateApiArg = void;
export type PanelPropertyTypesListApiResponse =
  /** status 200  */ PanelPropertyType[];
export type PanelPropertyTypesListApiArg = void;
export type PanelPropertyTypesRetrieveApiResponse =
  /** status 200  */ PanelPropertyType;
export type PanelPropertyTypesRetrieveApiArg = {
  /** A unique integer value identifying this panel property type. */
  pk: number;
};
export type PanelRuralSpecialisationsListApiResponse =
  /** status 200  */ PanelValuerRuralSpecialisation[];
export type PanelRuralSpecialisationsListApiArg = void;
export type PanelRuralSpecialisationsRetrieveApiResponse =
  /** status 200  */ PanelValuerRuralSpecialisation;
export type PanelRuralSpecialisationsRetrieveApiArg = {
  /** A unique integer value identifying this panel valuer rural specialisation. */
  pk: number;
};
export type PanelValuerAttachmentsRetrieveApiResponse =
  /** status 200  */ Attachment;
export type PanelValuerAttachmentsRetrieveApiArg = {
  /** A unique integer value identifying this panel valuer attachment. */
  pk: number;
};
export type PanelValuerAttachmentsDestroyApiResponse = unknown;
export type PanelValuerAttachmentsDestroyApiArg = {
  /** A unique integer value identifying this panel valuer attachment. */
  pk: number;
};
export type PanelValuerRegionsListApiResponse =
  /** status 200  */ PanelValuerRegion[];
export type PanelValuerRegionsListApiArg = {
  /** Type of the panel valuer (commercial or rural) this region relates to
    
    * `commercial` - Commercial
    * `rural` - Rural */
  valuerType?: "commercial" | "rural";
};
export type PanelValuerRegionsRetrieveApiResponse =
  /** status 200  */ PanelValuerRegion;
export type PanelValuerRegionsRetrieveApiArg = {
  /** A unique integer value identifying this panel valuer region. */
  pk: number;
};
export type PanelValuersListApiResponse = /** status 200  */ PanelValuer[];
export type PanelValuersListApiArg = {
  addressMatch?: string;
  /** Classification of the panel valuer (Enhanced Review or Unlimited)
    
    * `Enhanced Review` - Enhanced Review
    * `Unlimited` - Unlimited */
  classification?: "Enhanced Review" | "Unlimited";
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  commercialSpecialisations?: string[];
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  competenceRegions?: string[];
  deleted?: boolean;
  keywordMatch?: string;
  name?: string;
  officeAddressText?: string;
  /** Ordering
    
    * `name` - Name
    * `-name` - Name (descending)
    * `valuationFirm` - Valuationfirm
    * `-valuationFirm` - Valuationfirm (descending)
    * `region` - Region
    * `-region` - Region (descending)
    * `random` - Order by Random */
  orderBy?: (
    | "-name"
    | "-region"
    | "-valuationFirm"
    | "name"
    | "random"
    | "region"
    | "valuationFirm"
  )[];
  postalAddressText?: string;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  ruralSpecialisations?: string[];
  /** State of the panel valuer - if they are considered published for use, or just being considered for use.
    
    * `abeyance` - Abeyance
    * `draft` - Draft
    * `published` - Published */
  state?: "abeyance" | "draft" | "published";
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  valuationFirm?: string[];
  /** Type of the panel valuer (commercial or rural)
    
    * `commercial` - Commercial
    * `rural` - Rural */
  valuerType?: "commercial" | "rural";
};
export type PanelValuersCreateApiResponse = /** status 201  */ PanelValuer;
export type PanelValuersCreateApiArg = {
  panelValuer: PanelValuer;
};
export type PanelValuersRetrieveApiResponse = /** status 200  */ PanelValuer;
export type PanelValuersRetrieveApiArg = {
  /** A unique integer value identifying this panel valuer. */
  pk: number;
};
export type PanelValuersUpdateApiResponse = /** status 200  */ PanelValuer;
export type PanelValuersUpdateApiArg = {
  /** A unique integer value identifying this panel valuer. */
  pk: number;
  panelValuer: PanelValuer;
};
export type PanelValuersPartialUpdateApiResponse =
  /** status 200  */ PanelValuer;
export type PanelValuersPartialUpdateApiArg = {
  /** A unique integer value identifying this panel valuer. */
  pk: number;
  patchedPanelValuer: PatchedPanelValuer;
};
export type PanelValuersExportRetrieveApiResponse =
  /** status 200  */ PanelValuer;
export type PanelValuersExportRetrieveApiArg = {
  addressMatch?: string;
  /** Classification of the panel valuer (Enhanced Review or Unlimited)
    
    * `Enhanced Review` - Enhanced Review
    * `Unlimited` - Unlimited */
  classification?: "Enhanced Review" | "Unlimited";
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  commercialSpecialisations?: string[];
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  competenceRegions?: string[];
  deleted?: boolean;
  keywordMatch?: string;
  name?: string;
  officeAddressText?: string;
  /** Ordering
    
    * `name` - Name
    * `-name` - Name (descending)
    * `valuationFirm` - Valuationfirm
    * `-valuationFirm` - Valuationfirm (descending)
    * `region` - Region
    * `-region` - Region (descending)
    * `random` - Order by Random */
  orderBy?: (
    | "-name"
    | "-region"
    | "-valuationFirm"
    | "name"
    | "random"
    | "region"
    | "valuationFirm"
  )[];
  postalAddressText?: string;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  ruralSpecialisations?: string[];
  /** State of the panel valuer - if they are considered published for use, or just being considered for use.
    
    * `abeyance` - Abeyance
    * `draft` - Draft
    * `published` - Published */
  state?: "abeyance" | "draft" | "published";
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  valuationFirm?: string[];
  /** Type of the panel valuer (commercial or rural)
    
    * `commercial` - Commercial
    * `rural` - Rural */
  valuerType?: "commercial" | "rural";
};
export type PerilListApiResponse = /** status 200  */ PerilList;
export type PerilListApiArg = void;
export type PerilRetrieveApiResponse = /** status 200  */ Peril;
export type PerilRetrieveApiArg = {
  /** A unique integer value identifying this peril. */
  pk: number;
};
export type PerilAnzsicListApiResponse = /** status 200  */ SelectOption[];
export type PerilAnzsicListApiArg = void;
export type PerilCategoryStatisticsListApiResponse =
  /** status 200  */ CategoryStatistic[];
export type PerilCategoryStatisticsListApiArg = {
  anzPropertyClass?: string;
  anzsic?: string;
  assetClass?: string;
  assetPortfolio?: string;
  ccr?: string;
  customerSegment?: string;
  dimension?: string;
  lossModel?: number;
  perilType?: number;
  propertyStatus?: string;
  propertyType?: string;
  propertyZoning?: string;
  roofConstruction?: string;
  si?: string;
  territorialUnit0?: string;
  territorialUnit1?: string;
  territorialUnit2?: string;
  territorialUnit3?: string;
  territorialUnit4?: string;
  territorialUnit5?: string;
  valocityPropertyClass?: string;
  wallConstruction?: string;
};
export type PerilHistogramListApiResponse = /** status 200  */ Histogram[];
export type PerilHistogramListApiArg = {
  perilType?: number;
};
export type PerilImpactedListApiResponse =
  /** status 200  */ PaginatedPhysicalRiskImpactedAddressList;
export type PerilImpactedListApiArg = {
  anzPropertyClass?: string;
  anzsic?: string;
  assetClass?: string;
  assetPortfolio?: string;
  ccr?: string;
  customerSegment?: string;
  lossModel?: number;
  orderBy?: string;
  page?: number;
  perilType?: number;
  propertyStatus?: string;
  propertyType?: string;
  propertyZoning?: string;
  roofConstruction?: string;
  si?: string;
  /** Number of results to return per page. */
  size?: number;
  territorialUnit0?: string;
  territorialUnit1?: string;
  territorialUnit2?: string;
  territorialUnit3?: string;
  territorialUnit4?: string;
  territorialUnit5?: string;
  valocityPropertyClass?: string;
  wallConstruction?: string;
};
export type PerilImpactedCustomersListApiResponse =
  /** status 200  */ PaginatedRiskGroupList;
export type PerilImpactedCustomersListApiArg = {
  anzPropertyClass?: string;
  anzsic?: string;
  assetClass?: string;
  assetPortfolio?: string;
  ccr?: string;
  customerSegment?: string;
  lossModel?: number;
  orderBy?: string;
  page?: number;
  perilType?: number;
  propertyStatus?: string;
  propertyType?: string;
  propertyZoning?: string;
  roofConstruction?: string;
  si?: string;
  /** Number of results to return per page. */
  size?: number;
  territorialUnit0?: string;
  territorialUnit1?: string;
  territorialUnit2?: string;
  territorialUnit3?: string;
  territorialUnit4?: string;
  territorialUnit5?: string;
  valocityPropertyClass?: string;
  wallConstruction?: string;
};
export type PerilLocationRetrieveApiResponse = /** status 200  */ Location;
export type PerilLocationRetrieveApiArg = {
  locationId?: number;
};
export type PerilLocationLayerListApiResponse =
  /** status 200  */ BaseLocation[];
export type PerilLocationLayerListApiArg = {
  anzPropertyClass?: string;
  anzsic?: string;
  assetClass?: string;
  assetPortfolio?: string;
  ccr?: string;
  customerSegment?: string;
  propertyStatus?: string;
  propertyType?: string;
  propertyZoning?: string;
  roofConstruction?: string;
  si?: string;
  territorialUnit0?: string;
  territorialUnit1?: string;
  territorialUnit2?: string;
  territorialUnit3?: string;
  territorialUnit4?: string;
  territorialUnit5?: string;
  valocityPropertyClass?: string;
  wallConstruction?: string;
};
export type PerilMermaidRetrieveApiResponse = /** status 200  */ MermaidGraph;
export type PerilMermaidRetrieveApiArg = {
  groupId: string;
};
export type PerilMvtStatsRetrieveApiResponse =
  /** status 200  */ PerilMvtStatistics;
export type PerilMvtStatsRetrieveApiArg = {
  perilType?: number;
};
export type PerilSummaryRetrieveApiResponse =
  /** status 200  */ PhysicalRiskImpactedSummary;
export type PerilSummaryRetrieveApiArg = {
  anzPropertyClass?: string;
  anzsic?: string;
  assetClass?: string;
  assetPortfolio?: string;
  ccr?: string;
  customerSegment?: string;
  dimension?: string;
  lossModel?: number;
  perilType?: number;
  propertyStatus?: string;
  propertyType?: string;
  propertyZoning?: string;
  roofConstruction?: string;
  si?: string;
  territorialUnit0?: string;
  territorialUnit1?: string;
  territorialUnit2?: string;
  territorialUnit3?: string;
  territorialUnit4?: string;
  territorialUnit5?: string;
  valocityPropertyClass?: string;
  wallConstruction?: string;
};
export type PerilCategoryListApiResponse = /** status 200  */ PerilCategory[];
export type PerilCategoryListApiArg = void;
export type PerilCategoryRetrieveApiResponse = /** status 200  */ PerilCategory;
export type PerilCategoryRetrieveApiArg = {
  /** A unique integer value identifying this peril category. */
  pk: number;
};
export type PerilSourceListApiResponse = /** status 200  */ PerilSource[];
export type PerilSourceListApiArg = void;
export type PerilSourceRetrieveApiResponse = /** status 200  */ PerilSource;
export type PerilSourceRetrieveApiArg = {
  /** A unique integer value identifying this peril source. */
  pk: number;
};
export type PerilTypeListApiResponse = /** status 200  */ PerilType[];
export type PerilTypeListApiArg = {
  category?: number;
};
export type PerilTypeRetrieveApiResponse = /** status 200  */ PerilType;
export type PerilTypeRetrieveApiArg = {
  /** A unique integer value identifying this peril type. */
  pk: number;
};
export type ProjectListApiResponse =
  /** status 200  */ PaginatedPartialGreenProjectList;
export type ProjectListApiArg = {
  addressMatch?: string;
  assetCategories?: string[];
  categories?: string[];
  creators?: string[];
  customerMatch?: string;
  orderBy?: string;
  orderDirection?: any;
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
  status?: any;
  subcategories?: string[];
};
export type ProjectCreateApiResponse = /** status 201  */ GreenProject;
export type ProjectCreateApiArg = {
  greenProject: GreenProject;
};
export type ProjectRetrieveApiResponse = /** status 200  */ GreenProject;
export type ProjectRetrieveApiArg = {
  /** A unique integer value identifying this Project. */
  pk: number;
};
export type ProjectUpdateApiResponse = /** status 200  */ GreenProject;
export type ProjectUpdateApiArg = {
  /** A unique integer value identifying this Project. */
  pk: number;
  greenProject: GreenProject;
};
export type ProjectPartialUpdateApiResponse = /** status 200  */ GreenProject;
export type ProjectPartialUpdateApiArg = {
  /** A unique integer value identifying this Project. */
  pk: number;
  patchedGreenProject: PatchedGreenProject;
};
export type ProjectDestroyApiResponse = unknown;
export type ProjectDestroyApiArg = {
  /** A unique integer value identifying this Project. */
  pk: number;
};
export type ProjectComplianceListApiResponse = /** status 200  */ ProjectRule[];
export type ProjectComplianceListApiArg = {
  /** A unique integer value identifying this Project. */
  pk: number;
};
export type ProjectHistoryListApiResponse =
  /** status 200  */ PaginatedProjectHistoryList;
export type ProjectHistoryListApiArg = {
  page?: number;
  /** A unique integer value identifying this Project. */
  pk: number;
  /** Number of results to return per page. */
  size?: number;
};
export type ProjectUploadFileCreateApiResponse = /** status 200  */ Upload;
export type ProjectUploadFileCreateApiArg = {
  /** A unique integer value identifying this Project. */
  pk: number;
  upload: Upload;
};
export type ProjectAnnualReportingListApiResponse =
  /** status 200  */ GreenAnnualReporting[];
export type ProjectAnnualReportingListApiArg = {
  projectPk: number;
};
export type ProjectAnnualReportingCreateApiResponse =
  /** status 201  */ GreenAnnualReporting;
export type ProjectAnnualReportingCreateApiArg = {
  projectPk: number;
  greenAnnualReporting: GreenAnnualReporting;
};
export type ProjectAnnualReportingRetrieveApiResponse =
  /** status 200  */ GreenAnnualReporting;
export type ProjectAnnualReportingRetrieveApiArg = {
  /** A unique integer value identifying this green annual reporting. */
  pk: number;
  projectPk: number;
};
export type ProjectAnnualReportingUpdateApiResponse =
  /** status 200  */ GreenAnnualReporting;
export type ProjectAnnualReportingUpdateApiArg = {
  /** A unique integer value identifying this green annual reporting. */
  pk: number;
  projectPk: number;
  greenAnnualReporting: GreenAnnualReporting;
};
export type ProjectAnnualReportingPartialUpdateApiResponse =
  /** status 200  */ GreenAnnualReporting;
export type ProjectAnnualReportingPartialUpdateApiArg = {
  /** A unique integer value identifying this green annual reporting. */
  pk: number;
  projectPk: number;
  patchedGreenAnnualReporting: PatchedGreenAnnualReporting;
};
export type ProjectAnnualReportingDestroyApiResponse = unknown;
export type ProjectAnnualReportingDestroyApiArg = {
  /** A unique integer value identifying this green annual reporting. */
  pk: number;
  projectPk: number;
};
export type AddressTitleListApiResponse = /** status 200  */ TitleList;
export type AddressTitleListApiArg = {
  addresses?: number[];
};
export type ProjectFeaturesListApiResponse =
  /** status 200  */ GreenProjectFeature[];
export type ProjectFeaturesListApiArg = void;
export type ProjectFormTemplateRetrieveApiResponse =
  /** status 200  */ FormTemplate;
export type ProjectFormTemplateRetrieveApiArg = {
  assetCategories?: number[];
  categoryId?: number;
};
export type ProjectImpactSummaryRetrieveApiResponse =
  /** status 200  */ GreenProjectImpactSummary;
export type ProjectImpactSummaryRetrieveApiArg = {
  projectId?: number;
};
export type ProjectLoansListApiResponse = /** status 200  */ Loan[];
export type ProjectLoansListApiArg = {
  projectId?: number;
};
export type ProjectApprovalsListApiResponse =
  /** status 200  */ GreenProjectApproval[];
export type ProjectApprovalsListApiArg = {
  projectId?: number;
};
export type ProjectApprovalsCreateApiResponse =
  /** status 201  */ GreenProjectApproval;
export type ProjectApprovalsCreateApiArg = {
  greenProjectApproval: GreenProjectApproval;
};
export type ProjectAssetListApiResponse = /** status 200  */ GreenAssetList;
export type ProjectAssetListApiArg = {
  projectId?: number;
};
export type ProjectAssetCreateApiResponse =
  /** status 201  */ GreenAssetWritable;
export type ProjectAssetCreateApiArg = {
  greenAssetWritable: GreenAssetWritable;
};
export type ProjectAssetRetrieveApiResponse = /** status 200  */ GreenAsset;
export type ProjectAssetRetrieveApiArg = {
  /** A unique integer value identifying this Asset. */
  pk: number;
};
export type ProjectAssetUpdateApiResponse =
  /** status 200  */ GreenAssetWritable;
export type ProjectAssetUpdateApiArg = {
  /** A unique integer value identifying this Asset. */
  pk: number;
  greenAssetWritable: GreenAssetWritable;
};
export type ProjectAssetPartialUpdateApiResponse =
  /** status 200  */ GreenAssetWritable;
export type ProjectAssetPartialUpdateApiArg = {
  /** A unique integer value identifying this Asset. */
  pk: number;
  patchedGreenAssetWritable: PatchedGreenAssetWritable;
};
export type ProjectAssetDestroyApiResponse = unknown;
export type ProjectAssetDestroyApiArg = {
  /** A unique integer value identifying this Asset. */
  pk: number;
};
export type ProjectAssetCategoryListApiResponse =
  /** status 200  */ GreenAssetCategory[];
export type ProjectAssetCategoryListApiArg = {
  categories?: number[];
  subcategories?: number[];
};
export type ProjectCategoryListApiResponse =
  /** status 200  */ GreenProjectCategory[];
export type ProjectCategoryListApiArg = void;
export type ProjectFilesListApiResponse = /** status 200  */ GreenProjectFile[];
export type ProjectFilesListApiArg = {
  fileType?: string;
  projectId?: number;
};
export type ProjectFilesRetrieveApiResponse =
  /** status 200  */ GreenProjectFile;
export type ProjectFilesRetrieveApiArg = {
  fileType?: string;
  /** A unique integer value identifying this green project file. */
  pk: number;
  projectId?: number;
};
export type ProjectFilesDestroyApiResponse = unknown;
export type ProjectFilesDestroyApiArg = {
  fileType?: string;
  /** A unique integer value identifying this green project file. */
  pk: number;
  projectId?: number;
};
export type ProjectFormOptionListApiResponse =
  /** status 200  */ GreenProjectFormOption[];
export type ProjectFormOptionListApiArg = {
  categories?: number[];
  fieldName?: string;
  subcategories?: number[];
};
export type ProjectStatsRegionalListApiResponse =
  /** status 200  */ GreenProjectRegionalStatistic[];
export type ProjectStatsRegionalListApiArg = void;
export type ProjectStatsSroiListApiResponse =
  /** status 200  */ GreenProjectSroiStatistic[];
export type ProjectStatsSroiListApiArg = void;
export type ProjectStatsSummaryListApiResponse =
  /** status 200  */ GreenProjectSummary[];
export type ProjectStatsSummaryListApiArg = void;
export type ProjectSubcategoryListApiResponse =
  /** status 200  */ GreenProjectSubcategory[];
export type ProjectSubcategoryListApiArg = {
  category?: number;
};
export type ResourceConsentRecordsListApiResponse =
  /** status 200  */ ValuationsResourceConsentRecordAttachment[];
export type ResourceConsentRecordsListApiArg = void;
export type ResourceConsentRecordsCreateApiResponse =
  /** status 201  */ ValuationsResourceConsentRecordAttachment;
export type ResourceConsentRecordsCreateApiArg = {
  valuationsResourceConsentRecordAttachment: ValuationsResourceConsentRecordAttachment;
};
export type ResourceConsentRecordsRetrieveApiResponse =
  /** status 200  */ ValuationsResourceConsentRecordAttachment;
export type ResourceConsentRecordsRetrieveApiArg = {
  /** A unique integer value identifying this valuation resource consent record. */
  pk: number;
};
export type ResourceConsentRecordsUpdateApiResponse =
  /** status 200  */ ValuationsResourceConsentRecordAttachment;
export type ResourceConsentRecordsUpdateApiArg = {
  /** A unique integer value identifying this valuation resource consent record. */
  pk: number;
  valuationsResourceConsentRecordAttachment: ValuationsResourceConsentRecordAttachment;
};
export type ResourceConsentRecordsPartialUpdateApiResponse =
  /** status 200  */ ValuationsResourceConsentRecordAttachment;
export type ResourceConsentRecordsPartialUpdateApiArg = {
  /** A unique integer value identifying this valuation resource consent record. */
  pk: number;
  patchedValuationsResourceConsentRecordAttachment: PatchedValuationsResourceConsentRecordAttachment;
};
export type ResourceConsentRecordsDestroyApiResponse = unknown;
export type ResourceConsentRecordsDestroyApiArg = {
  /** A unique integer value identifying this valuation resource consent record. */
  pk: number;
};
export type RuralInstructionLetterListApiResponse =
  /** status 200  */ ReadonlyInstructionLetterRequest[];
export type RuralInstructionLetterListApiArg = void;
export type RuralInstructionLetterCreateApiResponse =
  /** status 201  */ RuralInstructionLetterRequest;
export type RuralInstructionLetterCreateApiArg = {
  ruralInstructionLetterRequest: RuralInstructionLetterRequest;
};
export type RuralInstructionLetterRetrieveApiResponse =
  /** status 200  */ RuralInstructionLetterRequest;
export type RuralInstructionLetterRetrieveApiArg = {
  /** A unique value identifying this rural instruction letter request. */
  pk: number;
};
export type RuralInstructionLetterUpdateApiResponse =
  /** status 200  */ RuralInstructionLetterRequest;
export type RuralInstructionLetterUpdateApiArg = {
  /** A unique value identifying this rural instruction letter request. */
  pk: number;
  ruralInstructionLetterRequest: RuralInstructionLetterRequest;
};
export type RuralInstructionLetterPartialUpdateApiResponse =
  /** status 200  */ RuralInstructionLetterRequest;
export type RuralInstructionLetterPartialUpdateApiArg = {
  /** A unique value identifying this rural instruction letter request. */
  pk: number;
  patchedRuralInstructionLetterRequest: PatchedRuralInstructionLetterRequest;
};
export type RuralInstructionLetterDestroyApiResponse = unknown;
export type RuralInstructionLetterDestroyApiArg = {
  /** A unique value identifying this rural instruction letter request. */
  pk: number;
};
export type RuralInstructionLetterPdfDataRetrieveApiResponse =
  /** status 200  */ RuralInstructionLetterRequestPdf;
export type RuralInstructionLetterPdfDataRetrieveApiArg = {
  /** A unique value identifying this rural instruction letter request. */
  pk: number;
};
export type SaleAttachmentsRetrieveApiResponse = /** status 200  */ Attachment;
export type SaleAttachmentsRetrieveApiArg = {
  /** A unique integer value identifying this sale attachment. */
  pk: number;
};
export type SaleAttachmentsDestroyApiResponse = unknown;
export type SaleAttachmentsDestroyApiArg = {
  /** A unique integer value identifying this sale attachment. */
  pk: number;
};
export type SaleExistsListApiResponse = /** status 200  */ RegularSale[];
export type SaleExistsListApiArg = {
  addressId?: number;
  saleDate?: string;
};
export type SalesListApiResponse = /** status 200  */ PaginatedSaleList;
export type SalesListApiArg = {
  addressMatch?: string;
  averageEfficientProductionGte?: number;
  averageEfficientProductionLte?: number;
  bonafide?: boolean;
  chattelsStockOtherGte?: number;
  chattelsStockOtherLte?: number;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  creatorIn?: string[];
  distanceFrom?: string;
  effectiveHaGte?: number;
  effectiveHaLte?: number;
  grossSalesPriceGte?: number;
  grossSalesPriceLte?: number;
  improvementsValueGte?: number;
  improvementsValueLte?: number;
  isLs?: boolean;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  marketCircumstanceIn?: string[];
  match?: string;
  notionalSiteValueGte?: number;
  notionalSiteValueLte?: number;
  /** Ordering
    
    * `fullAddress` - Fulladdress
    * `-fullAddress` - Fulladdress (descending)
    * `highestAndBestUseType` - Highestandbestusetype
    * `-highestAndBestUseType` - Highestandbestusetype (descending)
    * `region` - Region
    * `-region` - Region (descending)
    * `saleDate` - Saledate
    * `-saleDate` - Saledate (descending)
    * `averageEfficientProduction` - Averageefficientproduction
    * `-averageEfficientProduction` - Averageefficientproduction (descending)
    * `effectiveHa` - Effectiveha
    * `-effectiveHa` - Effectiveha (descending)
    * `grossSalesPrice` - Grosssalesprice
    * `-grossSalesPrice` - Grosssalesprice (descending)
    * `creatorName` - Creatorname
    * `-creatorName` - Creatorname (descending) */
  orderBy?: (
    | "-averageEfficientProduction"
    | "-creatorName"
    | "-effectiveHa"
    | "-fullAddress"
    | "-grossSalesPrice"
    | "-highestAndBestUseType"
    | "-region"
    | "-saleDate"
    | "averageEfficientProduction"
    | "creatorName"
    | "effectiveHa"
    | "fullAddress"
    | "grossSalesPrice"
    | "highestAndBestUseType"
    | "region"
    | "saleDate"
  )[];
  /** A page number within the paginated result set. */
  page?: number;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  proceedUseIn?: string[];
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  purchaserBankIn?: string[];
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  regionIn?: string[];
  saleDateGte?: string;
  saleDateLte?: string;
  /** Number of results to return per page. */
  size?: number;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  sourceIn?: string[];
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  statusIn?: string[];
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  tenureIn?: string[];
  totalHaGte?: number;
  totalHaLte?: number;
  /** * `listings` - Listings
   * `vettedSales` - Vetted Sales
   * `unvettedSales` - Unvetted Sales */
  type?: "listings" | "unvettedSales" | "vettedSales";
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  valuationAssignedHighestAndBestUseTypesHighestAndBestUseIn?: string[];
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  vendorBankIn?: string[];
  vetted?: boolean;
};
export type SalesCreateApiResponse = /** status 201  */ BaseSale;
export type SalesCreateApiArg = {
  baseSale: BaseSale;
};
export type SalesRetrieveApiResponse = /** status 200  */ Sale;
export type SalesRetrieveApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
};
export type SalesUpdateApiResponse = /** status 200  */ BaseSale;
export type SalesUpdateApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  baseSale: BaseSale;
};
export type SalesPartialUpdateApiResponse = /** status 200  */ BaseSale;
export type SalesPartialUpdateApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  patchedBaseSale: PatchedBaseSale;
};
export type SalesAddressListApiResponse = /** status 200  */ Address[];
export type SalesAddressListApiArg = {
  salePk: string;
};
export type SalesAddressCreateApiResponse = /** status 201  */ Address;
export type SalesAddressCreateApiArg = {
  salePk: string;
  address: Address;
};
export type SalesAddressRetrieveApiResponse = /** status 200  */ Address;
export type SalesAddressRetrieveApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
};
export type SalesAddressUpdateApiResponse = /** status 200  */ Address;
export type SalesAddressUpdateApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
  address: Address;
};
export type SalesAddressPartialUpdateApiResponse = /** status 200  */ Address;
export type SalesAddressPartialUpdateApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
  patchedAddress: PatchedAddress;
};
export type SalesAddressDestroyApiResponse = unknown;
export type SalesAddressDestroyApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
};
export type SalesDescriptionListApiResponse =
  /** status 200  */ SalePropertyDescription[];
export type SalesDescriptionListApiArg = {
  salePk: number;
};
export type SalesDescriptionCreateApiResponse =
  /** status 201  */ SalePropertyDescription;
export type SalesDescriptionCreateApiArg = {
  salePk: number;
  salePropertyDescription: SalePropertyDescription;
};
export type SalesDescriptionRetrieveApiResponse =
  /** status 200  */ SalePropertyDescription;
export type SalesDescriptionRetrieveApiArg = {
  sale: number;
  salePk: number;
};
export type SalesDescriptionUpdateApiResponse =
  /** status 200  */ SalePropertyDescription;
export type SalesDescriptionUpdateApiArg = {
  sale: number;
  salePk: number;
  salePropertyDescription: SalePropertyDescription;
};
export type SalesElevationListApiResponse = /** status 200  */ ElevationList;
export type SalesElevationListApiArg = {
  salePk: string;
};
export type SalesElevationCreateApiResponse = /** status 201  */ Elevation;
export type SalesElevationCreateApiArg = {
  salePk: string;
  elevation: Elevation;
};
export type SalesElevationRetrieveApiResponse = /** status 200  */ Elevation;
export type SalesElevationRetrieveApiArg = {
  /** A unique value identifying this elevation. */
  pk: number;
  salePk: string;
};
export type SalesElevationUpdateApiResponse = /** status 200  */ Elevation;
export type SalesElevationUpdateApiArg = {
  /** A unique value identifying this elevation. */
  pk: number;
  salePk: string;
  elevation: Elevation;
};
export type SalesElevationPartialUpdateApiResponse =
  /** status 200  */ Elevation;
export type SalesElevationPartialUpdateApiArg = {
  /** A unique value identifying this elevation. */
  pk: number;
  salePk: string;
  patchedElevation: PatchedElevation;
};
export type SalesElevationDestroyApiResponse = unknown;
export type SalesElevationDestroyApiArg = {
  /** A unique value identifying this elevation. */
  pk: number;
  salePk: string;
};
export type SalesFilesListApiResponse = /** status 200  */ SaleFile[];
export type SalesFilesListApiArg = {
  salePk: number;
};
export type SalesFilesCreateApiResponse = /** status 201  */ SaleFile;
export type SalesFilesCreateApiArg = {
  salePk: number;
  saleFile: SaleFile;
};
export type SalesFilesRetrieveApiResponse = /** status 200  */ SaleFile;
export type SalesFilesRetrieveApiArg = {
  pk: number;
  salePk: number;
};
export type SalesFilesUpdateApiResponse = /** status 200  */ SaleFile;
export type SalesFilesUpdateApiArg = {
  pk: number;
  salePk: number;
  saleFile: SaleFile;
};
export type SalesFilesDestroyApiResponse = unknown;
export type SalesFilesDestroyApiArg = {
  pk: number;
  salePk: number;
};
export type SalesNeighboursListApiResponse = /** status 200  */ NeighbourList;
export type SalesNeighboursListApiArg = {
  salePk: string;
};
export type SalesNeighboursCreateApiResponse = /** status 201  */ Neighbour;
export type SalesNeighboursCreateApiArg = {
  salePk: string;
  neighbour: Neighbour;
};
export type SalesNeighboursRetrieveApiResponse = /** status 200  */ Neighbour;
export type SalesNeighboursRetrieveApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
};
export type SalesNeighboursUpdateApiResponse = /** status 200  */ Neighbour;
export type SalesNeighboursUpdateApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
  neighbour: Neighbour;
};
export type SalesNeighboursPartialUpdateApiResponse =
  /** status 200  */ Neighbour;
export type SalesNeighboursPartialUpdateApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
  patchedNeighbour: PatchedNeighbour;
};
export type SalesNeighboursDestroyApiResponse = unknown;
export type SalesNeighboursDestroyApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
};
export type SalesPdfListApiResponse = /** status 200  */ SalePdfData[];
export type SalesPdfListApiArg = {
  /** A unique integer value identifying this sale. */
  salePk: number;
};
export type SalesPdfCreateApiResponse = /** status 201  */ SalePdfData;
export type SalesPdfCreateApiArg = {
  /** A unique integer value identifying this sale. */
  salePk: number;
  salePdfData: SalePdfData;
};
export type SalesPdfRetrieveApiResponse = /** status 200  */ SalePdfData;
export type SalesPdfRetrieveApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  /** A unique integer value identifying this sale. */
  salePk: number;
};
export type SalesPdfUpdateApiResponse = /** status 200  */ SalePdfData;
export type SalesPdfUpdateApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  /** A unique integer value identifying this sale. */
  salePk: number;
  salePdfData: SalePdfData;
};
export type SalesPdfPartialUpdateApiResponse = /** status 200  */ SalePdfData;
export type SalesPdfPartialUpdateApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  /** A unique integer value identifying this sale. */
  salePk: number;
  patchedSalePdfData: PatchedSalePdfData;
};
export type SalesPdfDestroyApiResponse = unknown;
export type SalesPdfDestroyApiArg = {
  /** A unique integer value identifying this sale. */
  pk: number;
  /** A unique integer value identifying this sale. */
  salePk: number;
};
export type SalesSmapFamilyListApiResponse = /** status 200  */ SmapFamilyList;
export type SalesSmapFamilyListApiArg = {
  salePk: string;
};
export type SalesSmapFamilyCreateApiResponse = /** status 201  */ SmapFamily;
export type SalesSmapFamilyCreateApiArg = {
  salePk: string;
  smapFamily: SmapFamily;
};
export type SalesSmapFamilyRetrieveApiResponse = /** status 200  */ SmapFamily;
export type SalesSmapFamilyRetrieveApiArg = {
  pk: number;
  salePk: string;
};
export type SalesSmapFamilyUpdateApiResponse = /** status 200  */ SmapFamily;
export type SalesSmapFamilyUpdateApiArg = {
  pk: number;
  salePk: string;
  smapFamily: SmapFamily;
};
export type SalesSmapFamilyPartialUpdateApiResponse =
  /** status 200  */ SmapFamily;
export type SalesSmapFamilyPartialUpdateApiArg = {
  pk: number;
  salePk: string;
  patchedSmapFamily: PatchedSmapFamily;
};
export type SalesSmapFamilyDestroyApiResponse = unknown;
export type SalesSmapFamilyDestroyApiArg = {
  pk: number;
  salePk: string;
};
export type SalesTitlesListApiResponse = /** status 200  */ TitleList;
export type SalesTitlesListApiArg = {
  salePk: string;
};
export type SalesTitlesUpdateApiResponse = /** status 200  */ Title;
export type SalesTitlesUpdateApiArg = {
  salePk: string;
  title: Title;
};
export type SalesTitlesRetrieveApiResponse = /** status 200  */ Title;
export type SalesTitlesRetrieveApiArg = {
  salePk: string;
  valuation: number;
};
export type SalesTitlesUpdate2ApiResponse = /** status 200  */ Title;
export type SalesTitlesUpdate2ApiArg = {
  salePk: string;
  valuation: number;
  title: Title;
};
export type SalesUnionListApiResponse = /** status 200  */ AnzUnionList;
export type SalesUnionListApiArg = {
  salePk: string;
};
export type SalesUnionCreateApiResponse = /** status 201  */ AnzUnion;
export type SalesUnionCreateApiArg = {
  salePk: string;
  anzUnion: AnzUnion;
};
export type SalesUnionRetrieveApiResponse = /** status 200  */ AnzUnion;
export type SalesUnionRetrieveApiArg = {
  /** A unique integer value identifying this anz union. */
  pk: number;
  salePk: string;
};
export type SalesUnionUpdateApiResponse = /** status 200  */ AnzUnion;
export type SalesUnionUpdateApiArg = {
  /** A unique integer value identifying this anz union. */
  pk: number;
  salePk: string;
  anzUnion: AnzUnion;
};
export type SalesUnionPartialUpdateApiResponse = /** status 200  */ AnzUnion;
export type SalesUnionPartialUpdateApiArg = {
  /** A unique integer value identifying this anz union. */
  pk: number;
  salePk: string;
  patchedAnzUnion: PatchedAnzUnion;
};
export type SalesUnionDestroyApiResponse = unknown;
export type SalesUnionDestroyApiArg = {
  /** A unique integer value identifying this anz union. */
  pk: number;
  salePk: string;
};
export type SalesExportRetrieveApiResponse = /** status 200  */ Sale;
export type SalesExportRetrieveApiArg = void;
export type SalesBboxRetrieveApiResponse = unknown;
export type SalesBboxRetrieveApiArg = void;
export type ScenarioExportListApiResponse =
  /** status 200  */ ScenarioExportRequest[];
export type ScenarioExportListApiArg = void;
export type ScenarioExportCreateApiResponse =
  /** status 201  */ ScenarioExportRequest;
export type ScenarioExportCreateApiArg = {
  riskRadarExport: RiskRadarExport;
};
export type ScenarioExportRetrieveApiResponse =
  /** status 200  */ ScenarioExportRequest;
export type ScenarioExportRetrieveApiArg = {
  /** A unique integer value identifying this scenario export request. */
  pk: number;
};
export type SmapSiblingsListApiResponse = /** status 200  */ SmapSibling[];
export type SmapSiblingsListApiArg = {
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  smu?: string[];
};
export type SmapSiblingsRetrieveApiResponse = /** status 200  */ SmapSibling;
export type SmapSiblingsRetrieveApiArg = {
  pk: number;
};
export type TitleListApiResponse = /** status 200  */ PaginatedTitleList;
export type TitleListApiArg = {
  exclude?: number[];
  match?: string;
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
};
export type TitleRetrieveApiResponse = /** status 200  */ Title;
export type TitleRetrieveApiArg = {
  /** A unique value identifying this linz titles. */
  pk: number;
};
export type SelectedTitleDvrListApiResponse =
  /** status 200  */ DistrictValuationRoll[];
export type SelectedTitleDvrListApiArg = {
  titleIds?: number[];
};
export type SelectedTitleExportPdfApiResponse = /** status 200  */ TitlePdfData;
export type SelectedTitleExportPdfApiArg = {
  titleIds?: number[];
};
export type SelectedTitleListApiResponse = /** status 200  */ TitleList;
export type SelectedTitleListApiArg = {
  titleIds?: number[];
};
export type SelectedTitleMemorialListApiResponse =
  /** status 200  */ LinzTitlesMemorial[];
export type SelectedTitleMemorialListApiArg = {
  titleIds?: number[];
};
export type TitleRetrieveByNumberRetrieveApiResponse = /** status 200  */ Title;
export type TitleRetrieveByNumberRetrieveApiArg = {
  titleNumber?: string;
};
export type TitleLikelyAddressListApiResponse = /** status 200  */ Address[];
export type TitleLikelyAddressListApiArg = void;
export type TitleLikelyAddressRetrieveApiResponse = /** status 200  */ Address;
export type TitleLikelyAddressRetrieveApiArg = {
  pk: string;
};
export type TitleReverseSearchListApiResponse =
  /** status 200  */ PaginatedReverseSearchList;
export type TitleReverseSearchListApiArg = {
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
};
export type TitleReverseSearchRetrieveApiResponse =
  /** status 200  */ ReverseSearch;
export type TitleReverseSearchRetrieveApiArg = {
  /** A unique value identifying this linz titles. */
  pk: number;
};
export type TitleSearchListApiResponse =
  /** status 200  */ PaginatedLinzTitlesSearchList;
export type TitleSearchListApiArg = {
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
};
export type TitleSearchRetrieveApiResponse =
  /** status 200  */ LinzTitlesSearch;
export type TitleSearchRetrieveApiArg = {
  /** A unique value identifying this linz titles. */
  pk: number;
};
export type TitlesBboxRetrieveApiResponse = unknown;
export type TitlesBboxRetrieveApiArg = void;
export type TradingGroupListApiResponse =
  /** status 200  */ PaginatedTradingGroupList;
export type TradingGroupListApiArg = {
  match?: string;
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
};
export type TradingGroupRetrieveApiResponse = /** status 200  */ TradingGroup;
export type TradingGroupRetrieveApiArg = {
  pk: string;
};
export type TradingGroupAddressesListApiResponse =
  /** status 200  */ Valuation[];
export type TradingGroupAddressesListApiArg = {
  pk: string;
};
export type TradingGroupBenchmarkListApiResponse =
  /** status 200  */ Benchmark[];
export type TradingGroupBenchmarkListApiArg = {
  measure?: string;
  pk: string;
};
export type TradingGroupCustomersListApiResponse =
  /** status 200  */ Customer[];
export type TradingGroupCustomersListApiArg = {
  pk: string;
};
export type TradingGroupEmissionsBenchmarkRetrieveApiResponse =
  /** status 200  */ EmissionPercentile;
export type TradingGroupEmissionsBenchmarkRetrieveApiArg = {
  pk: number;
};
export type TradingGroupLendingListApiResponse = /** status 200  */ Facility[];
export type TradingGroupLendingListApiArg = {
  pk: string;
};
export type TradingGroupSequestrationRetrieveApiResponse =
  /** status 200  */ Sequestration;
export type TradingGroupSequestrationRetrieveApiArg = {
  pk: string;
};
export type TradingGroupValuationsListApiResponse =
  /** status 200  */ Valuation[];
export type TradingGroupValuationsListApiArg = {
  pk: string;
};
export type TradingGroupAddressesList2ApiResponse =
  /** status 200  */ PaginatedTradingGroupAddressList;
export type TradingGroupAddressesList2ApiArg = {
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
  tradingGroupPk: string;
};
export type TradingGroupAddressesCreateApiResponse =
  /** status 201  */ TradingGroupAddress;
export type TradingGroupAddressesCreateApiArg = {
  tradingGroupPk: string;
  tradingGroupAddress: TradingGroupAddress;
};
export type TradingGroupAddressesRetrieveApiResponse =
  /** status 200  */ TradingGroupAddress;
export type TradingGroupAddressesRetrieveApiArg = {
  /** A unique integer value identifying this Trading Group Address. */
  pk: number;
  tradingGroupPk: string;
};
export type TradingGroupAddressesUpdateApiResponse =
  /** status 200  */ TradingGroupAddress;
export type TradingGroupAddressesUpdateApiArg = {
  /** A unique integer value identifying this Trading Group Address. */
  pk: number;
  tradingGroupPk: string;
  tradingGroupAddress: TradingGroupAddress;
};
export type TradingGroupAddressesPartialUpdateApiResponse =
  /** status 200  */ TradingGroupAddress;
export type TradingGroupAddressesPartialUpdateApiArg = {
  /** A unique integer value identifying this Trading Group Address. */
  pk: number;
  tradingGroupPk: string;
  patchedTradingGroupAddress: PatchedTradingGroupAddress;
};
export type TradingGroupAddressesDestroyApiResponse = unknown;
export type TradingGroupAddressesDestroyApiArg = {
  /** A unique integer value identifying this Trading Group Address. */
  pk: number;
  tradingGroupPk: string;
};
export type TradingGroupPropertiesListApiResponse =
  /** status 200  */ PaginatedTradingGroupPropertyList;
export type TradingGroupPropertiesListApiArg = {
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
  tradingGroupPk: string;
};
export type TradingGroupPropertiesRetrieveApiResponse =
  /** status 200  */ TradingGroupProperty;
export type TradingGroupPropertiesRetrieveApiArg = {
  pk: string;
  tradingGroupPk: string;
};
export type TradingGroupLinkAddressCreateApiResponse =
  /** status 200  */ TradingGroup;
export type TradingGroupLinkAddressCreateApiArg = {
  tradingGroupAddress: TradingGroupAddress;
};
export type TradingGroupRegionListApiResponse =
  /** status 200  */ TradingGroupRegion[];
export type TradingGroupRegionListApiArg = void;
export type TradingGroupRegionRetrieveApiResponse =
  /** status 200  */ TradingGroupRegion;
export type TradingGroupRegionRetrieveApiArg = {
  pk: number;
};
export type UserCurrentRetrieveApiResponse = /** status 200  */ User;
export type UserCurrentRetrieveApiArg = void;
export type UserUserSettingsUpdateApiResponse = unknown;
export type UserUserSettingsUpdateApiArg = {
  userSettings: UserSettings;
};
export type UserOptionsListApiResponse = /** status 200  */ PaginatedUserList;
export type UserOptionsListApiArg = {
  match?: string;
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
};
export type UsernamesListApiResponse = /** status 200  */ Username[];
export type UsernamesListApiArg = {
  username?: string;
};
export type UsernamesRetrieveApiResponse = /** status 200  */ Username;
export type UsernamesRetrieveApiArg = {
  /** A unique integer value identifying this user. */
  pk: number;
};
export type ValidKpiMeasuresListApiResponse = /** status 200  */ KpiType[];
export type ValidKpiMeasuresListApiArg = {
  segment?: string;
};
export type ValidKpiMeasuresCascaderOptionsApiResponse =
  /** status 200  */ SelectOption[];
export type ValidKpiMeasuresCascaderOptionsApiArg = {
  segment?: string;
};
export type ValocitySalesListApiResponse =
  /** status 200  */ PaginatedValocityGeoSaleList;
export type ValocitySalesListApiArg = {
  addressMatch?: string;
  effectiveHaGte?: number;
  effectiveHaLte?: number;
  grossSalesPriceGte?: number;
  grossSalesPriceLte?: number;
  improvementsValueGte?: number;
  improvementsValueLte?: number;
  irrelevantDateIsnull?: boolean;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  landUses?: string[];
  linked?: boolean;
  match?: string;
  notionalSiteValueGte?: number;
  notionalSiteValueLte?: number;
  /** Ordering
    
    * `fullAddress` - Fulladdress
    * `-fullAddress` - Fulladdress (descending)
    * `bestUse` - Bestuse
    * `-bestUse` - Bestuse (descending)
    * `region` - Region
    * `-region` - Region (descending)
    * `saleDate` - Saledate
    * `-saleDate` - Saledate (descending)
    * `effectiveHa` - Effectiveha
    * `-effectiveHa` - Effectiveha (descending)
    * `grossSalesPrice` - Grosssalesprice
    * `-grossSalesPrice` - Grosssalesprice (descending) */
  orderBy?: (
    | "-bestUse"
    | "-effectiveHa"
    | "-fullAddress"
    | "-grossSalesPrice"
    | "-region"
    | "-saleDate"
    | "bestUse"
    | "effectiveHa"
    | "fullAddress"
    | "grossSalesPrice"
    | "region"
    | "saleDate"
  )[];
  /** A page number within the paginated result set. */
  page?: number;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  regionIn?: string[];
  saleDateGte?: string;
  saleDateLte?: string;
  /** Number of results to return per page. */
  size?: number;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  sourceIn?: string[];
  totalHaGte?: number;
  totalHaLte?: number;
  valueOfStockGte?: number;
  valueOfStockLte?: number;
  vettedIsnull?: boolean;
};
export type ValocitySalesRetrieveApiResponse =
  /** status 200  */ ValocityGeoSale;
export type ValocitySalesRetrieveApiArg = {
  /** A unique integer value identifying this valocity sale. */
  pk: number;
};
export type ValocitySalesUpdateApiResponse = unknown;
export type ValocitySalesUpdateApiArg = {
  /** A unique integer value identifying this valocity sale. */
  pk: number;
  valocitySaleUpdate: ValocitySaleUpdate;
};
export type ValocitySalesAddressListApiResponse = /** status 200  */ Address[];
export type ValocitySalesAddressListApiArg = {
  salePk: string;
};
export type ValocitySalesAddressRetrieveApiResponse =
  /** status 200  */ Address;
export type ValocitySalesAddressRetrieveApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
};
export type ValocitySalesElevationListApiResponse =
  /** status 200  */ ElevationList;
export type ValocitySalesElevationListApiArg = {
  salePk: string;
};
export type ValocitySalesElevationRetrieveApiResponse =
  /** status 200  */ Elevation;
export type ValocitySalesElevationRetrieveApiArg = {
  /** A unique value identifying this elevation. */
  pk: number;
  salePk: string;
};
export type ValocitySalesNeighboursListApiResponse =
  /** status 200  */ NeighbourList;
export type ValocitySalesNeighboursListApiArg = {
  salePk: string;
};
export type ValocitySalesNeighboursRetrieveApiResponse =
  /** status 200  */ Neighbour;
export type ValocitySalesNeighboursRetrieveApiArg = {
  /** A unique integer value identifying this address. */
  pk: number;
  salePk: string;
};
export type ValocitySalesSmapFamilyListApiResponse =
  /** status 200  */ SmapFamilyList;
export type ValocitySalesSmapFamilyListApiArg = {
  salePk: string;
};
export type ValocitySalesSmapFamilyRetrieveApiResponse =
  /** status 200  */ SmapFamily;
export type ValocitySalesSmapFamilyRetrieveApiArg = {
  pk: number;
  salePk: string;
};
export type ValocitySalesTitlesListApiResponse =
  /** status 200  */ PaginatedTitleList;
export type ValocitySalesTitlesListApiArg = {
  /** A page number within the paginated result set. */
  page?: number;
  /** A unique integer value identifying this valocity sale. */
  salePk: number;
  /** Number of results to return per page. */
  size?: number;
};
export type ValocitySalesTitlesRetrieveApiResponse = /** status 200  */ Title;
export type ValocitySalesTitlesRetrieveApiArg = {
  /** A unique integer value identifying this valocity sale. */
  pk: number;
  /** A unique integer value identifying this valocity sale. */
  salePk: number;
};
export type ValocitySalesUnionListApiResponse = /** status 200  */ AnzUnionList;
export type ValocitySalesUnionListApiArg = {
  salePk: string;
};
export type ValocitySalesUnionRetrieveApiResponse = /** status 200  */ AnzUnion;
export type ValocitySalesUnionRetrieveApiArg = {
  /** A unique integer value identifying this anz union. */
  pk: number;
  salePk: string;
};
export type ValocitySalesBboxRetrieveApiResponse = unknown;
export type ValocitySalesBboxRetrieveApiArg = void;
export type ValuationFirmsListApiResponse = /** status 200  */ ValuationFirm[];
export type ValuationFirmsListApiArg = {
  /** Type of the panel valuer (commercial or rural) this valuation firm relates to
    
    * `commercial` - Commercial
    * `rural` - Rural */
  valuerType?: "commercial" | "rural";
};
export type ValuationFirmsRetrieveApiResponse =
  /** status 200  */ ValuationFirm;
export type ValuationFirmsRetrieveApiArg = {
  /** A unique integer value identifying this valuation firm. */
  pk: number;
};
export type ValuationSearchListApiResponse =
  /** status 200  */ PaginatedValuationSearchList;
export type ValuationSearchListApiArg = {
  addressMatch?: string;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  creators?: string[];
  currentUser?: boolean;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  highestAndBestUses?: string[];
  match?: string;
  maxCompletedDate?: string;
  maxCreatedDate?: string;
  minCompletedDate?: string;
  minCreatedDate?: string;
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
  /** Multiple values may be specified as name=1&name=2 or name[]=1&name[]=2 */
  tla?: string[];
  /** * `PENDING` - Pending
   * `COMPLETED` - Completed
   * `ALL` - All */
  valuationState?: "ALL" | "COMPLETED" | "PENDING";
};
export type ValuationSearchCreateApiResponse =
  /** status 201  */ ValuationSearch;
export type ValuationSearchCreateApiArg = {
  valuationSearch: ValuationSearch;
};
export type ValuationSearchRetrieveApiResponse =
  /** status 200  */ ValuationSearch;
export type ValuationSearchRetrieveApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
};
export type ValuationSearchUpdateApiResponse =
  /** status 200  */ ValuationSearch;
export type ValuationSearchUpdateApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
  valuationSearch: ValuationSearch;
};
export type ValuationSearchPartialUpdateApiResponse =
  /** status 200  */ ValuationSearch;
export type ValuationSearchPartialUpdateApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
  patchedValuationSearch: PatchedValuationSearch;
};
export type ValuationSearchDestroyApiResponse = unknown;
export type ValuationSearchDestroyApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
};
export type ValuationsListApiResponse = /** status 200  */ Valuations[];
export type ValuationsListApiArg = void;
export type ValuationsCreateApiResponse = /** status 201  */ ValuationsWritable;
export type ValuationsCreateApiArg = {
  valuationsWritable: ValuationsWritable;
};
export type ValuationsRetrieveApiResponse = /** status 200  */ Valuations;
export type ValuationsRetrieveApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
};
export type ValuationsUpdateApiResponse = /** status 200  */ ValuationsWritable;
export type ValuationsUpdateApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
  valuationsWritable: ValuationsWritable;
};
export type ValuationsPartialUpdateApiResponse =
  /** status 200  */ ValuationsWritable;
export type ValuationsPartialUpdateApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
  patchedValuationsWritable: PatchedValuationsWritable;
};
export type ValuationsDestroyApiResponse = unknown;
export type ValuationsDestroyApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
};
export type ValuationsPvsSummaryRetrieveApiResponse =
  /** status 200  */ ValuationsPvsSummary;
export type ValuationsPvsSummaryRetrieveApiArg = {
  /** A unique integer value identifying this Valuation. */
  pk: number;
};
export type ValuationsCombinedResourceConsentsListApiResponse =
  /** status 200  */ ValuationsCombinedResourceConsent[];
export type ValuationsCombinedResourceConsentsListApiArg = {
  valuationPk: number;
};
export type ValuationsCombinedResourceConsentsRetrieveApiResponse =
  /** status 200  */ ValuationsCombinedResourceConsent;
export type ValuationsCombinedResourceConsentsRetrieveApiArg = {
  pk: number;
  valuationPk: number;
};
export type ValuationsProjectedValuationSummariesListApiResponse =
  /** status 200  */ ProjectedValuationSummary[];
export type ValuationsProjectedValuationSummariesListApiArg = {
  valuationPk: number;
};
export type ValuationsProjectedValuationSummariesRetrieveApiResponse =
  /** status 200  */ ProjectedValuationSummary;
export type ValuationsProjectedValuationSummariesRetrieveApiArg = {
  /** A unique integer value identifying this projected valuation summary. */
  pk: number;
  valuationPk: number;
};
export type ValuationsResourceConsentRecordsListApiResponse =
  /** status 200  */ ValuationsResourceConsentRecord[];
export type ValuationsResourceConsentRecordsListApiArg = {
  valuationPk: number;
};
export type ValuationsResourceConsentRecordsCreateApiResponse =
  /** status 201  */ ValuationsResourceConsentRecord;
export type ValuationsResourceConsentRecordsCreateApiArg = {
  valuationPk: number;
  valuationsResourceConsentRecord: ValuationsResourceConsentRecord;
};
export type ValuationsResourceConsentRecordsRetrieveApiResponse =
  /** status 200  */ ValuationsResourceConsentRecord;
export type ValuationsResourceConsentRecordsRetrieveApiArg = {
  /** A unique integer value identifying this valuation resource consent record. */
  pk: number;
  valuationPk: number;
};
export type ValuationsResourceConsentRecordsUpdateApiResponse =
  /** status 200  */ ValuationsResourceConsentRecord;
export type ValuationsResourceConsentRecordsUpdateApiArg = {
  /** A unique integer value identifying this valuation resource consent record. */
  pk: number;
  valuationPk: number;
  valuationsResourceConsentRecord: ValuationsResourceConsentRecord;
};
export type ValuationsResourceConsentRecordsPartialUpdateApiResponse =
  /** status 200  */ ValuationsResourceConsentRecord;
export type ValuationsResourceConsentRecordsPartialUpdateApiArg = {
  /** A unique integer value identifying this valuation resource consent record. */
  pk: number;
  valuationPk: number;
  patchedValuationsResourceConsentRecord: PatchedValuationsResourceConsentRecord;
};
export type ValuationsResourceConsentRecordsDestroyApiResponse = unknown;
export type ValuationsResourceConsentRecordsDestroyApiArg = {
  /** A unique integer value identifying this valuation resource consent record. */
  pk: number;
  valuationPk: number;
};
export type ValuationsTitlesListApiResponse =
  /** status 200  */ ValuationsSavedTitle[];
export type ValuationsTitlesListApiArg = {
  valuationPk: number;
};
export type ValuationsTitlesCreateApiResponse =
  /** status 201  */ ValuationsSavedTitle;
export type ValuationsTitlesCreateApiArg = {
  valuationPk: number;
  valuationsSavedTitle: ValuationsSavedTitle;
};
export type ValuationsTitlesRetrieveApiResponse =
  /** status 200  */ ValuationsSavedTitle;
export type ValuationsTitlesRetrieveApiArg = {
  /** A unique integer value identifying this valuation saved title. */
  pk: number;
  valuationPk: number;
};
export type ValuationsTitlesUpdateApiResponse =
  /** status 200  */ ValuationsSavedTitle;
export type ValuationsTitlesUpdateApiArg = {
  /** A unique integer value identifying this valuation saved title. */
  pk: number;
  valuationPk: number;
  valuationsSavedTitle: ValuationsSavedTitle;
};
export type ValuationsTitlesPartialUpdateApiResponse =
  /** status 200  */ ValuationsSavedTitle;
export type ValuationsTitlesPartialUpdateApiArg = {
  /** A unique integer value identifying this valuation saved title. */
  pk: number;
  valuationPk: number;
  patchedValuationsSavedTitle: PatchedValuationsSavedTitle;
};
export type ValuationsTitlesDestroyApiResponse = unknown;
export type ValuationsTitlesDestroyApiArg = {
  /** A unique integer value identifying this valuation saved title. */
  pk: number;
  valuationPk: number;
};
export type ValuationsResourceConsentRecordsAttachmentsListApiResponse =
  /** status 200  */ ValuationsResourceConsentRecordAttachment[];
export type ValuationsResourceConsentRecordsAttachmentsListApiArg = void;
export type ValuationsResourceConsentRecordsAttachmentsCreateApiResponse =
  /** status 201  */ ValuationsResourceConsentRecordAttachment;
export type ValuationsResourceConsentRecordsAttachmentsCreateApiArg = {
  valuationsResourceConsentRecordAttachment: ValuationsResourceConsentRecordAttachment;
};
export type ValuationsResourceConsentRecordsAttachmentsRetrieveApiResponse =
  /** status 200  */ ValuationsResourceConsentRecordAttachment;
export type ValuationsResourceConsentRecordsAttachmentsRetrieveApiArg = {
  /** A unique integer value identifying this valuation resource consent record attachment. */
  pk: number;
};
export type ValuationsResourceConsentRecordsAttachmentsUpdateApiResponse =
  /** status 200  */ ValuationsResourceConsentRecordAttachment;
export type ValuationsResourceConsentRecordsAttachmentsUpdateApiArg = {
  /** A unique integer value identifying this valuation resource consent record attachment. */
  pk: number;
  valuationsResourceConsentRecordAttachment: ValuationsResourceConsentRecordAttachment;
};
export type ValuationsResourceConsentRecordsAttachmentsPartialUpdateApiResponse =
  /** status 200  */ ValuationsResourceConsentRecordAttachment;
export type ValuationsResourceConsentRecordsAttachmentsPartialUpdateApiArg = {
  /** A unique integer value identifying this valuation resource consent record attachment. */
  pk: number;
  patchedValuationsResourceConsentRecordAttachment: PatchedValuationsResourceConsentRecordAttachment;
};
export type ValuationsResourceConsentRecordsAttachmentsDestroyApiResponse =
  unknown;
export type ValuationsResourceConsentRecordsAttachmentsDestroyApiArg = {
  /** A unique integer value identifying this valuation resource consent record attachment. */
  pk: number;
};
export type ValuationsTitlesReviewsListApiResponse =
  /** status 200  */ ValuationsTitleReview[];
export type ValuationsTitlesReviewsListApiArg = void;
export type ValuationsTitlesReviewsCreateApiResponse =
  /** status 201  */ ValuationsTitleReviewWritable;
export type ValuationsTitlesReviewsCreateApiArg = {
  valuationsTitleReviewWritable: ValuationsTitleReviewWritable;
};
export type ValuationsTitlesReviewsRetrieveApiResponse =
  /** status 200  */ ValuationsTitleReview;
export type ValuationsTitlesReviewsRetrieveApiArg = {
  /** A unique integer value identifying this valuation title review. */
  pk: number;
};
export type ValuationsTitlesReviewsUpdateApiResponse =
  /** status 200  */ ValuationsTitleReviewWritable;
export type ValuationsTitlesReviewsUpdateApiArg = {
  /** A unique integer value identifying this valuation title review. */
  pk: number;
  valuationsTitleReviewWritable: ValuationsTitleReviewWritable;
};
export type ValuationsTitlesReviewsPartialUpdateApiResponse =
  /** status 200  */ ValuationsTitleReviewWritable;
export type ValuationsTitlesReviewsPartialUpdateApiArg = {
  /** A unique integer value identifying this valuation title review. */
  pk: number;
  patchedValuationsTitleReviewWritable: PatchedValuationsTitleReviewWritable;
};
export type ValuationsTitlesReviewsDestroyApiResponse = unknown;
export type ValuationsTitlesReviewsDestroyApiArg = {
  /** A unique integer value identifying this valuation title review. */
  pk: number;
};
export type LinzTitles = {
  fid: number;
  feature: string;
  area: number;
  titleId: number;
  titleNumber: string;
  id: number;
  titleNo: string;
  status?: string | null;
  type?: string | null;
  landDistrict?: string | null;
  issueDate?: string | null;
  guaranteeStatus?: string | null;
  estateDescription?: string | null;
  owners?: string | null;
  spatialExtentsShared?: string | null;
  searchVector?: string | null;
  surveyArea: string;
  districtValuationRolls: number[];
};
export type AddressesDistrictValuationRoll = {
  dvrId: number;
  titles: LinzTitles[];
  dipid: string | null;
  valRef?: string | null;
  tlaId?: string | null;
  tlaName?: string | null;
  fullAddress?: string | null;
  category?: string | null;
  valDate?: string | null;
  cv?: number | null;
  lv?: number | null;
  iv?: number | null;
  landArea?: number | null;
  floorArea?: number | null;
  improvement?: string | null;
  legalDesc?: string | null;
  salesGroup?: string | null;
  unitsOfUse?: string | null;
  landUse?: string | null;
  landUseDesc?: string | null;
  landZone?: string | null;
  landZoneDesc?: string | null;
  deletedDate?: string | null;
  updatedDate: string | null;
  searchVector?: string | null;
};
export type Addresses = {
  addressId: number;
  id: number;
  districtValuationRoll: AddressesDistrictValuationRoll;
  lat?: number;
  lng?: number;
  tradingGroupId: string | null;
  fullAddress: string;
  owners: string;
  searchVector?: string | null;
  geom?: {
    type: "Point";
    coordinates: number[];
  } | null;
  deletedDate?: string | null;
  updatedDate: string | null;
  createdDate: string | null;
  linzAddress?: number | null;
  tradingGroup: string[];
};
export type PatchedAddresses = {
  addressId?: number;
  id?: number;
  districtValuationRoll?: AddressesDistrictValuationRoll;
  lat?: number;
  lng?: number;
  tradingGroupId?: string | null;
  fullAddress?: string;
  owners?: string;
  searchVector?: string | null;
  geom?: {
    type: "Point";
    coordinates: number[];
  } | null;
  deletedDate?: string | null;
  updatedDate?: string | null;
  createdDate?: string | null;
  linzAddress?: number | null;
  tradingGroup?: string[];
};
export type AddressLineTitleFeature = {
  id: string;
  geometry: {
    [key: string]: any;
  };
  type: string | null;
};
export type AddressLineTitleFeatureCollection = {
  pk: number;
  features: AddressLineTitleFeature[];
  type: string;
};
export type AddressLine = {
  id: string;
  fullAddress: string;
  fields: {
    id: string;
    title: string;
    value: (number | string) | null;
  }[];
  titles: AddressLineTitleFeatureCollection;
};
export type AdminGreenProject = {
  pk: number;
  deletedDatetime?: string | null;
  name?: string | null;
  finalizedDate?: string | null;
};
export type AdminSale = {
  pk: number;
  fullAddress?: string;
  status?: string;
};
export type AdminTerroritorialAuthorityAssignment = {
  id?: number;
  territorialAuthority: string;
  valuer: number;
  valuerName?: string;
};
export type PatchedAdminTerroritorialAuthorityAssignment = {
  id?: number;
  territorialAuthority?: string;
  valuer?: number;
  valuerName?: string;
};
export type UserSummary = {
  username: string;
  count: number;
  date: string;
};
export type PageSummary = {
  pageUrl: string;
  count: number;
  date: string;
};
export type TimeSummary = {
  count: number;
  date: string;
};
export type ByUserAggregate = {
  count: number;
  date: string;
  user: string;
};
export type ByRegionAggregate = {
  count: number;
  date: string;
  region: string;
};
export type Usage = {
  users: UserSummary[];
  pages: PageSummary[];
  time: TimeSummary[];
  salesCreatedByUser: ByUserAggregate[];
  salesCreatedByRegion: ByRegionAggregate[];
  salesVettedByRegion: ByRegionAggregate[];
  valuationsCreatedByUser: ByUserAggregate[];
  valuationsCreatedByRegion: ByRegionAggregate[];
  valuationsCompletedByUser: ByUserAggregate[];
  valuationsCompletedByRegion: ByRegionAggregate[];
  valocitySalesCreatedByRegion: ByRegionAggregate[];
  valocitySalesProcessedByRegion: ByRegionAggregate[];
  valocitySalesProcessedByUser: ByUserAggregate[];
  valuationsCompletedYoy: ByUserAggregate[];
  rvrsCreatedByUser: ByUserAggregate[];
  rvrsCompletedByUser: ByUserAggregate[];
};
export type UserAdmin = {
  username: string;
  id: number;
  pk: number;
  isAdmin: boolean;
  isValuer: boolean;
  groups: string;
};
export type Alert = {
  id: number;
  createdDatetime: string;
  updatedDatetime: string;
  priority: "Low" | "Medium" | "High" | "Priority" | "Info";
  jiraLink?: string | null;
  title: string;
  description: string;
};
export type PatchedAlert = {
  id?: number;
  createdDatetime?: string;
  updatedDatetime?: string;
  priority?: "Low" | "Medium" | "High" | "Priority" | "Info";
  jiraLink?: string | null;
  title?: string;
  description?: string;
};
export type Anzsic = {
  id: number;
  code: string;
  description?: string;
  isOccupationCode?: boolean;
};
export type ValuationApportionmentTitle = {
  titleNo?: string | null;
  areaHa?: string | null;
};
export type ValuationMortgageApportionment = {
  id: number;
  mortgageNumber: string;
  titles: ValuationApportionmentTitle[];
  total_LWB?: string;
  totalImprovementsValue?: string;
  total_AEP: number;
  totalEffectiveHectares: string;
  mortgageKey: string;
  totalValue?: string;
  totalIneffectiveHectares?: string;
  mortgageTitleApportionment: number;
};
export type ValuationMortgageTitleApportionment = {
  id: number;
  mortgageApportionments: ValuationMortgageApportionment[];
  total_LWB?: string;
  totalImprovementsValue?: string;
  total_AEP: number;
  totalEffectiveHectares: string;
  valuation: number;
};
export type TerritorialUnit = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    level: 0 | 1 | 2 | 3 | 4;
    country: "NZ" | "AU";
    localProjection?: number | null;
    name: string;
  };
};
export type TerritorialUnitList = {
  type?: "FeatureCollection";
  features?: TerritorialUnit[];
};
export type SelectOption = {
  label: string;
  value: string;
};
export type CcraTopContributors = {
  id: number;
  ccraId: number;
  contributor: string;
  helpText?: string | null;
  index: number;
  isCustomContributor: boolean;
};
export type CcraRevenueOrOutput = {
  id: number;
  ccraId: number;
  yearEnd?: string | null;
  quantity?: string | null;
  unit?: string | null;
  tag?: string | null;
};
export type CcraTransitionRisk = {
  id: number;
  ccraId: number;
  risk: string;
  helpText?: string | null;
  measureTaken?: string | null;
  materialToCustomer?: string | null;
};
export type CcraPhysicalRisk = {
  id: number;
  ccraId: number;
  risk: string;
  helpText?: string | null;
  measureTaken?: string | null;
  materialToCustomer?: string | null;
  isInsured: boolean;
};
export type CcraEmissionsTarget = {
  id: number;
  ccraId: number;
  emissionsType?: string | null;
  emissionsTargetYear?: string | null;
  emissionsTargetType?: string | null;
  emissionsTargetPercentage?: string | null;
  emissionsTargetAbsoluteFrom?: string | null;
  emissionsTargetAbsoluteTo?: string | null;
  emissionsTargetAbsoluteUnits?: string | null;
  emissionsTargetIntensityFrom?: string | null;
  emissionsTargetIntensityTo?: string | null;
  emissionsTargetIntensityUnits?: string | null;
  emissionsTargetIntensityPerUnitOf?: string | null;
  emissionsBaselineYear?: string | null;
  emissionsBaselineValue?: string | null;
  emissionsBaselineUnits?: string | null;
  emissionsBaselineTargetSetYear?: string | null;
};
export type CcraAttachment = {
  id: number;
  createdDate: string;
  deletedDate?: string | null;
  name: string;
  contentType: string;
  size: number;
  ccra: number;
};
export type CustomerEmissionsReportAttachment = {
  pk: number;
  attachment?: string;
  fileName: string;
  url: string;
  attachmentTypeId: number;
  reportId: number;
  reportTypeId: number;
};
export type CustomerEmissionsMetricType = {
  pk: number;
  name: string;
  unit: string;
  isSection?: boolean;
};
export type CcraCustomerEmissionsMetric = {
  pk: number;
  metricTypeId: number;
  reportTypeId: number;
  value: string;
  metricType: CustomerEmissionsMetricType;
};
export type CcraCustomerEmissionsReport = {
  attachments?: CustomerEmissionsReportAttachment[];
  pk: number;
  reportType: number;
  reportTypeName: string;
  status: "draft" | "completed";
  reportingPeriod?: string[] | null;
  metrics?: CcraCustomerEmissionsMetric[];
};
export type Ccra = {
  id: number;
  creator: number;
  creatorName: string;
  status: "incomplete" | "complete";
  disclosures?: string | null;
  reportingPeriod?: string[] | null;
  participantOfEts?: boolean;
  knowsEmissions?: boolean | null;
  emissionsMeasurementPlan?: string | null;
  emissionsMeasurementMethod?: string | null;
  emissionsVerificationMethod?: string | null;
  periodStartEndDate?: string[] | null;
  emissionsScope1?: string | null;
  emissionsScope2?: string | null;
  emissionsScope3?: string | null;
  emissionsScope3SupplyChainAspects?: string | null;
  kgms?: string | null;
  biologicalIntensity?: string | null;
  nonBiologicalIntensity?: string | null;
  methane?: string | null;
  nitrogen?: string | null;
  carbon?: string | null;
  co2Mt?: string | null;
  co2eKg?: string | null;
  synlaitCo2eKgms?: string | null;
  synlaitKgms?: string | null;
  tradingGroupRevenueReportingPercentage?: string | null;
  topContributors?: CcraTopContributors[];
  revenueOrOutput?: CcraRevenueOrOutput[];
  emissionsReductionPlan?: string | null;
  emissionsReductionPlanTargetMeasureExplanation?: string | null;
  transitionRisks?: CcraTransitionRisk[];
  physicalRisks?: CcraPhysicalRisk[];
  emissionsStrategyFurtherCommentary?: string | null;
  hasCarbonCredits?: boolean;
  carbonCreditsDetail?: string | null;
  hasAssessedClimateTransitionRisks?: boolean | null;
  plansToAssessClimateTransitionRisks?: boolean | null;
  hasAssessedPhysicalRisks?: boolean | null;
  plansToAssessPhysicalRisks?: boolean | null;
  customers: number[];
  customerNames?: string | null;
  emissionsTargets?: CcraEmissionsTarget[];
  createdDatetime: string;
  updatedDatetime: string;
  climateRiskManagement?: number[];
  isLenzCustomer?: boolean | null;
  hasCustomerBeenAskedAboutStrategy?: boolean | null;
  customerBeenAskedAboutStrategyExplanation?: string | null;
  hasStrategyProvidedCompetitiveAdvantage?: boolean | null;
  strategyProvidedCompetitiveAdvantageExplanation?: string | null;
  segment: string;
  attachments: CcraAttachment[];
  newAttachments?: string[];
  businessUnit?:
    | (
        | "INSTITUTIONAL"
        | "RETAIL_AGRI"
        | "CORPORATE"
        | "RETAIL_BUSINESS"
        | "AGRI"
        | "SMALL_BUSINESS"
        | "RETAIL"
        | "PRIVATE"
        | "BUSINESS"
        | ""
        | null
      )
    | null;
  emissionsReport?: CcraCustomerEmissionsReport;
  canEdit: boolean;
  processFuels?: number[];
};
export type PatchedCcra = {
  id?: number;
  creator?: number;
  creatorName?: string;
  status?: "incomplete" | "complete";
  disclosures?: string | null;
  reportingPeriod?: string[] | null;
  participantOfEts?: boolean;
  knowsEmissions?: boolean | null;
  emissionsMeasurementPlan?: string | null;
  emissionsMeasurementMethod?: string | null;
  emissionsVerificationMethod?: string | null;
  periodStartEndDate?: string[] | null;
  emissionsScope1?: string | null;
  emissionsScope2?: string | null;
  emissionsScope3?: string | null;
  emissionsScope3SupplyChainAspects?: string | null;
  kgms?: string | null;
  biologicalIntensity?: string | null;
  nonBiologicalIntensity?: string | null;
  methane?: string | null;
  nitrogen?: string | null;
  carbon?: string | null;
  co2Mt?: string | null;
  co2eKg?: string | null;
  synlaitCo2eKgms?: string | null;
  synlaitKgms?: string | null;
  tradingGroupRevenueReportingPercentage?: string | null;
  topContributors?: CcraTopContributors[];
  revenueOrOutput?: CcraRevenueOrOutput[];
  emissionsReductionPlan?: string | null;
  emissionsReductionPlanTargetMeasureExplanation?: string | null;
  transitionRisks?: CcraTransitionRisk[];
  physicalRisks?: CcraPhysicalRisk[];
  emissionsStrategyFurtherCommentary?: string | null;
  hasCarbonCredits?: boolean;
  carbonCreditsDetail?: string | null;
  hasAssessedClimateTransitionRisks?: boolean | null;
  plansToAssessClimateTransitionRisks?: boolean | null;
  hasAssessedPhysicalRisks?: boolean | null;
  plansToAssessPhysicalRisks?: boolean | null;
  customers?: number[];
  customerNames?: string | null;
  emissionsTargets?: CcraEmissionsTarget[];
  createdDatetime?: string;
  updatedDatetime?: string;
  climateRiskManagement?: number[];
  isLenzCustomer?: boolean | null;
  hasCustomerBeenAskedAboutStrategy?: boolean | null;
  customerBeenAskedAboutStrategyExplanation?: string | null;
  hasStrategyProvidedCompetitiveAdvantage?: boolean | null;
  strategyProvidedCompetitiveAdvantageExplanation?: string | null;
  segment?: string;
  attachments?: CcraAttachment[];
  newAttachments?: string[];
  businessUnit?:
    | (
        | "INSTITUTIONAL"
        | "RETAIL_AGRI"
        | "CORPORATE"
        | "RETAIL_BUSINESS"
        | "AGRI"
        | "SMALL_BUSINESS"
        | "RETAIL"
        | "PRIVATE"
        | "BUSINESS"
        | ""
        | null
      )
    | null;
  emissionsReport?: CcraCustomerEmissionsReport;
  canEdit?: boolean;
  processFuels?: number[];
};
export type Upload = {
  file: Blob;
};
export type CustomerEmissionsMetricTypeGroup = {
  pk: number;
  name: string;
  metricTypes: CustomerEmissionsMetricType[];
};
export type CustomerEmissionsReportTypeMetricType = {
  metricType: CustomerEmissionsMetricType;
  metricGroup: CustomerEmissionsMetricTypeGroup;
  sortOrder?: number | null;
  description?: string | null;
};
export type CustomerEmissionsAttachmentType = {
  pk: number;
  name: string;
  required: boolean;
};
export type CustomerEmissionsReportType = {
  pk: number;
  name: string;
  sourceName: string;
  metricTypes: CustomerEmissionsReportTypeMetricType[];
  attachmentTypes: CustomerEmissionsAttachmentType[];
};
export type CcraEmissionsReportMetricMetadata = {
  id: number;
  reportType: CustomerEmissionsReportType;
  metricType: CustomerEmissionsMetricType;
  category: string;
  order: number;
};
export type CcraFormOption = {
  id: number;
  value: string;
};
export type ValuationFirm = {
  id: number;
  name: string;
};
export type PanelValuerRegion = {
  id: number;
  region: string;
};
export type Attachment = {
  pk: number;
  contentType: string;
  name: string;
  size: number;
  url: string;
};
export type PanelValuerCommercialSpecialisation = {
  id: number;
  prtsReview?: boolean;
  propertyType: number;
};
export type PanelValuer = {
  id: number;
  valuationFirm: ValuationFirm;
  competenceRegions: PanelValuerRegion[];
  postalAddress: string;
  landlinePhone?: string;
  mobilePhone?: string;
  attachments: Attachment[];
  newAttachments?: string[];
  currentAge: number;
  hasExpiredInsurance: boolean;
  apc: boolean;
  commercialSpecialisations?: PanelValuerCommercialSpecialisation[];
  ruralSpecialisations?: number[];
  name: string;
  state?: "abeyance" | "draft" | "published";
  valuerType: "commercial" | "rural";
  classification?: "Enhanced Review" | "Unlimited";
  officeAddressText?: string;
  postalAddressText?: string;
  addressSearchVector: string;
  email: string;
  internalComments?: string;
  updatedDate: string;
  createdDate: string;
  deletedDate?: string | null;
  deletionReason?: string | null;
  valuationRegistrationDate?: string | null;
  dateOfBirth?: string | null;
  piAggregateLimitM?: string;
  piPerClaimLimitM?: string;
  piCocExpiryDate?: string | null;
  plGlAggregateLimitM?: string;
  plGlPerClaimLimitM?: string;
  plGlCocExpiryDate?: string | null;
};
export type InstructionLetterRequestPropertyTitle = {
  id: number;
  titleNumber: string;
};
export type InstructionLetterRequestProperty = {
  id: number;
  titles: InstructionLetterRequestPropertyTitle[];
  addressOverrideText?: string | null;
  owners?: string;
  totalHectares?: string;
  address?: number | null;
};
export type InstructionLetterCustomerDetails = {
  id: number;
  alternativeName?: string | null;
  alternativePosition?: string | null;
  alternativePhoneNumber?: string | null;
  alternativeEmail?: string | null;
  name: string;
  phoneNumber: string;
  email: string;
};
export type ReadonlyInstructionLetterRequest = {
  id: number;
  panelValuer: PanelValuer;
  properties: InstructionLetterRequestProperty[];
  customerDetails: InstructionLetterCustomerDetails;
  letterType: string;
  propertyName?: string;
  propertyDescription?: string;
  interest?: string;
  tenancy?: string;
  basisOfValuation?: string;
  additionalReportingRequirements?: string | null;
  dateRequired?: string | null;
  customerFeeBasis?: boolean;
  feeBasisDescription?: string | null;
  updatedDate: string;
  createdDate: string;
  valuationManager: number;
  creator: number;
};
export type InstructionSenderDetails = {
  id: number;
  firstName: string;
  lastName: string;
  title: string;
  email: string;
  mobileNumber: string;
  user?: number | null;
};
export type CommercialInstructionLetterRequest = {
  id: number;
  properties: InstructionLetterRequestProperty[];
  customerDetails: InstructionLetterCustomerDetails;
  panelValuer: number;
  valuationManager: InstructionSenderDetails;
  deletedDate: string;
  additionalValuationMethodologies?: string[];
  propertyName?: string;
  propertyDescription?: string;
  interest?: string;
  tenancy?: string;
  basisOfValuation?: string;
  additionalReportingRequirements?: string | null;
  dateRequired?: string | null;
  customerFeeBasis?: boolean;
  feeBasisDescription?: string | null;
  updatedDate: string;
  createdDate: string;
  additionalValuationMethodology: boolean;
};
export type PatchedCommercialInstructionLetterRequest = {
  id?: number;
  properties?: InstructionLetterRequestProperty[];
  customerDetails?: InstructionLetterCustomerDetails;
  panelValuer?: number;
  valuationManager?: InstructionSenderDetails;
  deletedDate?: string;
  additionalValuationMethodologies?: string[];
  propertyName?: string;
  propertyDescription?: string;
  interest?: string;
  tenancy?: string;
  basisOfValuation?: string;
  additionalReportingRequirements?: string | null;
  dateRequired?: string | null;
  customerFeeBasis?: boolean;
  feeBasisDescription?: string | null;
  updatedDate?: string;
  createdDate?: string;
  additionalValuationMethodology?: boolean;
};
export type InstructionLetterRequestPropertyPdf = {
  id: number;
  titleNumbers: string;
  addressText: string;
  totalHectares: string;
  addressOverrideText?: string | null;
  owners?: string;
  address?: number | null;
};
export type CommercialInstructionLetterRequestPdf = {
  id: number;
  currentDate: string;
  valuationManager: InstructionSenderDetails;
  dateRequired: string;
  deletedDate: string;
  properties: InstructionLetterRequestPropertyPdf[];
  customerDetails: InstructionLetterCustomerDetails;
  panelValuer: PanelValuer;
  propertyName?: string;
  propertyDescription?: string;
  interest?: string;
  tenancy?: string;
  basisOfValuation?: string;
  additionalReportingRequirements?: string | null;
  customerFeeBasis?: boolean;
  feeBasisDescription?: string | null;
  updatedDate: string;
  createdDate: string;
  additionalValuationMethodology: boolean;
  additionalValuationMethodologies?: string[];
  creator: number;
};
export type TradingGroup = {
  tradingGroupId: string;
  regionName?: string;
  anzsic?: string;
  tradingGroupNumber: number;
  tradingGroupName: string;
  riskGroupId?: string | null;
  deleted: number;
  searchVector?: string | null;
  industry?: number | null;
};
export type Customer = {
  pk: number;
  key: number;
  entityName: string;
  customerId: number;
  customerSetCode: string;
  customerNumber: string;
  anzsicDescription: string;
  anzsic: string;
  segment: string;
  businessUnit: string;
  tradingGroup: TradingGroup | null;
  customerGroupId: number;
};
export type PaginatedCustomerList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: Customer[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type ClsEntity = {
  entityId: number;
  customerHierarchyId?: string | null;
  entityName: string;
  entityType: string;
  countryOfDomicile: string;
  anzsic: string;
  businessPortfolio: string;
  parentEntity?: number | null;
  customer?: number | null;
};
export type CustomerGroup = {
  id: number;
  customers: Customer[];
  entity: ClsEntity;
  anzsic: Anzsic;
  uid?: string | null;
  segment?: string | null;
  setCode?: string | null;
  branchCode?: string | null;
  businessUnit?: string | null;
  name: string;
};
export type PaginatedCustomerGroupList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: CustomerGroup[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type FacilitySettings = {
  endDate?: string | null;
};
export type Facility = {
  pk: string;
  accountSk: number;
  accountType: string;
  entityName: string;
  type: string;
  limit: string;
  limitDollars?: string;
  limitFloat: number;
  endYear?: string;
  maturityDate?: string;
  maturityYear?: string;
  annualAmortisation?: string;
  interestRate?: number;
  drawdownDate?: string;
  accountNumber: string;
  hostProductIdentifier: string;
  productName: string;
  projectId?: number;
  salesChannel: string;
  settings?: FacilitySettings;
};
export type BaseDistrictValuationRoll = {
  dipid: string | null;
  dvrId: number;
  valRef?: string | null;
  fullAddress?: string | null;
  lat: number;
  lng: number;
  addressId: number;
};
export type CustomerDistrictValuationRoll = {
  districtValuationRolls: number[];
};
export type Kpi = {
  id: number;
  statementId: string;
  periodTo: string;
  auditMethod: string;
  measure: string;
  value: string;
  label: string;
};
export type Benchmark = {
  year: string;
  lq: string | null;
  uq: string | null;
  median: string | null;
  target: string | null;
  count: number;
  measureKey: string;
  measureType: "$" | "%" | "x" | "days" | "ha" | "";
};
export type CustomerBalance = {
  id: string;
  customerName: string;
  accountNumber?: string | null;
  valueDate?: string | null;
  odLimit?: string | null;
  nzdBalance?: string | null;
  customer: number;
};
export type Emission = {
  id: number;
  relativeValue?: number;
  greenProjectAnnualCo2eTonnes?: number;
  totalIncome?: string;
  createdDatetime: string;
  deletedDatetime?: string | null;
  emissionType?: 0 | 1;
  startDatetime: string;
  endDatetime: string;
  scope1EmissionsCo2eTonnes: number;
  scope2EmissionsCo2eTonnes: number;
  scope3EmissionsCo2eTonnes: number;
  totalEmissionsCo2eTonnes: number;
  targetDatetime: string;
  baseDatetime: string;
  comments?: string | null;
  outputSource: number;
  verifier: number;
  customers: number[];
  categories: number[];
  focusAreas: number[];
};
export type TradingGroupEmission = {
  pseudoId: number;
  measure?: string | null;
  value?: string | null;
  effectiveArea?: string | null;
  totalFarmIncome?: string | null;
  totalAssets?: string | null;
  tradingGroup: string;
};
export type ValuationInspection = {
  pk: number;
  id: number;
  date?: string | null;
  name?: string;
  party?: "Frontline" | "Agri Valuations Manager";
  formattedDate: string;
  formattedDateLong: string;
};
export type LandClassType = {
  id: number;
  landClass: string;
  isProductive: boolean;
  useTotalValue?: boolean;
  color?: string;
  isJunk?: boolean | null;
};
export type ImprovementType = {
  id: number;
  improvementType: string;
  defaultMetricUnit: string;
  defaultMetricType?: "M2" | "CUSTOM" | "NOMINAL";
};
export type PlantingVarieties = {
  id: number;
  name: string;
};
export type HighestAndBestUseType = {
  id: number;
  validLandClasses: LandClassType[];
  validImprovementTypes: ImprovementType[];
  fallbackLandClass: LandClassType;
  plantingVarieties: PlantingVarieties[];
  bestUseCategory: string;
  highestAndBestUse: string;
  aepUnit: string;
  aepRounding?: number | null;
  aepPrecision?: number;
  categories: string[];
  plantingRegimes?: string[];
  plantingVarietiesOld?: string[];
  usesGrafting?: boolean;
  orchardTypes?: string[];
  coverTypes?: string[];
};
export type ValuationPriorMarketValue = {
  pk: number;
  value: string;
  valueDollars: string;
  assessmentType?: ("INTERNAL" | "RVR" | "SALE" | null) | null;
  changePercent: string;
  justification?: string | null;
  dateOfPriorAssessment?: string | null;
  formattedDateOfPriorAssessment: string;
};
export type RvrValuation = {
  rvrValuationId: number;
  rvr: number;
  externalValuer: string;
  primaryValuationApproach: string;
  valuationDate: string;
  rvrMarketValue?: number | null;
  internalValuationId: number | null;
  valuersAep?: number | null;
  completed?: boolean;
  tenure: string;
};
export type LimitedDistrictValuationRoll = {
  dvrId: number;
  valRef?: string | null;
  tlaName?: string | null;
  landZoneDesc?: string | null;
  landUseDesc?: string | null;
  cv?: number | null;
  iv?: number | null;
  lv?: number | null;
  landArea?: number | null;
  floorArea?: number | null;
  valDate?: string | null;
};
export type Valuation = {
  id: number;
  valuationReference?: string | null;
  valuationName?: string | null;
  valuationType: string;
  approachName: string;
  address?: number | null;
  creator: number;
  createdDate: string;
  editable: boolean;
  updatedDate: string;
  completedDate: string;
  deletedDate?: string | null;
  creatorName: string;
  tradingGroup: string;
  inspection: ValuationInspection;
  linkedToProspect?: boolean;
  fullAddress: string | null;
  frontlineCreated?: boolean;
  savedTitleAreaString: string;
  transferAddressId?: string | null;
  tradingGroupId?: string | null;
  tradingGroupName?: string | null;
  totalHa?: number | null;
  highestAndBestUseType: HighestAndBestUseType;
  highestAndBestUseTypeId?: number;
  addressId: number | null;
  valuationId: number;
  saleId: number;
  tier?: (1 | 2 | 3 | null) | null;
  totalSurveyArea: number;
  totalArea: number;
  elevationStep?: number;
  titleReviewStatus?: number | null;
  titleReviewComments?: string | null;
  benchmarkComparableSale?: number | null;
  marketValue: string;
  priorMarketValue: ValuationPriorMarketValue | null;
  rvrValuation?: RvrValuation;
  remarksAndActions?: string | null;
  remarksAndActionsReportInclude?: boolean;
  titleMatchingRatingValuation: LimitedDistrictValuationRoll;
  waterSecurityReviewStatus?: number | null;
  waterSecurityReviewComments?: string | null;
  hasKiwifruitBestUse: boolean;
  ccr?: (1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | null) | null;
  si?: ("A" | "B" | "C" | "D" | "E" | "F" | "G" | null) | null;
  lvr?: number | null;
};
export type CustomerReportTemplate = {
  id?: number;
  createdDatetime?: string;
  data: {
    [key: string]: any;
  };
  name: string;
  segment: string;
};
export type PatchedCustomerReportTemplate = {
  id?: number;
  createdDatetime?: string;
  data?: {
    [key: string]: any;
  };
  name?: string;
  segment?: string;
};
export type FieldChoice = {
  name: string;
  value: string;
};
export type FieldConstraint = {
  blank?: boolean;
  choices?: FieldChoice[];
  dbConstraint?: boolean;
  dbIndex?: boolean;
  decimalPlaces?: number;
  geomType?: string;
  maxDigits?: number;
  maxLength?: number;
  null?: boolean;
  oneToOne?: boolean;
  primaryKey?: boolean;
  spatialIndex?: boolean;
  srid?: number;
  unique?: boolean;
};
export type FieldDefinition = {
  helpText?: string;
  column?: string;
  name?: string;
  suffix?: string;
  verboseName?: string;
  isPii?: string;
  piiType?: string;
  integrity?: string;
  confidentiality?: string;
  constraints?: FieldConstraint;
  fieldType?: string;
  djangoFieldType?: string;
  relatedModel?: string;
};
export type ModelDefinition = {
  modelKey?: string;
  fields?: FieldDefinition[];
  modelName?: string;
  app?: string;
  dbTable?: string;
  deprecated?: boolean;
  provider?: string;
  url?: string;
  name?: string;
  license?: string;
  description?: string;
  omit?: boolean;
  managed?: boolean;
  mermaid?: string;
  exportTable?: string;
};
export type FormField = {
  name: string;
  tooltip: string;
  label: string;
  options?: SelectOption[];
  suffix?: string;
};
export type PatchedEmission = {
  id?: number;
  relativeValue?: number;
  greenProjectAnnualCo2eTonnes?: number;
  totalIncome?: string;
  createdDatetime?: string;
  deletedDatetime?: string | null;
  emissionType?: 0 | 1;
  startDatetime?: string;
  endDatetime?: string;
  scope1EmissionsCo2eTonnes?: number;
  scope2EmissionsCo2eTonnes?: number;
  scope3EmissionsCo2eTonnes?: number;
  totalEmissionsCo2eTonnes?: number;
  targetDatetime?: string;
  baseDatetime?: string;
  comments?: string | null;
  outputSource?: number;
  verifier?: number;
  customers?: number[];
  categories?: number[];
  focusAreas?: number[];
};
export type EmissionPercentile = {
  pct10th: string;
  pct20th: string;
  pct30th: string;
  pct40th: string;
  pct50th: string;
  pct60th: string;
  pct70th: string;
  pct80th: string;
  pct90th: string;
  customerPercentile: number;
  min: string;
  max: string;
  customerEmissions: string;
  lowerQuartile: string;
  median: string;
  upperQuartile: string;
  count: number;
  anzsic: string;
  anzsicDescription: string;
  region: string;
};
export type EmissionFormField = {
  name: string;
  tooltip: string;
  label: string;
  options?: SelectOption[];
  suffix?: string;
};
export type PartialEmission = {
  x: string;
  y: string;
  isTarget: number;
};
export type EntityGroup = {
  entityId: string;
  entityName: string;
  entityNumber: string;
  groupType: "customer_group" | "trading_group";
};
export type Event = {
  eventName: string;
  kwargs?: {
    [key: string]: any;
  } | null;
};
export type FrontendErrorEvent = {
  pageUrl: string;
  error: string;
  stacktrace: string;
  componentStack: string;
};
export type PageLoadEvent = {
  pageUrl?: string | null;
};
export type MapTitleFeature = {
  type: "Feature";
  geometry: {
    type: "MultiPolygon";
    coordinates: number[][][][];
  };
  properties: {
    id: number;
    estateDescription?: string | null;
    titleNo: string;
    mortgagee?: string;
    owners?: string;
    area: number;
    areaString: string;
    type?: string | null;
  };
};
export type MapTitleFeatureList = {
  type?: "FeatureCollection";
  features?: MapTitleFeature[];
};
export type LoanSettings = {
  endDate?: string | null;
};
export type WritableLoan = {
  pk: number;
  accountSk: number;
  settings: LoanSettings;
};
export type PatchedWritableLoan = {
  pk?: number;
  accountSk?: number;
  settings?: LoanSettings;
};
export type Username = {
  username: string;
  id: number;
  pk: number;
  firstName?: string;
  name: string;
  lastName?: string;
  title: string;
  mobileNumber: string;
  email?: string;
};
export type NestedWritableValuation = {
  pk: number;
  highestAndBestUseType?: number | null;
};
export type SalePropertyDescription = {
  saleCommentId: number;
  location?: string | null;
  climate?: string | null;
  landClass?: string | null;
  improvements?: string | null;
  marketCircumstances?: string | null;
  other?: string | null;
  searchVector?: string | null;
};
export type FrontlineSale = {
  key: string;
  id: string;
  pk: number;
  askingPrice: number | null;
  assigned?: Username | null;
  attachments: string[];
  cv: string;
  cvPerHa: string;
  effectiveHa?: number | null;
  fullAddress: string | null;
  valuation: NestedWritableValuation;
  highestAndBestUse: string;
  irrigation?: string | null;
  irrigationPrimaryApplication?: string | null;
  irrigationSource?: string | null;
  listingCloseDate: string | null;
  region?: string | null;
  salePropertyDescription: SalePropertyDescription;
  saleAgent?: string | null;
  saleAgency?: string | null;
  saleDate: string;
  saleDateString: string;
  saleMechanism?: string | null;
  stale?: boolean;
  totalHa?: number | null;
  uploads: Attachment[];
  url?: string;
  userActions?: string | null;
  vendor?: string | null;
  vendorBank?: string | null;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
};
export type PatchedFrontlineSale = {
  key?: string;
  id?: string;
  pk?: number;
  askingPrice?: number | null;
  assigned?: Username | null;
  attachments?: string[];
  cv?: string;
  cvPerHa?: string;
  effectiveHa?: number | null;
  fullAddress?: string | null;
  valuation?: NestedWritableValuation;
  highestAndBestUse?: string;
  irrigation?: string | null;
  irrigationPrimaryApplication?: string | null;
  irrigationSource?: string | null;
  listingCloseDate?: string | null;
  region?: string | null;
  salePropertyDescription?: SalePropertyDescription;
  saleAgent?: string | null;
  saleAgency?: string | null;
  saleDate?: string;
  saleDateString?: string;
  saleMechanism?: string | null;
  stale?: boolean;
  totalHa?: number | null;
  uploads?: Attachment[];
  url?: string;
  userActions?: string | null;
  vendor?: string | null;
  vendorBank?: string | null;
  geometry?:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
};
export type PatchedReadonlyInstructionLetterRequest = {
  id?: number;
  panelValuer?: PanelValuer;
  properties?: InstructionLetterRequestProperty[];
  customerDetails?: InstructionLetterCustomerDetails;
  letterType?: string;
  propertyName?: string;
  propertyDescription?: string;
  interest?: string;
  tenancy?: string;
  basisOfValuation?: string;
  additionalReportingRequirements?: string | null;
  dateRequired?: string | null;
  customerFeeBasis?: boolean;
  feeBasisDescription?: string | null;
  updatedDate?: string;
  createdDate?: string;
  valuationManager?: number;
  creator?: number;
};
export type AnzUnion = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    area: number;
    contour?: number | null;
    vegetation?: number | null;
    ps?: string | null;
    luc?: string | null;
  };
};
export type AnzUnionList = {
  type?: "FeatureCollection";
  features?: AnzUnion[];
};
export type LineageNodeValue = {
  title: string;
  type: string;
};
export type LineageNode = {
  id: string;
  value: LineageNodeValue;
};
export type LineageEdge = {
  source: string;
  target: string;
};
export type DataLineage = {
  nodes: LineageNode[];
  edges: LineageEdge[];
};
export type Location = {
  id: number;
  lat: number;
  lng: number;
  owners: string[];
  legalIdentifier?: string | null;
  territorialUnit0?: string | null;
  territorialUnit1?: string | null;
  territorialUnit2?: string | null;
  territorialUnit3?: string | null;
  territorialUnit4?: string | null;
  territorialUnit5?: string | null;
  propertyType?: string | null;
  propertyZoning?: string | null;
  propertyStatus?: string | null;
  assetPortfolio?: string | null;
  anzsic?: string | null;
  ccr?: string | null;
  si?: string | null;
  roofConstruction?: string | null;
  assetClass?: string | null;
  customerSegment?: string | null;
  buildingAge?: number | null;
  buildingFloorArea?: number | null;
  wallConstruction?:
    | (
        | "Mixed Materials"
        | "Plastic"
        | "Roughcast"
        | "Malthoid"
        | "Tiles"
        | "Aluminium"
        | "Fibrolite"
        | "Unknown"
        | "Wood"
        | "Glass"
        | "Brick"
        | "Concrete"
        | "Stone"
        | "Iron"
        | null
      )
    | null;
  cv?: string | null;
  lv?: string | null;
  iv?: string | null;
  landArea?: string | null;
  valocityPropertyClass?: string | null;
  anzPropertyClass?: string | null;
  setCode?: string | null;
  identifier?: string | null;
  titleIdentifier?: string | null;
  fullAddress?: string | null;
  updatedDate: string;
  collateral?: number | null;
  address?: number | null;
  riskGroup?: string | null;
};
export type PatchedLocation = {
  id?: number;
  lat?: number;
  lng?: number;
  owners?: string[];
  legalIdentifier?: string | null;
  territorialUnit0?: string | null;
  territorialUnit1?: string | null;
  territorialUnit2?: string | null;
  territorialUnit3?: string | null;
  territorialUnit4?: string | null;
  territorialUnit5?: string | null;
  propertyType?: string | null;
  propertyZoning?: string | null;
  propertyStatus?: string | null;
  assetPortfolio?: string | null;
  anzsic?: string | null;
  ccr?: string | null;
  si?: string | null;
  roofConstruction?: string | null;
  assetClass?: string | null;
  customerSegment?: string | null;
  buildingAge?: number | null;
  buildingFloorArea?: number | null;
  wallConstruction?:
    | (
        | "Mixed Materials"
        | "Plastic"
        | "Roughcast"
        | "Malthoid"
        | "Tiles"
        | "Aluminium"
        | "Fibrolite"
        | "Unknown"
        | "Wood"
        | "Glass"
        | "Brick"
        | "Concrete"
        | "Stone"
        | "Iron"
        | null
      )
    | null;
  cv?: string | null;
  lv?: string | null;
  iv?: string | null;
  landArea?: string | null;
  valocityPropertyClass?: string | null;
  anzPropertyClass?: string | null;
  setCode?: string | null;
  identifier?: string | null;
  titleIdentifier?: string | null;
  fullAddress?: string | null;
  updatedDate?: string;
  collateral?: number | null;
  address?: number | null;
  riskGroup?: string | null;
};
export type LocationAttribute = {
  value: string;
  label: string;
};
export type LocationSearch = {
  id: number;
  lat: number;
  lng: number;
  fullAddress?: string | null;
};
export type SolarInstaller = {
  label: string;
  value: number;
};
export type CarbonAssetType = {
  value: number;
  label: string;
};
export type LossModelPerilCategory = {
  id: number;
  defaultLossModel?: number | null;
};
export type LossModelStep = {
  id: number;
  stepLabel?: string | null;
  color?: string | null;
  startValue?: number | null;
  endValue?: number | null;
  lossValue?: number;
};
export type LossModelFavourite = {
  pk: number;
  enabled: boolean;
};
export type LossModel = {
  id: number;
  category: LossModelPerilCategory;
  creatorName: string;
  label: string;
  steps: LossModelStep[];
  createdDatetime: string;
  updatedDatetime: string;
  favourites?: LossModelFavourite[];
  isCategoryDefault: boolean;
  isFavourite: boolean;
  name: string;
  descriptionHtml?: string | null;
  description?: string | null;
  editable?: boolean;
  annotations?: {
    [key: string]: any;
  } | null;
};
export type PatchedLossModelStep = {
  id?: number;
  stepLabel?: string | null;
  color?: string | null;
  startValue?: number | null;
  endValue?: number | null;
  lossValue?: number;
};
export type PatchedLossModel = {
  id?: number;
  category?: LossModelPerilCategory;
  creatorName?: string;
  label?: string;
  steps?: LossModelStep[];
  createdDatetime?: string;
  updatedDatetime?: string;
  favourites?: LossModelFavourite[];
  isCategoryDefault?: boolean;
  isFavourite?: boolean;
  name?: string;
  descriptionHtml?: string | null;
  description?: string | null;
  editable?: boolean;
  annotations?: {
    [key: string]: any;
  } | null;
};
export type News = {
  newsId: number;
  creatorName: string;
  creator?: number;
  createdDatetime: string;
  publishedDatetime?: string | null;
  title?: string | null;
  content?: string | null;
};
export type PatchedNews = {
  newsId?: number;
  creatorName?: string;
  creator?: number;
  createdDatetime?: string;
  publishedDatetime?: string | null;
  title?: string | null;
  content?: string | null;
};
export type Notification = {
  id: number;
  new: boolean;
  text?: string;
  title?: string;
  pinned?: boolean;
  type?: string;
  createdDate: string;
  updatedDate: string;
  user: number;
};
export type PaginatedNotificationList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: Notification[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type PanelPropertyType = {
  id: number;
  category?: string | null;
  name: string;
};
export type PanelValuerRuralSpecialisation = {
  id: number;
  specialisation: string;
  position: number;
};
export type PatchedPanelValuer = {
  id?: number;
  valuationFirm?: ValuationFirm;
  competenceRegions?: PanelValuerRegion[];
  postalAddress?: string;
  landlinePhone?: string;
  mobilePhone?: string;
  attachments?: Attachment[];
  newAttachments?: string[];
  currentAge?: number;
  hasExpiredInsurance?: boolean;
  apc?: boolean;
  commercialSpecialisations?: PanelValuerCommercialSpecialisation[];
  ruralSpecialisations?: number[];
  name?: string;
  state?: "abeyance" | "draft" | "published";
  valuerType?: "commercial" | "rural";
  classification?: "Enhanced Review" | "Unlimited";
  officeAddressText?: string;
  postalAddressText?: string;
  addressSearchVector?: string;
  email?: string;
  internalComments?: string;
  updatedDate?: string;
  createdDate?: string;
  deletedDate?: string | null;
  deletionReason?: string | null;
  valuationRegistrationDate?: string | null;
  dateOfBirth?: string | null;
  piAggregateLimitM?: string;
  piPerClaimLimitM?: string;
  piCocExpiryDate?: string | null;
  plGlAggregateLimitM?: string;
  plGlPerClaimLimitM?: string;
  plGlCocExpiryDate?: string | null;
};
export type Peril = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    value?: number | null;
    asAtDatetime: string;
    perilType: number;
  };
};
export type PerilList = {
  type?: "FeatureCollection";
  features?: Peril[];
};
export type CategoryStatistic = {
  color: string;
  lossCategory: string;
  impactedCountPct: number;
  impactedCount: number;
  lossCv: string;
};
export type Histogram = {
  label: string;
  value: number;
};
export type PhysicalRiskImpactedAddress = {
  identifier: string;
  legalIdentifier: string;
  locationId: number;
  cv: string;
  lossCv: string;
  lossPct: string;
  fullAddress: string;
  assetPortfolio: string;
  territorialUnit0: string;
  territorialUnit1: string;
  territorialUnit2: string;
  territorialUnit3: string;
  territorialUnit4: string;
  territorialUnit5: string;
  customerGroup: string;
};
export type PaginatedPhysicalRiskImpactedAddressList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: PhysicalRiskImpactedAddress[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type RiskGroup = {
  id: number;
  uid?: string | null;
  lending: string;
  cv: string;
  lossCv?: string;
  groupName: string;
  segment: string;
  anzsic: string;
};
export type PaginatedRiskGroupList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: RiskGroup[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type BaseLocation = {
  id: number;
  lat: number;
  lng: number;
};
export type MermaidGraph = {
  md: string;
};
export type PerilMvtStatistics = {
  minValue: number;
  maxValue: number;
  lq: number;
  uq: number;
  median: number;
  definition: {
    [key: string]: any;
  };
};
export type PhysicalRiskImpactSummaryItem = {
  label: string;
  anzCount?: number;
  totalAnzCount?: number;
  anzCvSum?: string;
  totalAnzCvSum?: string;
  lossAnzCvSum?: string;
};
export type PhysicalRiskImpactedSummary = {
  results: PhysicalRiskImpactSummaryItem[];
  lending?: string;
  totalLending?: string;
};
export type PerilCategory = {
  id: number;
  value: number;
  key: string;
  label: string;
  measurement?: string | null;
  definition?: {
    [key: string]: any;
  } | null;
  description?: string | null;
  source?: number | null;
  defaultLossModel?: number | null;
};
export type PerilSource = {
  id: number;
  key: string;
  label: string;
};
export type PerilDefinition = {
  value: number;
  label: string;
};
export type PerilType = {
  value: number;
  label: string;
  category?: string;
  source?: string;
  description?: string | null;
  definition?: PerilDefinition[];
  categoryDescription?: string;
};
export type PartialGreenProject = {
  id?: number | null;
  name?: string | null;
  countOfLoans: number;
  finalizedDate?: string | null;
  costTotal?: string | null;
  proposedGreenLoanAmount?: string | null;
  creatorName?: string | null;
  categoryName?: string | null;
  subcategoryNames: string[];
  fullAddresses: string[];
  customerFullNames: string[];
  proposedLvr: string;
  startDatetime?: string | null;
  completionDatetime?: string | null;
  status: "draft" | "in_progress" | "pending" | "complete";
};
export type PaginatedPartialGreenProjectList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: PartialGreenProject[];
};
export type LatLng = {
  lng: string;
  lat: string;
};
export type GreenAsset = {
  type: "Feature";
  id: number;
  geometry: {
    type: "GeometryCollection";
    geometries: (
      | {
          type: "Point";
          coordinates: number[];
        }
      | {
          type: "LineString";
          coordinates: number[][];
        }
      | {
          type: "Polygon";
          coordinates: number[][][];
        }
      | {
          type: "MultiPoint";
          coordinates: number[][];
        }
      | {
          type: "MultiPolygon";
          coordinates: number[][][][];
        }
      | {
          type: "MultiLineString";
          coordinates: number[][][];
        }
      | {
          type: "GeometryCollection";
          geometries: any[];
        }
    )[];
  };
  properties: {
    project: number;
    projectId?: number | null;
    categories?: number[];
    categoryNames?: string[] | null;
    creatorName?: string | null;
    predominantSpeciesPlantedName?: string | null;
    areaPlanted: string | null;
    createdDatetime: string;
    updatedDatetime: string;
    baselineSource?: ("Peer Comparison" | "Historical Data" | null) | null;
    baselineSyntheticFertiliserUsedTonnes?: string | null;
    expectedSyntheticFertiliserUsedTonnes?: string | null;
    baselineAnnualKwh?: string | null;
    expectedAnnualKwh?: string | null;
    expectedAnnualTonnesCo2e?: string | null;
    baselineAnnualTonnesCo2e?: string | null;
    expectedAnnualTonnesCo2eReduced?: string | null;
    floorAreaM2?: string | null;
    wasteRemovedInConstructionTonnes?: string | null;
    wasteMinimisedInConstructionTonnes?: string | null;
    renewableEnergyGeneratedOnsiteKwh?: string | null;
    renewableEnergyGeneratedType?:
      | ("Solar" | "Hydro" | "Wind" | "Other" | null)
      | null;
    percentageRenewableEnergyGeneratedOnsite?: string | null;
    currentDaysStorage?: number | null;
    baselineNDwellingsServiced?: number | null;
    baselineNDwellingsOccupants?: number | null;
    expectedDaysStorage?: number | null;
    nitrogenFertiliserAppliedT?: string | null;
    nitrogenFertiliserAppliedKgHaPa?: string | null;
    currentEffluentSpreadingAreaHa?: string | null;
    currentStorageCapacityM3?: string | null;
    expectedVolumeOfWastewaterTreatedM3?: string | null;
    expectedNitrogenFertiliserAppliedT?: string | null;
    expectedNitrogenFertiliserAppliedKgHaPa?: string | null;
    expectedEffluentSpreadingAreaHa?: string | null;
    expectedStorageCapacityTPa?: string | null;
    failSafeAlarmInstalledEvidence?: boolean | null;
    wastewaterDischargeTreatmentEvidence?: boolean | null;
    baselineVolumeWasteWaterDischargedM3?: string | null;
    baselineVolumeWasteWaterCapturedM3?: string | null;
    typeOfWasteWaterCaptured?: string | null;
    currentWasteWaterDischargedM3?: string | null;
    expectedReductionOfWaterUsedM3?: string | null;
    annualWaterUseSqm?: string | null;
    emissionReductionTonnesCo2e?: string | null;
    annualCo2eTonnesReducedOrAvoided?: string | null;
    annualKwhReducedOrAvoided?: string | null;
    annualKwhReductionAsPercentageOfNormalEnergyUse?: string | null;
    insulationArea?: string | null;
    glazingType?: string | null;
    ledWattage?: string | null;
    baselineAnnualEmissionsCo2eKgKwh?: string | null;
    expectedAnnualEmissionsCo2eKgKwh?: string | null;
    previousFuelSourceUsedAnnually?: string | null;
    baselinePreviousFuelUsageL?: string | null;
    previousFuelSource?:
      | (
          | "Coal"
          | "Natural Gas"
          | "Diesel"
          | "Petrol"
          | "Biofuel"
          | "Other"
          | null
        )
      | null;
    previousFuelType?:
      | (
          | "Regular petrol"
          | "Premium petrol"
          | "Diesel"
          | "LPG"
          | "Other"
          | null
        )
      | null;
    replacementEnergyAssetOtherDescription?: string | null;
    outputCapacityKw?: string | null;
    renewableEnergyGeneratedKwh?: string | null;
    heatPumpRatingKw?: string | null;
    useSeer?: boolean;
    seerHeatingRating?: string | null;
    seerCoolingRating?: string | null;
    acopRating?: string | null;
    aeerRating?: string | null;
    copRating?: string | null;
    iplvRating?: string | null;
    eerRating?: string | null;
    transportationMake?: string | null;
    transportationModel?: string | null;
    hectaresPlanted?: string | null;
    expectedNonEffectiveArea?: string | null;
    expectedEffectiveArea?: string | null;
    plantingDate?: string | null;
    speciesPlanted?: string | null;
    countOfSpecies?: string | null;
    pestAndMaintenaceStrategyEvidenced?: boolean | null;
    rightTreeRightPlaceEvidenced?: boolean | null;
    nesPfEvidenced?: boolean | null;
    baselineNLossKgHa?: string | null;
    expectedNLossKgHa?: string | null;
    baselinePLossKgHa?: string | null;
    expectedPLossKgHa?: string | null;
    baselineStorageCapacityM3?: string | null;
    expectedStorageCapacityM3?: string | null;
    currentAnnualProcessingCapacityL?: string | null;
    ageOfReplacedSystemYears?: string | null;
    monthsOfYearStockKeptOffPaddock?: string | null;
    estimatedLeachateCaptured?: string | null;
    leachateTreatmentEvidence?: boolean | null;
    bunker50mFromWaterEvidence?: boolean | null;
    waterUseRecordedDailyEvidence?: boolean | null;
    soilProbesUsedEvidence?: boolean | null;
    monthsStockKeptOffPaddock?:
      | (
          | "jan"
          | "feb"
          | "mar"
          | "apr"
          | "may"
          | "jun"
          | "jul"
          | "aug"
          | "sep"
          | "oct"
          | "nov"
          | "dec"
        )[]
      | null;
    lengthOfWaterwaysM?: string | null;
    capacityOfInstalledWaterReticulationTanksM3?: string | null;
    waterStorageCapacityM3?: string | null;
    numberOfStockPreventedFromWaterways?: string | null;
    numberOfStockUnitsPriorToProject?: string | null;
    numberOfStockUnitsAtCompletion?: string | null;
    numberOfStockKeptOffPaddock?: string | null;
    hybridVehicleEmissionsGco2Km?: string | null;
    chargerModel?: string | null;
    chargerCapacityKw?: string | null;
    baselineVolumeOfWasteProcessedM3?: string | null;
    volumeOfWasteDivertedFromLandfillM3?: string | null;
    volumeOfWasteRecycledRecoveredReusedAsAPercentageOfTotalWasteProcessed?:
      | string
      | null;
    typeOfWasteSorted?: string | null;
    typeOfWasteTreated?: string | null;
    typeOfWasteRecycled?: string | null;
    baselineVolumeOfRecyclableCollectedM3?: string | null;
    baselineVolumeOfRecyclableStoredM3?: string | null;
    baselineVolumeOfWasteSortedM3?: string | null;
    baselineVolumeOfWasteTreatedM3?: string | null;
    baselineVolumeOfWasteRecycledM3?: string | null;
    expectedVolumeOfWasteSortedM3?: string | null;
    expectedVolumeOfTreatedWastewaterDischargedM3?: string | null;
    expectedVolumeOfWasteTreatedM3?: string | null;
    expectedVolumeOfWasteRecycledM3?: string | null;
    baselineVolumeOfWasteProducedPriorToProjectM3?: string | null;
    wasteProcessingEquipmentDescription?: string | null;
    volumeOfWasteRecycledRecoveredReusedM3?: string | null;
    typeOfRecyclableCollected?: string | null;
    typeOfRecyclableStored?: string | null;
    expectedVolumeOfRecyclableCollectedM3?: string | null;
    expectedVolumeOfRecyclableStoredM3?: string | null;
    volumeOfWasteReducedAfterPlantOrEquipmentReplacementPercentage?:
      | string
      | null;
    volumeOfWasteProducedBeforeReplacementM3?: string | null;
    volumeOfWasteProducedAfterReplacementM3?: string | null;
    baselineAnnualWaterUseSqm?: string | null;
    expectedAnnualWaterUseSqm?: string | null;
    renewableEnergyProducedKwh?: string | null;
    typeOfVehicleUsedForCollection?: string | null;
    collectionVehicleFossilFuelPowered?: ("Yes" | "No" | null) | null;
    wasteReductionDescription?: string | null;
    hybridVehicleEmissionsSource?: string | null;
    expectedAnnualProcessingCapacityL?: string | null;
    baselineWaterUseM3?: string | null;
    deletedDatetime?: string | null;
    predominantSpeciesPlanted?: number | null;
    seanzInstaller?: number | null;
  };
};
export type CustomerOption = {
  value: number;
  label: string;
  fullName: string;
  customerNumber?: string | null;
  customerType: "CUSTOMER" | "ORGANIZATION";
};
export type AddressOption = {
  value: string;
  label: string;
};
export type LinzTitlesOption = {
  value?: number | null;
  label?: string | null;
};
export type LoanOption = {
  value?: number | null;
  label?: string | null;
};
export type GreenProject = {
  id?: number | null;
  originalCreator: number;
  creatorName?: string | null;
  creatorFullName?: string | null;
  creatorEmail?: string | null;
  categoryName?: string | null;
  proposedEquity: string;
  proposedLvr: string;
  centroid?: LatLng | null;
  customers?: number[];
  customerAddresses?: number[];
  loans?: number[];
  addresses?: number[];
  titles?: number[];
  greenAsset?: GreenAsset | null;
  customersData: CustomerOption[];
  addressesData: AddressOption[];
  customerAddressesData: AddressOption[];
  titlesData: LinzTitlesOption[];
  loansData: LoanOption[];
  subcategoriesData: string[];
  isAttachmentRequired: string;
  createdDatetime: string;
  updatedDatetime: string;
  name?: string | null;
  description?: string | null;
  finalizedDate?: string | null;
  deletedDatetime?: string | null;
  status: "draft" | "in_progress" | "pending" | "complete";
  startDatetime?: string | null;
  completionDatetime?: string | null;
  proposedGreenLoanAmount?: string | null;
  proposedLoanAmount?: string | null;
  costTotal?: string | null;
  environmentalStrategy?: string | null;
  environmentalBenefits?: string | null;
  environmentalEvidence?: boolean | null;
  effectiveAreaHa?: string | null;
  nonEffectiveAreaHa?: string | null;
  totalAreaHa?: string | null;
  primaryCustomerType?: ("Wholesale" | "Retail" | null) | null;
  screeningToolCompleted?: boolean | null;
  retailAttestationCompleted?: boolean | null;
  category?: number | null;
  subcategories: number[];
};
export type PatchedGreenProject = {
  id?: number | null;
  originalCreator?: number;
  creatorName?: string | null;
  creatorFullName?: string | null;
  creatorEmail?: string | null;
  categoryName?: string | null;
  proposedEquity?: string;
  proposedLvr?: string;
  centroid?: LatLng | null;
  customers?: number[];
  customerAddresses?: number[];
  loans?: number[];
  addresses?: number[];
  titles?: number[];
  greenAsset?: GreenAsset | null;
  customersData?: CustomerOption[];
  addressesData?: AddressOption[];
  customerAddressesData?: AddressOption[];
  titlesData?: LinzTitlesOption[];
  loansData?: LoanOption[];
  subcategoriesData?: string[];
  isAttachmentRequired?: string;
  createdDatetime?: string;
  updatedDatetime?: string;
  name?: string | null;
  description?: string | null;
  finalizedDate?: string | null;
  deletedDatetime?: string | null;
  status?: "draft" | "in_progress" | "pending" | "complete";
  startDatetime?: string | null;
  completionDatetime?: string | null;
  proposedGreenLoanAmount?: string | null;
  proposedLoanAmount?: string | null;
  costTotal?: string | null;
  environmentalStrategy?: string | null;
  environmentalBenefits?: string | null;
  environmentalEvidence?: boolean | null;
  effectiveAreaHa?: string | null;
  nonEffectiveAreaHa?: string | null;
  totalAreaHa?: string | null;
  primaryCustomerType?: ("Wholesale" | "Retail" | null) | null;
  screeningToolCompleted?: boolean | null;
  retailAttestationCompleted?: boolean | null;
  category?: number | null;
  subcategories?: number[];
};
export type ProjectRule = {
  detail: string;
  result: boolean;
  phase: "Pre-Drawdown" | "Post-Drawdown";
  approvalType?: "ENV_HELD" | "SIGN_OFF" | "EVID_IMGD" | "";
};
export type HistoryChange = {
  field: string;
  newValue: string;
  oldValue: string;
};
export type ProjectHistory = {
  id: number;
  datetime: string;
  user: string;
  userName: string;
  changes: HistoryChange[];
};
export type PaginatedProjectHistoryList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: ProjectHistory[];
};
export type GreenAnnualReporting = {
  id: number;
  createdDatetime: string;
  updatedDatetime: string;
  renewableEnergyGeneratedAnuallyKwh?: string | null;
  annualEnergyUseKwhM2?: string | null;
  annualEnergyUseKwh?: string | null;
  annualTonnesCo2e?: string | null;
  annualTonnesCo2eM2?: string | null;
  annualEmissionsCo2eTonnes?: string | null;
  annualSequestrationCo2eTonnes?: string | null;
  annualNLossKgHa?: string | null;
  annualPLossKgHa?: string | null;
  complianceCertificateReceived?: boolean | null;
  ownershipConfirmed?: boolean | null;
  annualWasteDivertedFromLandfillM3?: string | null;
  annualEmissionsAvoidedCo2eTonnes?: string | null;
  q1?: ("Yes" | "No" | null) | null;
  q2?: ("Yes" | "No" | null) | null;
  q2a?: ("Yes" | "No" | null) | null;
  q2b?: ("Yes" | "No" | null) | null;
  q3?: ("Yes" | "No" | null) | null;
  q3a?: string | null;
  q4?: ("Yes" | "No" | null) | null;
  q5?: boolean | null;
  deletedDatetime?: string | null;
  periodDatetime: string;
  evidenceImaged?: boolean;
  project: number;
};
export type PatchedGreenAnnualReporting = {
  id?: number;
  createdDatetime?: string;
  updatedDatetime?: string;
  renewableEnergyGeneratedAnuallyKwh?: string | null;
  annualEnergyUseKwhM2?: string | null;
  annualEnergyUseKwh?: string | null;
  annualTonnesCo2e?: string | null;
  annualTonnesCo2eM2?: string | null;
  annualEmissionsCo2eTonnes?: string | null;
  annualSequestrationCo2eTonnes?: string | null;
  annualNLossKgHa?: string | null;
  annualPLossKgHa?: string | null;
  complianceCertificateReceived?: boolean | null;
  ownershipConfirmed?: boolean | null;
  annualWasteDivertedFromLandfillM3?: string | null;
  annualEmissionsAvoidedCo2eTonnes?: string | null;
  q1?: ("Yes" | "No" | null) | null;
  q2?: ("Yes" | "No" | null) | null;
  q2a?: ("Yes" | "No" | null) | null;
  q2b?: ("Yes" | "No" | null) | null;
  q3?: ("Yes" | "No" | null) | null;
  q3a?: string | null;
  q4?: ("Yes" | "No" | null) | null;
  q5?: boolean | null;
  deletedDatetime?: string | null;
  periodDatetime?: string;
  evidenceImaged?: boolean;
  project?: number;
};
export type Memorial = {
  fid: number;
  id: number;
  landDistrict?: string | null;
  memorialText?: string | null;
  current?: string | null;
  instrumentNumber?: string | null;
  instrumentLodgedDatetime?: string | null;
  instrumentType?: string | null;
  encumbrancees?: string | null;
  estimatedMortgagee?: string | null;
  mortgageInstrumentNumber?: string | null;
  deletedDate?: string | null;
  title: string;
};
export type Title = {
  type: "Feature";
  id: number;
  geometry: {
    type: "MultiPolygon";
    coordinates: number[][][][];
  };
  properties: {
    geometry: {
      [key: string]: any;
    };
    memorials: Memorial[] | null;
    totalArea?: string | null;
    surveyAreaHa: string;
    mortgagee?: string | null;
    fullAddress?: string | null;
    id: number;
    titleNo: string;
    status?: string | null;
    type?: string | null;
    landDistrict?: string | null;
    issueDate?: string | null;
    guaranteeStatus?: string | null;
    estateDescription?: string | null;
    owners?: string | null;
    spatialExtentsShared?: string | null;
    createdDate?: string | null;
    updatedDate?: string | null;
    deletedDate?: string | null;
    titleGeo?: {
      [key: string]: any;
    } | null;
    area: number;
    surveyArea: string;
  };
};
export type TitleList = {
  type?: "FeatureCollection";
  features?: Title[];
};
export type GreenProjectFeature = {
  id: number;
  lat: number;
  lng: number;
  categoryName: string;
  currentBalance: string;
};
export type CardItem = {
  suffix: string;
  name: string[];
  label?: string;
};
export type CardTemplate = {
  contactDetails: CardItem[];
  projectInformation: CardItem[];
  projectCosts: CardItem[];
  environmentalBenefits: CardItem[];
  products: CardItem[];
  baselineData: CardItem[];
  environmentalImpact: CardItem[];
};
export type Rule = {
  required?: boolean;
  message: string;
};
export type ConditionalRuleCondition = {
  field: string[];
  inputType: string;
  condition: string;
  values: number[];
};
export type ConditionalRules = {
  noValidationWhen: ConditionalRuleCondition[];
};
export type FieldTemplate = {
  name: string[];
  label?: string;
  rules?: Rule[];
  conditionalRules?: ConditionalRules;
  addonAfter?: string;
  tooltip?: string;
  options?: SelectOption[];
};
export type FormTemplate = {
  cards: CardTemplate;
  fields: FieldTemplate[];
};
export type GreenBuildingsImpact = {
  assetCategories: string;
  annualKwhM2Delta: string;
  annualTonnesCo2eM2Delta: string;
  annualWaterUseSqm: string;
  wasteRemovedInConstructionTonnes: string;
  wasteMinimisedInConstructionTonnes: string;
  renewableEnergyGeneratedKwh: string;
};
export type EnergyEfficiencyImpact = {
  assetCategories: string;
  annualKwhReducedOrAvoided: string;
  outputCapacityKw: string;
  renewableEnergyGeneratedKwh: string;
  emissionReductionTonnesCo2e: string;
  annualCo2eTonnesReducedOrAvoided: string;
};
export type CleanTransportationImpact = {
  assetCategories: string;
  annualCo2eTonnesReducedOrAvoided: string;
};
export type SustainableWaterAndWastewaterImpact = {
  assetCategories: string;
  expectedNLossKgHaDelta: string;
  expectedPLossKgHaDelta: string;
  expectedStorageCapacityM3Delta: string;
  expectedSyntheticFertiliserUsedTonnesDelta: string;
  expectedReductionOfWaterUsedM3: string;
  expectedAnnualTonnesCo2eDelta: string;
  capacityOfInstalledWaterReticulationTanksM3: string;
  numberOfStockPreventedFromWaterways: number;
  waterStorageCapacityM3: string;
  waterEfficiencyWaterSavedM3: string;
  annualTonnesCo2eReduced: string;
  expectedAnnualProcessingCapacityL: string;
  lengthOfWaterwaysM: string;
};
export type RenewableEnergyImpact = {
  assetCategories: string;
  outputCapacityKw: string;
  renewableEnergyGeneratedKwh: string;
  emissionReductionTonnesCo2e: string;
};
export type SustainableLandUseImpact = {
  assetCategories: string;
  years: string[];
  hectaresPlanted: string;
  carbonTonnes: string[];
};
export type PollutionPreventionAndControlImpact = {
  assetCategories: string;
  annualCo2eTonnesReducedOrAvoided: string;
};
export type GreenProjectImpactSummary = {
  greenBuildings: GreenBuildingsImpact;
  energyEfficiency: EnergyEfficiencyImpact;
  cleanTransportation: CleanTransportationImpact;
  sustainableWaterAndWastewater: SustainableWaterAndWastewaterImpact;
  renewableEnergy: RenewableEnergyImpact;
  sustainableLandUse: SustainableLandUseImpact;
  pollutionPreventionAndControl: PollutionPreventionAndControlImpact;
  baseYear: number;
};
export type Loan = {
  accountSk: number;
  customers?: Customer[] | null;
  isGreenLoan?: boolean;
  hasGreenproject?: boolean | null;
  interestType?: "Floating" | "Fixed";
  accountNumber: string;
  effectiveDate?: string | null;
  maturityDate?: string | null;
  drawdownAmount?: string | null;
  drawdownDate?: string | null;
  accountTypeCode?: string | null;
  accountTypeDescription?: string | null;
  currentBalance?: string | null;
  hostProductIdentifier?: string | null;
  salesChannel?: string | null;
  interestTypeCode?: string | null;
  productName?: string | null;
  interestRate?: string | null;
};
export type GreenProjectApproval = {
  id?: number;
  createdDatetime?: string;
  updatedDatetime?: string;
  approverUsername?: string;
  approverFirstName?: string;
  approverLastName?: string;
  approvalType: "SIGN_OFF" | "ENV_HELD" | "EVID_IMGD";
  greenProject: number;
};
export type GreenAssetList = {
  type?: "FeatureCollection";
  features?: GreenAsset[];
};
export type GreenAssetWritable = {
  type: "Feature";
  id: number;
  geometry: {
    type: "GeometryCollection";
    geometries: (
      | {
          type: "Point";
          coordinates: number[];
        }
      | {
          type: "LineString";
          coordinates: number[][];
        }
      | {
          type: "Polygon";
          coordinates: number[][][];
        }
      | {
          type: "MultiPoint";
          coordinates: number[][];
        }
      | {
          type: "MultiPolygon";
          coordinates: number[][][][];
        }
      | {
          type: "MultiLineString";
          coordinates: number[][][];
        }
      | {
          type: "GeometryCollection";
          geometries: any[];
        }
    )[];
  };
  properties: {
    project: number;
    projectId?: number | null;
    categories?: number[];
    categoryNames?: string[] | null;
    creatorName?: string | null;
    predominantSpeciesPlantedName?: string | null;
    baselineSource?: ("Peer Comparison" | "Historical Data" | null) | null;
    baselineSyntheticFertiliserUsedTonnes?: string | null;
    expectedSyntheticFertiliserUsedTonnes?: string | null;
    baselineAnnualKwh?: string | null;
    expectedAnnualKwh?: string | null;
    expectedAnnualTonnesCo2e?: string | null;
    baselineAnnualTonnesCo2e?: string | null;
    expectedAnnualTonnesCo2eReduced?: string | null;
    floorAreaM2?: string | null;
    wasteRemovedInConstructionTonnes?: string | null;
    wasteMinimisedInConstructionTonnes?: string | null;
    renewableEnergyGeneratedOnsiteKwh?: string | null;
    renewableEnergyGeneratedType?:
      | ("Solar" | "Hydro" | "Wind" | "Other" | null)
      | null;
    percentageRenewableEnergyGeneratedOnsite?: string | null;
    currentDaysStorage?: number | null;
    baselineNDwellingsServiced?: number | null;
    baselineNDwellingsOccupants?: number | null;
    expectedDaysStorage?: number | null;
    nitrogenFertiliserAppliedT?: string | null;
    nitrogenFertiliserAppliedKgHaPa?: string | null;
    currentEffluentSpreadingAreaHa?: string | null;
    currentStorageCapacityM3?: string | null;
    expectedVolumeOfWastewaterTreatedM3?: string | null;
    expectedNitrogenFertiliserAppliedT?: string | null;
    expectedNitrogenFertiliserAppliedKgHaPa?: string | null;
    expectedEffluentSpreadingAreaHa?: string | null;
    expectedStorageCapacityTPa?: string | null;
    failSafeAlarmInstalledEvidence?: boolean | null;
    wastewaterDischargeTreatmentEvidence?: boolean | null;
    baselineVolumeWasteWaterDischargedM3?: string | null;
    baselineVolumeWasteWaterCapturedM3?: string | null;
    typeOfWasteWaterCaptured?: string | null;
    currentWasteWaterDischargedM3?: string | null;
    expectedReductionOfWaterUsedM3?: string | null;
    annualWaterUseSqm?: string | null;
    emissionReductionTonnesCo2e?: string | null;
    annualCo2eTonnesReducedOrAvoided?: string | null;
    annualKwhReducedOrAvoided?: string | null;
    annualKwhReductionAsPercentageOfNormalEnergyUse?: string | null;
    insulationArea?: string | null;
    glazingType?: string | null;
    ledWattage?: string | null;
    baselineAnnualEmissionsCo2eKgKwh?: string | null;
    expectedAnnualEmissionsCo2eKgKwh?: string | null;
    previousFuelSourceUsedAnnually?: string | null;
    baselinePreviousFuelUsageL?: string | null;
    previousFuelSource?:
      | (
          | "Coal"
          | "Natural Gas"
          | "Diesel"
          | "Petrol"
          | "Biofuel"
          | "Other"
          | null
        )
      | null;
    previousFuelType?:
      | (
          | "Regular petrol"
          | "Premium petrol"
          | "Diesel"
          | "LPG"
          | "Other"
          | null
        )
      | null;
    replacementEnergyAssetOtherDescription?: string | null;
    outputCapacityKw?: string | null;
    renewableEnergyGeneratedKwh?: string | null;
    heatPumpRatingKw?: string | null;
    useSeer?: boolean;
    seerHeatingRating?: string | null;
    seerCoolingRating?: string | null;
    acopRating?: string | null;
    aeerRating?: string | null;
    copRating?: string | null;
    iplvRating?: string | null;
    eerRating?: string | null;
    transportationMake?: string | null;
    transportationModel?: string | null;
    hectaresPlanted?: string | null;
    expectedNonEffectiveArea?: string | null;
    expectedEffectiveArea?: string | null;
    plantingDate?: string | null;
    speciesPlanted?: string | null;
    countOfSpecies?: string | null;
    pestAndMaintenaceStrategyEvidenced?: boolean | null;
    rightTreeRightPlaceEvidenced?: boolean | null;
    nesPfEvidenced?: boolean | null;
    baselineNLossKgHa?: string | null;
    expectedNLossKgHa?: string | null;
    baselinePLossKgHa?: string | null;
    expectedPLossKgHa?: string | null;
    baselineStorageCapacityM3?: string | null;
    expectedStorageCapacityM3?: string | null;
    currentAnnualProcessingCapacityL?: string | null;
    ageOfReplacedSystemYears?: string | null;
    monthsOfYearStockKeptOffPaddock?: string | null;
    estimatedLeachateCaptured?: string | null;
    leachateTreatmentEvidence?: boolean | null;
    bunker50mFromWaterEvidence?: boolean | null;
    waterUseRecordedDailyEvidence?: boolean | null;
    soilProbesUsedEvidence?: boolean | null;
    monthsStockKeptOffPaddock?:
      | (
          | "jan"
          | "feb"
          | "mar"
          | "apr"
          | "may"
          | "jun"
          | "jul"
          | "aug"
          | "sep"
          | "oct"
          | "nov"
          | "dec"
        )[]
      | null;
    lengthOfWaterwaysM?: string | null;
    capacityOfInstalledWaterReticulationTanksM3?: string | null;
    waterStorageCapacityM3?: string | null;
    numberOfStockPreventedFromWaterways?: string | null;
    numberOfStockUnitsPriorToProject?: string | null;
    numberOfStockUnitsAtCompletion?: string | null;
    numberOfStockKeptOffPaddock?: string | null;
    hybridVehicleEmissionsGco2Km?: string | null;
    chargerModel?: string | null;
    chargerCapacityKw?: string | null;
    baselineVolumeOfWasteProcessedM3?: string | null;
    volumeOfWasteDivertedFromLandfillM3?: string | null;
    volumeOfWasteRecycledRecoveredReusedAsAPercentageOfTotalWasteProcessed?:
      | string
      | null;
    typeOfWasteSorted?: string | null;
    typeOfWasteTreated?: string | null;
    typeOfWasteRecycled?: string | null;
    baselineVolumeOfRecyclableCollectedM3?: string | null;
    baselineVolumeOfRecyclableStoredM3?: string | null;
    baselineVolumeOfWasteSortedM3?: string | null;
    baselineVolumeOfWasteTreatedM3?: string | null;
    baselineVolumeOfWasteRecycledM3?: string | null;
    expectedVolumeOfWasteSortedM3?: string | null;
    expectedVolumeOfTreatedWastewaterDischargedM3?: string | null;
    expectedVolumeOfWasteTreatedM3?: string | null;
    expectedVolumeOfWasteRecycledM3?: string | null;
    baselineVolumeOfWasteProducedPriorToProjectM3?: string | null;
    wasteProcessingEquipmentDescription?: string | null;
    volumeOfWasteRecycledRecoveredReusedM3?: string | null;
    typeOfRecyclableCollected?: string | null;
    typeOfRecyclableStored?: string | null;
    expectedVolumeOfRecyclableCollectedM3?: string | null;
    expectedVolumeOfRecyclableStoredM3?: string | null;
    volumeOfWasteReducedAfterPlantOrEquipmentReplacementPercentage?:
      | string
      | null;
    volumeOfWasteProducedBeforeReplacementM3?: string | null;
    volumeOfWasteProducedAfterReplacementM3?: string | null;
    baselineAnnualWaterUseSqm?: string | null;
    expectedAnnualWaterUseSqm?: string | null;
    renewableEnergyProducedKwh?: string | null;
    typeOfVehicleUsedForCollection?: string | null;
    collectionVehicleFossilFuelPowered?: ("Yes" | "No" | null) | null;
    wasteReductionDescription?: string | null;
    hybridVehicleEmissionsSource?: string | null;
    expectedAnnualProcessingCapacityL?: string | null;
    baselineWaterUseM3?: string | null;
    deletedDatetime?: string | null;
    predominantSpeciesPlanted?: number | null;
    seanzInstaller?: number | null;
  };
};
export type PatchedGreenAssetWritable = {
  type: "Feature";
  id: number;
  geometry: {
    type: "GeometryCollection";
    geometries: (
      | {
          type: "Point";
          coordinates: number[];
        }
      | {
          type: "LineString";
          coordinates: number[][];
        }
      | {
          type: "Polygon";
          coordinates: number[][][];
        }
      | {
          type: "MultiPoint";
          coordinates: number[][];
        }
      | {
          type: "MultiPolygon";
          coordinates: number[][][][];
        }
      | {
          type: "MultiLineString";
          coordinates: number[][][];
        }
      | {
          type: "GeometryCollection";
          geometries: any[];
        }
    )[];
  };
  properties: {
    project?: number;
    projectId?: number | null;
    categories?: number[];
    categoryNames?: string[] | null;
    creatorName?: string | null;
    predominantSpeciesPlantedName?: string | null;
    baselineSource?: ("Peer Comparison" | "Historical Data" | null) | null;
    baselineSyntheticFertiliserUsedTonnes?: string | null;
    expectedSyntheticFertiliserUsedTonnes?: string | null;
    baselineAnnualKwh?: string | null;
    expectedAnnualKwh?: string | null;
    expectedAnnualTonnesCo2e?: string | null;
    baselineAnnualTonnesCo2e?: string | null;
    expectedAnnualTonnesCo2eReduced?: string | null;
    floorAreaM2?: string | null;
    wasteRemovedInConstructionTonnes?: string | null;
    wasteMinimisedInConstructionTonnes?: string | null;
    renewableEnergyGeneratedOnsiteKwh?: string | null;
    renewableEnergyGeneratedType?:
      | ("Solar" | "Hydro" | "Wind" | "Other" | null)
      | null;
    percentageRenewableEnergyGeneratedOnsite?: string | null;
    currentDaysStorage?: number | null;
    baselineNDwellingsServiced?: number | null;
    baselineNDwellingsOccupants?: number | null;
    expectedDaysStorage?: number | null;
    nitrogenFertiliserAppliedT?: string | null;
    nitrogenFertiliserAppliedKgHaPa?: string | null;
    currentEffluentSpreadingAreaHa?: string | null;
    currentStorageCapacityM3?: string | null;
    expectedVolumeOfWastewaterTreatedM3?: string | null;
    expectedNitrogenFertiliserAppliedT?: string | null;
    expectedNitrogenFertiliserAppliedKgHaPa?: string | null;
    expectedEffluentSpreadingAreaHa?: string | null;
    expectedStorageCapacityTPa?: string | null;
    failSafeAlarmInstalledEvidence?: boolean | null;
    wastewaterDischargeTreatmentEvidence?: boolean | null;
    baselineVolumeWasteWaterDischargedM3?: string | null;
    baselineVolumeWasteWaterCapturedM3?: string | null;
    typeOfWasteWaterCaptured?: string | null;
    currentWasteWaterDischargedM3?: string | null;
    expectedReductionOfWaterUsedM3?: string | null;
    annualWaterUseSqm?: string | null;
    emissionReductionTonnesCo2e?: string | null;
    annualCo2eTonnesReducedOrAvoided?: string | null;
    annualKwhReducedOrAvoided?: string | null;
    annualKwhReductionAsPercentageOfNormalEnergyUse?: string | null;
    insulationArea?: string | null;
    glazingType?: string | null;
    ledWattage?: string | null;
    baselineAnnualEmissionsCo2eKgKwh?: string | null;
    expectedAnnualEmissionsCo2eKgKwh?: string | null;
    previousFuelSourceUsedAnnually?: string | null;
    baselinePreviousFuelUsageL?: string | null;
    previousFuelSource?:
      | (
          | "Coal"
          | "Natural Gas"
          | "Diesel"
          | "Petrol"
          | "Biofuel"
          | "Other"
          | null
        )
      | null;
    previousFuelType?:
      | (
          | "Regular petrol"
          | "Premium petrol"
          | "Diesel"
          | "LPG"
          | "Other"
          | null
        )
      | null;
    replacementEnergyAssetOtherDescription?: string | null;
    outputCapacityKw?: string | null;
    renewableEnergyGeneratedKwh?: string | null;
    heatPumpRatingKw?: string | null;
    useSeer?: boolean;
    seerHeatingRating?: string | null;
    seerCoolingRating?: string | null;
    acopRating?: string | null;
    aeerRating?: string | null;
    copRating?: string | null;
    iplvRating?: string | null;
    eerRating?: string | null;
    transportationMake?: string | null;
    transportationModel?: string | null;
    hectaresPlanted?: string | null;
    expectedNonEffectiveArea?: string | null;
    expectedEffectiveArea?: string | null;
    plantingDate?: string | null;
    speciesPlanted?: string | null;
    countOfSpecies?: string | null;
    pestAndMaintenaceStrategyEvidenced?: boolean | null;
    rightTreeRightPlaceEvidenced?: boolean | null;
    nesPfEvidenced?: boolean | null;
    baselineNLossKgHa?: string | null;
    expectedNLossKgHa?: string | null;
    baselinePLossKgHa?: string | null;
    expectedPLossKgHa?: string | null;
    baselineStorageCapacityM3?: string | null;
    expectedStorageCapacityM3?: string | null;
    currentAnnualProcessingCapacityL?: string | null;
    ageOfReplacedSystemYears?: string | null;
    monthsOfYearStockKeptOffPaddock?: string | null;
    estimatedLeachateCaptured?: string | null;
    leachateTreatmentEvidence?: boolean | null;
    bunker50mFromWaterEvidence?: boolean | null;
    waterUseRecordedDailyEvidence?: boolean | null;
    soilProbesUsedEvidence?: boolean | null;
    monthsStockKeptOffPaddock?:
      | (
          | "jan"
          | "feb"
          | "mar"
          | "apr"
          | "may"
          | "jun"
          | "jul"
          | "aug"
          | "sep"
          | "oct"
          | "nov"
          | "dec"
        )[]
      | null;
    lengthOfWaterwaysM?: string | null;
    capacityOfInstalledWaterReticulationTanksM3?: string | null;
    waterStorageCapacityM3?: string | null;
    numberOfStockPreventedFromWaterways?: string | null;
    numberOfStockUnitsPriorToProject?: string | null;
    numberOfStockUnitsAtCompletion?: string | null;
    numberOfStockKeptOffPaddock?: string | null;
    hybridVehicleEmissionsGco2Km?: string | null;
    chargerModel?: string | null;
    chargerCapacityKw?: string | null;
    baselineVolumeOfWasteProcessedM3?: string | null;
    volumeOfWasteDivertedFromLandfillM3?: string | null;
    volumeOfWasteRecycledRecoveredReusedAsAPercentageOfTotalWasteProcessed?:
      | string
      | null;
    typeOfWasteSorted?: string | null;
    typeOfWasteTreated?: string | null;
    typeOfWasteRecycled?: string | null;
    baselineVolumeOfRecyclableCollectedM3?: string | null;
    baselineVolumeOfRecyclableStoredM3?: string | null;
    baselineVolumeOfWasteSortedM3?: string | null;
    baselineVolumeOfWasteTreatedM3?: string | null;
    baselineVolumeOfWasteRecycledM3?: string | null;
    expectedVolumeOfWasteSortedM3?: string | null;
    expectedVolumeOfTreatedWastewaterDischargedM3?: string | null;
    expectedVolumeOfWasteTreatedM3?: string | null;
    expectedVolumeOfWasteRecycledM3?: string | null;
    baselineVolumeOfWasteProducedPriorToProjectM3?: string | null;
    wasteProcessingEquipmentDescription?: string | null;
    volumeOfWasteRecycledRecoveredReusedM3?: string | null;
    typeOfRecyclableCollected?: string | null;
    typeOfRecyclableStored?: string | null;
    expectedVolumeOfRecyclableCollectedM3?: string | null;
    expectedVolumeOfRecyclableStoredM3?: string | null;
    volumeOfWasteReducedAfterPlantOrEquipmentReplacementPercentage?:
      | string
      | null;
    volumeOfWasteProducedBeforeReplacementM3?: string | null;
    volumeOfWasteProducedAfterReplacementM3?: string | null;
    baselineAnnualWaterUseSqm?: string | null;
    expectedAnnualWaterUseSqm?: string | null;
    renewableEnergyProducedKwh?: string | null;
    typeOfVehicleUsedForCollection?: string | null;
    collectionVehicleFossilFuelPowered?: ("Yes" | "No" | null) | null;
    wasteReductionDescription?: string | null;
    hybridVehicleEmissionsSource?: string | null;
    expectedAnnualProcessingCapacityL?: string | null;
    baselineWaterUseM3?: string | null;
    deletedDatetime?: string | null;
    predominantSpeciesPlanted?: number | null;
    seanzInstaller?: number | null;
  };
};
export type GreenAssetCategory = {
  label: string;
  value: number;
};
export type GreenProjectCategory = {
  label: string;
  value: number;
};
export type GreenProjectFile = {
  id: number;
  uploaderName: string;
  createdDatetime: string;
  updatedDatetime: string;
  name: string;
  contentType: string;
  size: number;
  fileType?: "GLAF" | "ENVIRONMENTAL_EVIDENCE";
  uploader?: number | null;
  project: number;
};
export type GreenProjectFormOption = {
  label: string;
  value: number;
};
export type GreenProjectRegionalStatistic = {
  regionalCouncil: string;
  categoryName: string;
  count: number;
  drawdownAmount: string;
  currentBalance: string;
  waterSavedM3: number;
  fencingM: number;
  hectaresPlanted: number;
  emissionsReducedOrAvoidedTonnesCo2e: number;
  totalSequestrationTonnesCo2e: number;
  annualSroiPerMnzd: number;
  renewableEnergyGeneratedKwh: number;
};
export type GreenProjectSroiStatistic = {
  categoryName: string;
  count: number;
  drawdownAmount: string;
  currentBalance: string;
  waterSavedM3: number;
  fencingM: number;
  hectaresPlanted: number;
  emissionsReducedOrAvoidedTonnesCo2e: number;
  totalSequestrationTonnesCo2e: number;
  annualSroiPerMnzd: number;
  renewableEnergyGeneratedKwh: number;
};
export type GreenProjectSummary = {
  projectId: number;
  projectName: string;
  categoryName: string;
  accountNumbers: string[];
  customerNames: string[];
  customerNumbers: string[];
  regionalCouncil: string;
  drawdownAmount: string;
  currentBalance: string;
  waterSavedM3: number;
  fencingM: number;
  hectaresPlanted: number;
  emissionsReducedOrAvoidedTonnesCo2e: number;
  totalSequestrationTonnesCo2e: number;
  annualSroiPerMnzd: number;
  renewableEnergyGeneratedKwh: number;
};
export type GreenProjectSubcategory = {
  label: string;
  value: number;
};
export type ValuationsResourceConsentRecordAttachment = {
  id: number;
  blob: string;
  fileName?: string;
  fileSize?: number;
};
export type PatchedValuationsResourceConsentRecordAttachment = {
  id?: number;
  blob?: string;
  fileName?: string;
  fileSize?: number;
};
export type RuralInstructionLetterRequest = {
  id: number;
  properties: InstructionLetterRequestProperty[];
  customerDetails: InstructionLetterCustomerDetails;
  panelValuer: number;
  valuationManager: InstructionSenderDetails;
  deletedDate: string;
  relationshipManager: InstructionSenderDetails;
  propertyName?: string;
  propertyDescription?: string;
  interest?: string;
  tenancy?: string;
  basisOfValuation?: string;
  additionalReportingRequirements?: string | null;
  dateRequired?: string | null;
  customerFeeBasis?: boolean;
  feeBasisDescription?: string | null;
  updatedDate: string;
  createdDate: string;
  informationSupplied?: string | null;
};
export type PatchedRuralInstructionLetterRequest = {
  id?: number;
  properties?: InstructionLetterRequestProperty[];
  customerDetails?: InstructionLetterCustomerDetails;
  panelValuer?: number;
  valuationManager?: InstructionSenderDetails;
  deletedDate?: string;
  relationshipManager?: InstructionSenderDetails;
  propertyName?: string;
  propertyDescription?: string;
  interest?: string;
  tenancy?: string;
  basisOfValuation?: string;
  additionalReportingRequirements?: string | null;
  dateRequired?: string | null;
  customerFeeBasis?: boolean;
  feeBasisDescription?: string | null;
  updatedDate?: string;
  createdDate?: string;
  informationSupplied?: string | null;
};
export type RuralInstructionLetterRequestPdf = {
  id: number;
  currentDate: string;
  valuationManager: InstructionSenderDetails;
  dateRequired: string;
  deletedDate: string;
  properties: InstructionLetterRequestPropertyPdf[];
  customerDetails: InstructionLetterCustomerDetails;
  panelValuer: PanelValuer;
  relationshipManager: InstructionSenderDetails;
  propertyName?: string;
  propertyDescription?: string;
  interest?: string;
  tenancy?: string;
  basisOfValuation?: string;
  additionalReportingRequirements?: string | null;
  customerFeeBasis?: boolean;
  feeBasisDescription?: string | null;
  updatedDate: string;
  createdDate: string;
  informationSupplied?: string | null;
  creator: number;
};
export type HighestAndBestUseSummary = {
  id: number;
  valuationSummary: number;
  highestAndBestUseType: number;
  AEPByEffectiveHectare: string;
  AEPByImprovementsValue: string;
  LWBByEffectiveHectare: string;
  LWBByTotalHectare: string;
  LWBBy_AEP: string;
  improvementsAreaM2: string;
  improvementsMarketValue: string;
  improvementsPercentageOfMarketValue: string;
  marketValue: string;
  marketValueByEffectiveHectare: string;
  marketValueByTotalHectare: string;
  marketValueBy_AEP: string;
  nonCanopyPercentageOfMarketValue: string;
  pvRatio?: string;
  totalFarmIncome?: string;
  totalEffectiveHectares: string;
  totalHectares: string;
  totalIneffectiveHectares: string;
  total_AEP: string;
  total_LWB: string;
  totalCanopyHectares: string;
  totalCanopyValue: string;
  totalOrchardValue: string;
  nonCanopyValue: string;
  canopyValueByTotalHectare: string;
  canopyValueByCanopyHectare: string;
  orchardValueByCanopyHectare: string;
  AEPByCanopyHectare: string;
  canopyValueBy_AEP: string;
  aepUnit?: string;
  totalUnplantedHectares?: string | null;
  totalUnplantedValue?: string | null;
};
export type ValuationSummary = {
  highestAndBestUseSummary: HighestAndBestUseSummary[];
  id: number;
  valuation?: number | null;
  comparableSale?: number | null;
  improvementsAreaM2: string;
  improvementsMarketValue: string;
  improvementsPercentageOfMarketValue: number;
  marketValue: string;
  pvRatio: string;
  totalHectares: string;
  totalNonTitledHectares: string;
  totalEffectiveHectares: string;
  totalIneffectiveHectares: string;
  totalNonTitledEffectiveHectares: string;
  totalNonTitledIneffectiveHectares: string;
  totalUnallocatedHectares: string;
  total_LWB: string;
  totalAdjustmentPercent: string;
  lwbAdjustmentPercent: string;
  totalUnplantedHectares?: string | null;
  totalUnplantedValue?: string | null;
  totalHeadlandsHectares?: string | null;
  totalHeadlandsValue?: string | null;
  totalResidentialHectares?: string | null;
  totalResidentialValue?: string | null;
};
export type RegularSale = {
  saleId: number;
  id: number;
  fullAddress: string | null;
  address: number;
  creatorName: string | null;
  creator?: number | null;
  valuation: number;
  hasValuationSummary: boolean;
  bestUses: number[];
  highestAndBestUseName: string;
  aepUnit: string;
  highestAndBestUseType: number;
  buildingPerNetLandAndBuildingsPct: string;
  lwbPerTotalHa: number;
  lwbPerEffectiveHa: number;
  lwbPerAep: number;
  netLandAndBuildingsPerTotalHa: number;
  netLandAndBuildingsPerEffectiveHa: number;
  totalHa: number;
  valuationSummary: ValuationSummary;
  saleDate?: string | null;
  grossSalesPrice?: number | null;
  improvementsValue?: number | null;
  vendor?: string | null;
  purchaser?: string | null;
  listingCloseDate?: string | null;
  source?: string | null;
  sourceReference?: number | null;
  averageEfficientProduction?: number | null;
  effectiveHa?: number | null;
  bestUse?: string | null;
  tier?: (1 | 2 | 3 | null) | null;
  chattelsStockOther?: number | null;
  notionalSiteValue?: number | null;
  netLandAndBuildingsValue?: number | null;
  landWithoutBuildingsValue?: number | null;
  vetted?: boolean | null;
  isLs?: boolean | null;
  bonafide?: boolean | null;
  marketCircumstance?: string | null;
  region?: string | null;
  tenure?: ("FREEHOLD" | "LEASEHOLD" | null) | null;
  status?: string | null;
  irrigation?: string | null;
  irrigationWaterCost?: string | null;
  irrigationSource?: string | null;
  irrigationPrimaryApplication?: string | null;
  updatedDate: string | null;
  deletedDate?: string | null;
  partialSale?: boolean | null;
  saleAgent?: string | null;
  saleAgency?: string | null;
  saleMechanism?: string | null;
  closingDate?: string | null;
  stale?: boolean;
  lsdbId?: string | null;
  vendorBank?: string | null;
  purchaserBank?: string | null;
  proceedUse?: string | null;
  confidential?: boolean;
  url?: string;
  userActions?: string | null;
  assigned?: number | null;
  approver?: number | null;
  valocitySale?: number | null;
};
export type Sale = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    id: number;
    fullAddress: string | null;
    address: number;
    creatorName: string | null;
    creator?: number | null;
    valuation: number;
    hasValuationSummary: boolean;
    bestUses: number[];
    highestAndBestUseName: string;
    aepUnit: string;
    highestAndBestUseType: number;
    buildingPerNetLandAndBuildingsPct: string;
    lwbPerTotalHa: number;
    lwbPerEffectiveHa: number;
    lwbPerAep: number;
    netLandAndBuildingsPerTotalHa: number;
    netLandAndBuildingsPerEffectiveHa: number;
    totalHa: number;
    assigned?: Username | null;
    saleDate?: string | null;
    grossSalesPrice?: number | null;
    improvementsValue?: number | null;
    vendor?: string | null;
    purchaser?: string | null;
    listingCloseDate?: string | null;
    source?: string | null;
    sourceReference?: number | null;
    averageEfficientProduction?: number | null;
    effectiveHa?: number | null;
    bestUse?: string | null;
    tier?: (1 | 2 | 3 | null) | null;
    chattelsStockOther?: number | null;
    notionalSiteValue?: number | null;
    netLandAndBuildingsValue?: number | null;
    landWithoutBuildingsValue?: number | null;
    vetted?: boolean | null;
    isLs?: boolean | null;
    bonafide?: boolean | null;
    marketCircumstance?: string | null;
    region?: string | null;
    tenure?: ("FREEHOLD" | "LEASEHOLD" | null) | null;
    status?: string | null;
    irrigation?: string | null;
    irrigationWaterCost?: string | null;
    irrigationSource?: string | null;
    irrigationPrimaryApplication?: string | null;
    updatedDate: string | null;
    deletedDate?: string | null;
    partialSale?: boolean | null;
    saleAgent?: string | null;
    saleAgency?: string | null;
    saleMechanism?: string | null;
    closingDate?: string | null;
    stale?: boolean;
    lsdbId?: string | null;
    vendorBank?: string | null;
    purchaserBank?: string | null;
    proceedUse?: string | null;
    confidential?: boolean;
    url?: string;
    userActions?: string | null;
    approver?: number | null;
    valocitySale?: number | null;
  };
};
export type SaleList = {
  type?: "FeatureCollection";
  features?: Sale[];
};
export type PaginatedSaleList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: SaleList;
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type BaseSale = {
  saleId?: number;
  id?: number;
  fullAddress: string | null;
  creatorName: string;
  creator: number;
  valuation: number;
  sourceValuation?: number;
  valocitySaleId?: number;
  address?: number;
  createdDate?: string;
  highestAndBestUseType?: number;
  assigned?: Username | null;
  saleDate?: string | null;
  grossSalesPrice?: number | null;
  improvementsValue?: number | null;
  totalHa?: number | null;
  vendor?: string | null;
  purchaser?: string | null;
  listingCloseDate?: string | null;
  source?: string | null;
  sourceReference?: number | null;
  averageEfficientProduction?: number | null;
  effectiveHa?: number | null;
  bestUse?: string | null;
  tier?: (1 | 2 | 3 | null) | null;
  chattelsStockOther?: number | null;
  notionalSiteValue?: number | null;
  netLandAndBuildingsValue?: number | null;
  landWithoutBuildingsValue?: number | null;
  vetted?: boolean | null;
  isLs?: boolean | null;
  bonafide?: boolean | null;
  marketCircumstance?: string | null;
  region?: string | null;
  tenure?: ("FREEHOLD" | "LEASEHOLD" | null) | null;
  status?: string | null;
  irrigation?: string | null;
  irrigationWaterCost?: string | null;
  irrigationSource?: string | null;
  irrigationPrimaryApplication?: string | null;
  updatedDate: string | null;
  deletedDate?: string | null;
  partialSale?: boolean | null;
  saleAgent?: string | null;
  saleAgency?: string | null;
  saleMechanism?: string | null;
  closingDate?: string | null;
  stale?: boolean;
  lsdbId?: string | null;
  vendorBank?: string | null;
  purchaserBank?: string | null;
  proceedUse?: string | null;
  confidential?: boolean;
  url?: string;
  userActions?: string | null;
  approver?: number | null;
  valocitySale?: number | null;
};
export type PatchedBaseSale = {
  saleId?: number;
  id?: number;
  fullAddress?: string | null;
  creatorName?: string;
  creator?: number;
  valuation?: number;
  sourceValuation?: number;
  valocitySaleId?: number;
  address?: number;
  createdDate?: string;
  highestAndBestUseType?: number;
  assigned?: Username | null;
  saleDate?: string | null;
  grossSalesPrice?: number | null;
  improvementsValue?: number | null;
  totalHa?: number | null;
  vendor?: string | null;
  purchaser?: string | null;
  listingCloseDate?: string | null;
  source?: string | null;
  sourceReference?: number | null;
  averageEfficientProduction?: number | null;
  effectiveHa?: number | null;
  bestUse?: string | null;
  tier?: (1 | 2 | 3 | null) | null;
  chattelsStockOther?: number | null;
  notionalSiteValue?: number | null;
  netLandAndBuildingsValue?: number | null;
  landWithoutBuildingsValue?: number | null;
  vetted?: boolean | null;
  isLs?: boolean | null;
  bonafide?: boolean | null;
  marketCircumstance?: string | null;
  region?: string | null;
  tenure?: ("FREEHOLD" | "LEASEHOLD" | null) | null;
  status?: string | null;
  irrigation?: string | null;
  irrigationWaterCost?: string | null;
  irrigationSource?: string | null;
  irrigationPrimaryApplication?: string | null;
  updatedDate?: string | null;
  deletedDate?: string | null;
  partialSale?: boolean | null;
  saleAgent?: string | null;
  saleAgency?: string | null;
  saleMechanism?: string | null;
  closingDate?: string | null;
  stale?: boolean;
  lsdbId?: string | null;
  vendorBank?: string | null;
  purchaserBank?: string | null;
  proceedUse?: string | null;
  confidential?: boolean;
  url?: string;
  userActions?: string | null;
  approver?: number | null;
  valocitySale?: number | null;
};
export type Address = {
  addressId: number;
  address: string;
  lat?: number;
  lng?: number;
  landArea?: number;
  landZone?: string;
  landUse?: string;
  cv?: number;
  iv?: number;
  lv?: number;
  owners?: string;
  mortgagee?: string;
  linked: string;
  tradingGroupId?: string;
  lastSaleDate: string;
  lastGrossSalesPrice?: number;
  lastSaleSource: string;
  lastSaleId: string;
};
export type PatchedAddress = {
  addressId?: number;
  address?: string;
  lat?: number;
  lng?: number;
  landArea?: number;
  landZone?: string;
  landUse?: string;
  cv?: number;
  iv?: number;
  lv?: number;
  owners?: string;
  mortgagee?: string;
  linked?: string;
  tradingGroupId?: string;
  lastSaleDate?: string;
  lastGrossSalesPrice?: number;
  lastSaleSource?: string;
  lastSaleId?: string;
};
export type Elevation = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    elevation: number;
  };
};
export type ElevationList = {
  type?: "FeatureCollection";
  features?: Elevation[];
};
export type PatchedElevation = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    elevation?: number;
  };
};
export type SaleFile = {
  saleFileId: number;
  fileName?: string | null;
  fileDescription?: string | null;
  fileType?: string | null;
  fileSize?: number | null;
};
export type Neighbour = {
  type: "Feature";
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    addressId: string;
    fullAddress?: string | null;
    landUseDesc?: string | null;
    owners: string;
  };
};
export type NeighbourList = {
  type?: "FeatureCollection";
  features?: Neighbour[];
};
export type PatchedNeighbour = {
  type: "Feature";
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    addressId?: string;
    fullAddress?: string | null;
    landUseDesc?: string | null;
    owners?: string;
  };
};
export type DistrictValuationRoll = {
  dvrId: number;
  landArea: string;
  landAreaHectaresWithUnit: string;
  linzTitles: LinzTitles[];
  lvDollars?: string;
  ivDollars?: string;
  cvDollars?: string;
  valDateFormatted: string;
  dipid: string | null;
  valRef?: string | null;
  tlaId?: string | null;
  tlaName?: string | null;
  fullAddress?: string | null;
  category?: string | null;
  valDate?: string | null;
  cv?: number | null;
  lv?: number | null;
  iv?: number | null;
  floorArea?: number | null;
  improvement?: string | null;
  legalDesc?: string | null;
  salesGroup?: string | null;
  unitsOfUse?: string | null;
  landUse?: string | null;
  landUseDesc?: string | null;
  landZone?: string | null;
  landZoneDesc?: string | null;
  deletedDate?: string | null;
  updatedDate: string | null;
  searchVector?: string | null;
  titles: number[];
};
export type SalePdfData = {
  center: string;
  documentTitle: string;
  filename: string;
  sale: Sale;
  titles: Title[];
  districtValuationRoll: DistrictValuationRoll[];
  anzUnion: string;
  summary: string;
  propertyDescription: SalePropertyDescription;
};
export type PatchedSalePdfData = {
  center?: string;
  documentTitle?: string;
  filename?: string;
  sale?: Sale;
  titles?: Title[];
  districtValuationRoll?: DistrictValuationRoll[];
  anzUnion?: string;
  summary?: string;
  propertyDescription?: SalePropertyDescription;
};
export type SmapFamily = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    dominantFamily: string;
    area: string;
    objectid: number;
    source: string;
    smu: string;
  };
};
export type SmapFamilyList = {
  type?: "FeatureCollection";
  features?: SmapFamily[];
};
export type PatchedSmapFamily = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    dominantFamily?: string;
    area?: string;
    objectid?: number;
    source?: string;
    smu?: string;
  };
};
export type PatchedAnzUnion = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    area?: string;
    contour?: number | null;
    vegetation?: number | null;
    ps?: string | null;
    luc?: string | null;
  };
};
export type ScenarioExportRequest = {
  id: number;
  filename: string;
  status?:
    | ("REQUESTED" | "STARTED" | "COMPLETED" | "FAILED" | "EXPIRED" | null)
    | null;
  createdDatetime: string;
  failedDatetime?: string | null;
  completedDatetime?: string | null;
  kwargs?: {
    [key: string]: any;
  } | null;
};
export type RiskRadarExport = {
  perilType: number;
  lossModel: number;
  propertyType?: string;
  propertyZoning?: string;
  propertyStatus?: string;
  assetClass?: string;
  assetPortfolio?: string;
  anzsic?: string;
  ccr?: string;
  si?: string;
  roofConstruction?: string;
  customerSegment?: string;
  wallConstruction?: string;
  valocityPropertyClass?: string;
  anzPropertyClass?: string;
  territorialUnit0?: string;
  territorialUnit1?: string;
  territorialUnit2?: string;
  territorialUnit3?: string;
  territorialUnit4?: string;
  territorialUnit5?: string;
  isImpacted?: boolean;
};
export type SmapSibling = {
  fid: number;
  id: number;
  smu: string;
  name: string;
  confidence: string;
  drainageClass: string;
  droughtVulnerability: string;
  effluentClass: string;
  nitrogenLeachRisk: string;
  phosphateLeachRisk: string;
  profileMaterial: string;
  potrootingDepth: string;
  proportion: number;
  puggingRisk: string;
  rankOrder: string;
  relativeRunoffPotential: string;
  texture: string;
  soilDepth: string;
  soilDescription: string;
  topsoilStones: string;
  waterLoggingVulnerability: string;
  smapname?: string | null;
  siblingrankorder?: string | null;
  siblingproportion?: string | null;
  siblingconfidence?: string | null;
  family?: string | null;
  nzscdescription?: string | null;
  longsoildescription?: string | null;
  soildepth?: string | null;
  siblingtexture?: string | null;
  profilematerial?: string | null;
  rootbarrier?: string | null;
  potrootingdepth?: string | null;
  slowdepth?: string | null;
  topsoilstones?: string | null;
  drainageclass?: string | null;
  permeability?: string | null;
  puggingrisk?: string | null;
  waterloggedvulnirrigated?: string | null;
  droughtvulnerability?: string | null;
  irrigability?: string | null;
  relativenleachrisk?: string | null;
  relativepleachrisk?: string | null;
  effluentclass?: string | null;
  relativerunoffrotential?: string | null;
  awMm30cm?: string | null;
  awMm60cm?: string | null;
  pawMm?: string | null;
};
export type PaginatedTitleList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: TitleList;
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type TitleFeatureCollection = {
  type?: "FeatureCollection";
  features: Title[];
};
export type AnzUnionSummary = {
  type: "Feature";
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    contour: number;
    vegetation: number;
    ps: string;
    luc: string;
    geometryArea: string;
  };
};
export type AnzUnionFeatureCollection = {
  type?: "FeatureCollection";
  features: AnzUnionSummary[];
};
export type AnzUnionAgg = {
  ps: {
    [key: string]: any;
  };
  luc: {
    [key: string]: any;
  };
  vegetation: {
    [key: string]: any;
  };
};
export type ExportSummary = {
  landDescription: string;
  elevation: string;
  anzUnion: AnzUnionAgg;
};
export type TitlePdfData = {
  center: string;
  documentTitle: string;
  filename: string;
  titles: TitleFeatureCollection;
  districtValuationRoll: DistrictValuationRoll[];
  anzUnion: AnzUnionFeatureCollection;
  summary: ExportSummary;
};
export type LinzTitlesMemorial = {
  fid: number;
  titleNo: string;
  id: number;
  landDistrict?: string | null;
  memorialText?: string | null;
  current?: string | null;
  instrumentNumber?: string | null;
  instrumentLodgedDatetime?: string | null;
  instrumentType?: string | null;
  encumbrancees?: string | null;
  estimatedMortgagee?: string | null;
  mortgageInstrumentNumber?: string | null;
  deletedDate?: string | null;
  title: string;
};
export type ReverseSearch = {
  address: string;
  addressId?: number | null;
  fid: number;
  lat?: number;
  lng?: number;
  titleNo: string;
  owners: string;
};
export type PaginatedReverseSearchList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: ReverseSearch[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type LinzTitlesSearch = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    fid: number;
    id: number;
    titleNo: string;
    status?: string | null;
    type?: string | null;
    landDistrict?: string | null;
    issueDate?: string | null;
    guaranteeStatus?: string | null;
    estateDescription?: string | null;
    owners?: string | null;
    spatialExtentsShared?: string | null;
    createdDate?: string | null;
    updatedDate?: string | null;
    deletedDate?: string | null;
    area: number;
    surveyArea: string;
  };
};
export type PaginatedLinzTitlesSearchList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: LinzTitlesSearch[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type PaginatedTradingGroupList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: TradingGroup[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type PlantingAsset = {
  assetId: number;
  assetType: string;
  speciesName: string;
  speciesId: number;
  totalStock: number[];
  proposedDate: string;
  plantingArea: number;
};
export type Sequestration = {
  perAsset: PlantingAsset[];
  totalStock: number[];
  labels: string[];
};
export type TradingGroupAddress = {
  id?: number;
  createdDate?: string;
  tradingGroupId: string;
  tradingGroupName?: string | null;
  tradingGroupNumber?: string | null;
  linzAddress?: number | null;
  address: number | null;
};
export type PaginatedTradingGroupAddressList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: TradingGroupAddress[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type PatchedTradingGroupAddress = {
  id?: number;
  createdDate?: string;
  tradingGroupId?: string;
  tradingGroupName?: string | null;
  tradingGroupNumber?: string | null;
  linzAddress?: number | null;
  address?: number | null;
};
export type TradingGroupProperty = {
  assetId: string;
  tradingGroup: string;
  assetGroupId?: string | null;
  legalOwner: string;
  physicalAddress: string;
  certificateOfTitle?: string | null;
  comments?: string | null;
};
export type PaginatedTradingGroupPropertyList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: TradingGroupProperty[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type TradingGroupRegion = {
  regionId: number;
  regionName: string;
};
export type UserInformation = {
  id: number;
  name: string;
  title: string;
  user: number;
};
export type User = {
  id: number;
  username: string;
  settings: {
    [key: string]: any;
  };
  entitlements: string[];
  userInformation: UserInformation;
  assignedTlas: string[];
};
export type UserSettings = {
  settings: {
    [key: string]: any;
  };
};
export type PaginatedUserList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: User[];
};
export type KpiType = {
  id: number;
  value: string;
  key: string;
  label: string;
  description: string;
  agri?: boolean | null;
  commercial?: boolean | null;
  measureType: "$" | "%" | "x" | "days" | "ha" | "";
  category?: number | null;
};
export type ValocityGeoSale = {
  type: "Feature";
  id: number;
  geometry: {
    type: "Point";
    coordinates: number[];
  };
  properties: {
    fullAddress: string;
    irrelevantDate: string;
    irrelevancyComments: string | null;
    linkedSales: number[];
    source: string;
    sourceReference: string;
    effectiveHa: number;
    vetted: string;
    bestUse: string;
    region: string;
    linkedToListing: string;
    grossSalesPrice?: number | null;
    improvementsValue?: number | null;
    totalHa?: number | null;
    vendor?: string | null;
    purchaser?: string | null;
    saleType?: string | null;
    settlementDate?: string | null;
    vendorBank?: string | null;
    purchaserBank?: string | null;
    dipid?: string;
    valRef?: string;
    saleDate?: string | null;
    settleDate?: string | null;
    salePrice?: number | null;
    saleCategory?: string;
    saleValDate?: string | null;
    saleCv?: number | null;
    saleLv?: number | null;
    saleIv?: number | null;
    saleLandArea?: number | null;
    saleClassification?: string;
    saleTenure?: string;
    priceValueRelationship?: string;
    titleNo?: string;
    titleTransferDate?: string | null;
    titlePrevOwnerName?: string;
    titleNewOwnerName?: string;
    titlePrevMortgageeDischargeDate?: string | null;
    titlePrevMortgagee?: string;
    titleNewMortgagee?: string;
    titleNewMortgageeLodgedDate?: string | null;
    createdDate: string;
    updatedDate: string;
    address?: number | null;
  };
};
export type ValocityGeoSaleList = {
  type?: "FeatureCollection";
  features?: ValocityGeoSale[];
};
export type PaginatedValocityGeoSaleList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: ValocityGeoSaleList;
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type ValocitySaleUpdate = {
  irrelevantDate?: string;
  irrelevancyComments?: string;
};
export type ValuationSearch = {
  valuationId: number;
  valuationReference: string;
  valuationName: string;
  valuationType: string;
  highestAndBestUse: string | null;
  highestAndBestUseId?: string;
  address?: string;
  addressId?: number;
  creator?: number;
  creatorName: string;
  createdDate: string;
  updatedDate?: string;
  completedDate?: string;
  tradingGroupId?: string;
  tradingGroupNumber?: string;
  tradingGroupName?: string;
  totalHa?: number;
};
export type PaginatedValuationSearchList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: ValuationSearch[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type PatchedValuationSearch = {
  valuationId?: number;
  valuationReference?: string;
  valuationName?: string;
  valuationType?: string;
  highestAndBestUse?: string | null;
  highestAndBestUseId?: string;
  address?: string;
  addressId?: number;
  creator?: number;
  creatorName?: string;
  createdDate?: string;
  updatedDate?: string;
  completedDate?: string;
  tradingGroupId?: string;
  tradingGroupNumber?: string;
  tradingGroupName?: string;
  totalHa?: number;
};
export type ValuationResourceConsentReview = {
  pk: number;
  id: number;
  status?: 0 | 1 | 2;
  statusText: string;
  comments?: string;
};
export type LinzTitlesMemorials = {
  fid: number;
  instrumentLodgedDatetime?: string;
  id: number;
  landDistrict?: string | null;
  memorialText?: string | null;
  current?: string | null;
  instrumentNumber?: string | null;
  instrumentType?: string | null;
  encumbrancees?: string | null;
  estimatedMortgagee?: string | null;
  mortgageInstrumentNumber?: string | null;
  deletedDate?: string | null;
  title: string;
};
export type ValuationsTitleReview = {
  id: number;
  comments?: string;
  memorials: LinzTitlesMemorials[];
};
export type ValuationsSavedTitle = {
  valuationTitleId: number;
  id: number;
  areaHa: string;
  review?: ValuationsTitleReview;
  memorials: LinzTitlesMemorials[];
  label: string;
  fid: number;
  titleNo: string;
  status?: string | null;
  type?: string | null;
  landDistrict?: string | null;
  issueDate?: string | null;
  guaranteeStatus?: string | null;
  estateDescription?: string | null;
  owners?: string | null;
  spatialExtentsShared?: string | null;
  createdDate?: string | null;
  updatedDate?: string | null;
  deletedDate?: string | null;
  area?: number | null;
  surveyArea?: string | null;
  valuation: number;
};
export type Valuations = {
  id: number;
  inspections: ValuationInspection[];
  priorMarketValue: ValuationPriorMarketValue;
  resourceConsentReview: ValuationResourceConsentReview;
  titles: ValuationsSavedTitle[];
  valuationReference?: string | null;
  valuationName?: string | null;
  frontlineCreated?: boolean;
  createdFromSale?: boolean;
  linkedToProspect?: boolean;
  elevationStep?: number;
  remarksAndActions?: string;
  remarksAndActionsReportInclude?: boolean;
  titleReviewStatus?: number | null;
  titleReviewComments?: string | null;
  waterSecurityReviewStatus?: number | null;
  waterSecurityReviewComments?: string | null;
  createdDate: string;
  updatedDate: string;
  completedDate?: string | null;
  deletedDate?: string | null;
  approach?: "MARKET";
  tier?: (1 | 2 | 3 | null) | null;
  ccr?: (1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | null) | null;
  lvr?: number | null;
  si?: ("A" | "B" | "C" | "D" | "E" | "F" | "G" | null) | null;
  highestAndBestUseType?: number | null;
  benchmarkComparableSale?: number | null;
  address?: number | null;
  creator: number;
  tradingGroup?: number | null;
  sourceSale?: number | null;
  assignedHighestAndBestUseTypes: number[];
  customers: number[];
};
export type RvrValuationOnlyWritable = {
  rvrValuationId: number;
  rvr: number;
  externalValuer: string;
  primaryValuationApproach: string;
  valuationDate: string;
  rvrMarketValue?: number | null;
  valuersAep?: number | null;
  completed?: boolean;
};
export type ValuationsWritable = {
  pk: number;
  id: number;
  resourceConsentReview: ValuationResourceConsentReview | null;
  valuationName?: string | null;
  valuationReference?: string | null;
  address?: number | null;
  elevationStep?: number;
  highestAndBestUseType?: number | null;
  inspection: ValuationInspection | null;
  priorMarketValue: ValuationPriorMarketValue | null;
  rvrValuation?: RvrValuationOnlyWritable | null;
  remarksAndActions?: string;
  remarksAndActionsReportInclude?: boolean;
  tier?: (1 | 2 | 3 | null) | null;
  si?: ("A" | "B" | "C" | "D" | "E" | "F" | "G" | null) | null;
  ccr?: (1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | null) | null;
  lvr?: number | null;
  tradingGroup: string | null;
};
export type PatchedValuationsWritable = {
  pk?: number;
  id?: number;
  resourceConsentReview?: ValuationResourceConsentReview | null;
  valuationName?: string | null;
  valuationReference?: string | null;
  address?: number | null;
  elevationStep?: number;
  highestAndBestUseType?: number | null;
  inspection?: ValuationInspection | null;
  priorMarketValue?: ValuationPriorMarketValue | null;
  rvrValuation?: RvrValuationOnlyWritable | null;
  remarksAndActions?: string;
  remarksAndActionsReportInclude?: boolean;
  tier?: (1 | 2 | 3 | null) | null;
  si?: ("A" | "B" | "C" | "D" | "E" | "F" | "G" | null) | null;
  ccr?: (1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | null) | null;
  lvr?: number | null;
  tradingGroup?: string | null;
};
export type AddressFeature = {
  type: "Feature";
  id: number;
  geometry: {
    type: "Point";
    coordinates: number[];
  };
  properties: {
    address: string;
    lat?: number | null;
    lng?: number | null;
    fullAddress?: string | null;
    searchVector?: string | null;
    deletedDate?: string | null;
    updatedDate: string | null;
    createdDate: string | null;
    linzAddress?: number | null;
    districtValuationRoll?: number | null;
    tradingGroup: string[];
  };
};
export type ValuationsAnzUnionFeature = {
  type: "Feature";
  geometry: any;
  properties: {
    luc: string;
    geometryArea: number;
    geometryAreaPercentage: number;
    geometryAreaTotal: number;
    prorataArea: number;
  };
};
export type ValuationsAnzUnionFeatureList = {
  type: "FeatureCollection";
  features: ValuationsAnzUnionFeature[];
};
export type ComparableSaleAssetMetricAdjustment = {
  id?: number;
  comparableSale: number;
  landAssetMetric: number;
  comparability: string;
  comparableSubjectAep: string;
  productivityAdjustmentPercent: string;
};
export type ComparableSaleAdjustment = {
  id?: number;
  comparableSale: number;
  adjustmentType: string;
  buildingAdjustment: boolean;
  adjustmentPercent: string;
  comparability?: "Inferior" | "Comparable" | "Superior";
  comparabilityWithPercentage: string;
  comments?: string;
};
export type ValuationsDescription = {
  id: number;
  location?: string | null;
  climate?: string | null;
  landClass?: string | null;
  improvements?: string | null;
  irrigation?: string | null;
  environmentalCompliance?: string | null;
  dcCompliance?: string | null;
  other?: string | null;
  marketCircumstances?: string | null;
  searchVector?: string | null;
  valuation?: number | null;
  highestAndBestUseType?: number | null;
};
export type ValuationsImprovementAssetSummary = {
  id: number;
  area: string;
  buildingAge?: string;
  highestAndBestUseType: string;
  improvementType?: string;
  assignedMetricUnit?: string;
  unitMeasure: string;
  dollarPerUnitFormatted?: string;
  adoptedValue?: string;
  dollarPerUnit?: string;
  createdDate: string;
  updatedDate: string;
  deletedDate?: string | null;
  customImprovementType?: string | null;
  dollarPerUnitD?: string | null;
  valueD?: string | null;
  dateBuilt?: string;
  comments?: string;
  condition?: string;
  useCustomUnit?: boolean;
  customUnit?: "D" | "C" | "N";
  metricUnit?: string | null;
  metricValueD?: string | null;
  areaOverrideM2D?: string | null;
  valuation?: number | null;
  secondaryHighestAndBestUseType?: number | null;
  improvementBuildingOutlines: number[];
};
export type DiscountPoint = {
  absoluteYear: number;
  relativeYear: number;
  yearsToMaturity: number;
  discountFactor: string;
  vineStructuresHaDiscounted?: string;
  totalValueHaDiscounted?: string;
  totalValueDiscounted?: string;
};
export type LandAssetMetricSummary = {
  id?: number;
  landAsset: number;
  isProductive: boolean;
  isCanopy: boolean;
  isHorticulture: boolean;
  landClassName: string;
  byPercentage?: boolean;
  areaPercentage?: string | null;
  areaM2?: string | null;
  aepPerHectare: string;
  licenceValueHa?: string;
  canopyPlantedValueHa?: string;
  vineStructuresHa?: string;
  dollarPerHectare?: string;
  plantingVariety?: number | null;
  plantingVarietyName: string;
  plantingRegime?: string | null;
  coverType?: string | null;
  orchardType?: string | null;
  plantedYear?: string;
  maturity?: ("Mature" | "Immature" | "" | null) | null;
  yearsToMaturity: number;
  totalValueHaAtMaturity: string;
  graftingMethod?: ("NEW" | "EXISTING" | null) | null;
  irrigation?: string | null;
  irrigationSource?: string | null;
  irrigationWaterCost?: string | null;
  irrigationPrimaryApplication?: string | null;
  totalArea: string;
  totalValue?: string;
  totalHectares: string;
  totalDollarPerHectare: string;
  total_AEP: string;
  total_LWB: string;
  total_LWBBy_AEP: string;
  effectiveArea: string;
  ineffectiveArea: string;
  nonCanopyLandValueHa?: string;
  nonTitledLandType: string;
  discountPoints: DiscountPoint[];
  hasDiscountCurve: boolean;
  discountAtValuation: string;
  aepPerHectareWithUnit: string | null;
  effectiveAreaWithUnit: string;
  fullName: string;
  labelText: string;
  labelShortText: string;
  totalDollarPerHectareDollars: string;
  totalLwbDollars?: string;
  totalLwbByAepDollars?: string;
};
export type ValuationsLandAssetSummary = {
  id: number;
  area: string;
  aspect: string;
  effectiveArea: string;
  effectiveAreaWithUnit: string;
  landClass: string;
  metrics: LandAssetMetricSummary[];
  createdDate: string;
  updatedDate: string;
  deletedDate?: string | null;
  areaOverrideM2?: number | null;
  nonTitledLandType?:
    | ("AMF" | "DOC" | "ECAN" | "riverAccretion" | "otherNonTitledArea" | null)
    | null;
  valuation?: number | null;
  secondaryHighestAndBestUseType?: number | null;
  balanceLandAsset?: number | null;
};
export type HighestAndBestUseSummaryWithAssets = {
  id: number;
  aepByEffectiveHectare: string;
  aepByCanopyHectareWithUnit: string | null;
  aepByEffectiveHectareWithUnit: string | null;
  aepByImprovementsValueWithUnit: string | null;
  aepUnit: string;
  canopyValueByAepDollars: string;
  canopyValueByEffectiveHectareDollars: string;
  canopyValueByTotalHectareDollars: string;
  category?: string;
  highestAndBestUseType: number;
  highestAndBestUse: string;
  improvementAssets: ValuationsImprovementAssetSummary[];
  improvementsArea: string;
  improvementsAreaM2WithUnit: string;
  improvementsMarketValue: string;
  improvementsMarketValueDollars?: string;
  improvementsPercentageOfMarketValue: string;
  improvementsPercentageOfMarketValueWithUnit: string;
  isHorticulture: boolean;
  isOrchard: boolean;
  landAssets: ValuationsLandAssetSummary[];
  lwbByAep: string;
  lwbByAepDollars: string;
  lwbByEffectiveHectareDollars: string;
  lwbByEffectiveHectare: string;
  lwbByTotalHectare: string;
  lwbByTotalHectareDollars: string;
  lwbApportionmentText: string;
  marketValue: string;
  marketValueDollars: string;
  marketValueByAep: string;
  marketValueByAepDollars?: string;
  marketValueByEffectiveHectare: string;
  marketValueByEffectiveHectareDollars: string;
  marketValueByTotalHectare: string;
  marketValueByTotalHectareDollars: string;
  nonCanopyPercentageOfMarketValue: string;
  orchardValueByEffectiveHectareDollars: string;
  pvRatio: string;
  totalAep: string;
  totalAepWithUnit: string | null;
  totalCanopyValueDollars?: string;
  totalCanopyHectaresWithUnit: string;
  totalEffectiveHectares: string;
  totalFarmIncome?: string;
  totalHectares: string;
  totalHectaresWithUnit: string;
  totalLwb?: string;
};
export type ValuationSummaryWithAssets = {
  highestAndBestUseSummary: HighestAndBestUseSummaryWithAssets[];
  id: number;
  valuation?: number | null;
  comparableSale?: number | null;
  improvementsAreaM2: string;
  improvementsMarketValue: string;
  improvementsPercentageOfMarketValue: number;
  marketValue: string;
  pvRatio: string;
  totalHectares?: string;
  totalNonTitledHectares: string;
  totalEffectiveHectares?: string;
  totalIneffectiveHectares: string;
  totalNonTitledEffectiveHectares: string;
  totalNonTitledIneffectiveHectares: string;
  totalUnallocatedHectares: string;
  total_LWB: string;
  totalAdjustmentPercent: string;
  lwbAdjustmentPercent: string;
  totalUnplantedHectares?: string | null;
  totalUnplantedValue?: string | null;
  totalHeadlandsHectares?: string | null;
  totalHeadlandsValue?: string | null;
  totalResidentialHectares?: string | null;
  totalResidentialValue?: string | null;
  defaultAepUnit: string;
  improvementsAreaM2WithUnit: string;
  improvementsMarketValueDollars?: string;
  marketValueDollars: string;
  marketValueByAep: string;
  marketValueByEffectiveHectares: string;
  marketValueByTotalHectares: string;
  lwbByEffectiveHectares: string;
  totalAep: string;
  totalLwb: string;
  improvementsPercentageOfMarketValueWithUnit: string;
  lwbByAep: string;
  lwbByTotalHectares: string;
  defaultHbuSummary: HighestAndBestUseSummaryWithAssets;
};
export type RegularSaleWithSummary = {
  saleId: number;
  id: number;
  fullAddress: string | null;
  address: number;
  creatorName: string | null;
  creator?: number | null;
  valuation: number;
  hasValuationSummary: boolean;
  bestUses: number[];
  highestAndBestUseName: string;
  aepUnit: string;
  highestAndBestUseType: number;
  buildingPerNetLandAndBuildingsPct: string;
  lwbPerTotalHa: number;
  lwbPerEffectiveHa: number;
  lwbPerAep: number;
  netLandAndBuildingsPerTotalHa: number;
  netLandAndBuildingsPerEffectiveHa: number;
  totalHa: number;
  valuationSummary: ValuationSummaryWithAssets;
  saleDate?: string | null;
  grossSalesPrice?: number | null;
  improvementsValue?: number | null;
  vendor?: string | null;
  purchaser?: string | null;
  listingCloseDate?: string | null;
  source?: string | null;
  sourceReference?: number | null;
  averageEfficientProduction?: number | null;
  effectiveHa?: number | null;
  bestUse?: string | null;
  tier?: (1 | 2 | 3 | null) | null;
  chattelsStockOther?: number | null;
  notionalSiteValue?: number | null;
  netLandAndBuildingsValue?: number | null;
  landWithoutBuildingsValue?: number | null;
  vetted?: boolean | null;
  isLs?: boolean | null;
  bonafide?: boolean | null;
  marketCircumstance?: string | null;
  region?: string | null;
  tenure?: ("FREEHOLD" | "LEASEHOLD" | null) | null;
  status?: string | null;
  irrigation?: string | null;
  irrigationWaterCost?: string | null;
  irrigationSource?: string | null;
  irrigationPrimaryApplication?: string | null;
  updatedDate: string | null;
  deletedDate?: string | null;
  partialSale?: boolean | null;
  saleAgent?: string | null;
  saleAgency?: string | null;
  saleMechanism?: string | null;
  closingDate?: string | null;
  stale?: boolean;
  lsdbId?: string | null;
  vendorBank?: string | null;
  purchaserBank?: string | null;
  proceedUse?: string | null;
  confidential?: boolean;
  url?: string;
  userActions?: string | null;
  assigned?: number | null;
  approver?: number | null;
  valocitySale?: number | null;
};
export type ValuationsSavedTitleFeature = {
  type: "Feature";
  id: number;
  geometry: {
    type: "MultiPolygon";
    coordinates: number[][][][];
  };
  properties: {
    id: number;
    area: string;
    areaHa: string;
    review?: ValuationsTitleReview;
    memorials: LinzTitlesMemorials[];
    fid: number;
    titleNo: string;
    status?: string | null;
    type?: string | null;
    landDistrict?: string | null;
    issueDate?: string | null;
    guaranteeStatus?: string | null;
    estateDescription?: string | null;
    owners?: string | null;
    spatialExtentsShared?: string | null;
    createdDate?: string | null;
    updatedDate?: string | null;
    deletedDate?: string | null;
    surveyArea?: string | null;
    valuation: number;
  };
};
export type ValuationsSavedTitleFeatureList = {
  type: "FeatureCollection";
  features: ValuationsSavedTitleFeature[];
};
export type ValuationComparableSaleSummary = {
  type: "Feature";
  id: number;
  geometry: any;
  properties: {
    assetMetricAdjustments: ComparableSaleAssetMetricAdjustment[];
    fullAddress: string;
    totalAdjustmentPercent: string;
    lwbAdjustmentPercent: string;
    adjustments: ComparableSaleAdjustment[];
    descriptions: ValuationsDescription;
    highestAndBestUse: string;
    linkedSale: RegularSaleWithSummary;
    lwbComparability: string;
    overallComparability: string;
    saleDate?: string;
    titles: ValuationsSavedTitleFeatureList;
    valuationSummary: ValuationSummaryWithAssets;
    defaultHbuSummary: HighestAndBestUseSummaryWithAssets;
    defaultHbuSummaryUnadjusted: HighestAndBestUseSummaryWithAssets;
    useValuationSummary?: boolean | null;
    landClassAdjustmentSummary?: string | null;
    summaryComments?: string;
    createdDate: string;
    updatedDate: string;
    deletedDate?: string | null;
    valuation?: number | null;
    highestAndBestUseType: number;
    saleHighestAndBestUseType: number;
    creator?: number | null;
  };
};
export type ValuationsCombinedResourceConsentAttachment = {
  id: number;
  fileName?: string;
  fileDescription?: string;
  fileType?: string;
  fileSize?: number;
  createdDate: string;
  deletedDate?: string | null;
  resourceConsent: number;
};
export type ValuationsCombinedResourceConsent = {
  id: number;
  creatorId?: number;
  valuationId?: number;
  number: string;
  description?: string;
  status?: string;
  holder?: string;
  commencement?: string;
  expiry?: string;
  link?: string;
  attachments: ValuationsCombinedResourceConsentAttachment[];
};
export type ValuationComparableSaleSummaryList = {
  type: "FeatureCollection";
  features: ValuationComparableSaleSummary[];
};
export type ValuationsElevation = {
  type: "Feature";
  id: number;
  geometry: any;
  properties: {
    area: number;
    elevation: number;
  };
};
export type ValuationsElevationList = {
  type: "FeatureCollection";
  features: ValuationsElevation[];
};
export type ValuationsLandAssetFeature = {
  type: "Feature";
  id: number;
  geometry:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  properties: {
    area: string;
    aspect: string;
    effectiveArea: string;
    effectiveAreaWithUnit: string;
    landClass: string;
    metrics: LandAssetMetricSummary[];
    aepUnit?: string;
    landClassName: string;
    landClassColor: string;
    highestAndBestUseType: string;
    total_AEP: string;
    totalArea: string;
    totalEffectiveArea: string;
    createdDate: string;
    updatedDate: string;
    deletedDate?: string | null;
    areaOverrideM2?: number | null;
    nonTitledLandType?:
      | (
          | "AMF"
          | "DOC"
          | "ECAN"
          | "riverAccretion"
          | "otherNonTitledArea"
          | null
        )
      | null;
    valuation?: number | null;
    secondaryHighestAndBestUseType?: number | null;
    balanceLandAsset?: number | null;
  };
};
export type ValuationsLandAssetFeatureList = {
  type: "FeatureCollection";
  features: ValuationsLandAssetFeature[];
};
export type ValuationsImprovementAsset = {
  id: number;
  area: string;
  buildingAge?: string;
  highestAndBestUseType: string;
  improvementType?: string;
  assignedMetricUnit?: string;
  unitMeasure: string;
  dollarPerUnitFormatted?: string;
  adoptedValue?: string;
  geom:
    | {
        type: "Point";
        coordinates: number[];
      }
    | {
        type: "LineString";
        coordinates: number[][];
      }
    | {
        type: "Polygon";
        coordinates: number[][][];
      }
    | {
        type: "MultiPoint";
        coordinates: number[][];
      }
    | {
        type: "MultiPolygon";
        coordinates: number[][][][];
      }
    | {
        type: "MultiLineString";
        coordinates: number[][][];
      }
    | {
        type: "GeometryCollection";
        geometries: any[];
      };
  createdDate: string;
  updatedDate: string;
  deletedDate?: string | null;
  customImprovementType?: string | null;
  dollarPerUnitD?: string | null;
  valueD?: string | null;
  dateBuilt?: string;
  comments?: string;
  condition?: string;
  useCustomUnit?: boolean;
  customUnit?: "D" | "C" | "N";
  metricUnit?: string | null;
  metricValueD?: string | null;
  areaOverrideM2D?: string | null;
  valuation?: number | null;
  secondaryHighestAndBestUseType?: number | null;
  improvementBuildingOutlines: number[];
};
export type ValuationsLucFeature = {
  type: "Feature";
  geometry: any;
  properties: {
    luc: string;
    geometryArea: number;
    geometryAreaPercentage: number;
    geometryAreaTotal: number;
    prorataArea: number;
  };
};
export type ValuationsLucFeatureList = {
  type: "FeatureCollection";
  features: ValuationsLucFeature[];
};
export type ValuationsServiceCentres = {
  type: "Feature";
  id: number;
  geometry: any;
  properties: {
    name: string;
  };
};
export type ValuationsSmapSiblingByFamily = {
  key: string;
  family?: string | null;
  name: string;
  area: string;
  hectares: string;
  proportion: string;
  soilDepth: string;
  texture: string;
  potentialRootingDepth: string;
  topsoilStones: string;
  drainageClass: string;
  waterloggingVulnerability: string;
  droughtVulnerability: string;
  nitrogenLeachingVulnerability: string;
  relativeRunoffPotential: string;
};
export type ValuationsTitleReviewSummary = {
  id: number;
  comments: string;
  titleNo: string;
  memorialNos: string;
};
export type ValuationsTradingGroupAddress = {
  name: string;
  number: string;
};
export type PlantingSummary = {
  id: number;
  relativeYear: string;
  absoluteYear: string;
  plantingVarietyName: string;
  maturity: string;
  canopyHectares: string;
  vineStructuresHa?: string;
  totalValueHa?: string;
  totalValue?: string;
};
export type ProjectedValuationSummary = {
  id: number;
  valuation: number;
  relativeYear: number;
  absoluteYear: number;
  plantingValues: PlantingSummary[];
  improvementsAreaM2: string;
  improvementsAreaHa: string;
  improvementsMarketValue?: string;
  improvementsMarketValueByImprovementsAreaHa?: string;
  improvementsPercentageOfMarketValue: number;
  marketValue?: string;
  marketValueByTotalHectares?: string;
  pvRatio: string;
  totalHectares: string;
  totalNonTitledHectares: string;
  totalEffectiveHectares: string;
  totalIneffectiveHectares: string;
  totalNonTitledEffectiveHectares: string;
  totalNonTitledIneffectiveHectares: string;
  totalUnallocatedHectares: string;
  total_LWB?: string;
  totalUnplantedHectares: string;
  totalUnplantedValue?: string;
  totalUnplantedValuePerHa?: string;
  totalHeadlandsHectares: string;
  totalHeadlandsValue?: string;
  totalHeadlandsValuePerHa?: string;
  totalResidentialHectares: string;
  totalResidentialValue?: string;
  totalResidentialValuePerHa?: string;
  totalOtherUnproductiveHectares: string;
  totalOtherUnproductiveValue?: string;
  totalOtherUnproductiveValuePerHa?: string;
  totalNonCanopyProductiveHectares: string;
  totalNonCanopyProductiveValue?: string;
  totalNonCanopyProductiveValuePerHa?: string;
};
export type ValuationsPvsSummary = {
  id: number;
  address: AddressFeature;
  anzUnion: ValuationsAnzUnionFeatureList;
  appraiserName?: string;
  approach: string;
  aspectSummaries: string[];
  benchmarkComparableSale: ValuationComparableSaleSummary;
  combinedResourceConsents: ValuationsCombinedResourceConsent[];
  comparableSales: ValuationComparableSaleSummaryList;
  completedDateString?: string | null;
  completedDateLong: string;
  createdDateLong: string;
  descriptions: ValuationsDescription | null;
  environmentalComplianceDescription: string;
  elevation: ValuationsElevationList;
  elevationDescription?: string;
  fullAddress: string;
  highestAndBestUse?: string;
  highestAndBestUseCategory?: string;
  highestAndBestUseName?: string;
  inspected: boolean;
  inspection: ValuationInspection;
  waterSecurityReview: string | null;
  landAssets: ValuationsLandAssetFeatureList;
  landClassText: string;
  landDistrict: string;
  locationDescription: string;
  improvements: ValuationsImprovementAsset[];
  irrigationDescription: string;
  luc: ValuationsLucFeatureList;
  priorMarketValue: ValuationPriorMarketValue;
  savedTitleArea: string;
  serviceCentres: ValuationsServiceCentres[];
  seasonalRainfall?: {
    [key: string]: any;
  };
  smapArea: string;
  smapDescriptions: string[];
  fslSoilDescriptions: string[];
  smapSiblings: ValuationsSmapSiblingByFamily[];
  remarksAndActions: string;
  summary: ValuationSummaryWithAssets;
  titleApportionment: ValuationMortgageApportionment[];
  titleMatchingRatingValuation: DistrictValuationRoll;
  titles: ValuationsSavedTitleFeatureList;
  titleReviews: ValuationsTitleReviewSummary[];
  tlaName: string;
  totalHectares: string;
  tradingGroup?: ValuationsTradingGroupAddress;
  projectedValuationSummaries: ProjectedValuationSummary[];
  horticultureLandAssetMetrics: LandAssetMetricSummary[];
  hasKiwifruitBestUse: boolean;
  valuationReference?: string | null;
  valuationName?: string | null;
  frontlineCreated?: boolean;
  createdFromSale?: boolean;
  linkedToProspect?: boolean;
  elevationStep?: number;
  remarksAndActionsReportInclude?: boolean;
  titleReviewStatus?: number | null;
  titleReviewComments?: string | null;
  waterSecurityReviewStatus?: number | null;
  waterSecurityReviewComments?: string | null;
  createdDate: string;
  updatedDate: string;
  completedDate?: string | null;
  deletedDate?: string | null;
  tier?: (1 | 2 | 3 | null) | null;
  ccr?: (1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | null) | null;
  lvr?: number | null;
  si?: ("A" | "B" | "C" | "D" | "E" | "F" | "G" | null) | null;
  highestAndBestUseType?: number | null;
  creator: number;
  sourceSale?: number | null;
  assignedHighestAndBestUseTypes: number[];
  customers: number[];
};
export type ValuationsResourceConsentRecord = {
  id: number;
  creator: number;
  valuation: number;
  purpose?: string;
  holder?: string;
  number?: string;
  status?: "ceased" | "current" | "pending" | "unknown" | "";
  commencementDate?: string | null;
  expiryDate?: string | null;
  link?: string;
};
export type PatchedValuationsResourceConsentRecord = {
  id?: number;
  creator?: number;
  valuation?: number;
  purpose?: string;
  holder?: string;
  number?: string;
  status?: "ceased" | "current" | "pending" | "unknown" | "";
  commencementDate?: string | null;
  expiryDate?: string | null;
  link?: string;
};
export type PatchedValuationsSavedTitle = {
  valuationTitleId?: number;
  id?: number;
  areaHa?: string;
  review?: ValuationsTitleReview;
  memorials?: LinzTitlesMemorials[];
  label?: string;
  fid?: number;
  titleNo?: string;
  status?: string | null;
  type?: string | null;
  landDistrict?: string | null;
  issueDate?: string | null;
  guaranteeStatus?: string | null;
  estateDescription?: string | null;
  owners?: string | null;
  spatialExtentsShared?: string | null;
  createdDate?: string | null;
  updatedDate?: string | null;
  deletedDate?: string | null;
  area?: number | null;
  surveyArea?: string | null;
  valuation?: number;
};
export type ValuationsTitleReviewWritable = {
  comments?: string;
  memorials?: number[];
};
export type PatchedValuationsTitleReviewWritable = {
  comments?: string;
  memorials?: number[];
};
export const {
  useAddressesListQuery,
  useAddressesCreateMutation,
  useAddressesRetrieveQuery,
  useAddressesUpdateMutation,
  useAddressesPartialUpdateMutation,
  useAddressesDestroyMutation,
  useAddressesSummaryRetrieveQuery,
  useAddressesSummaryListQuery,
  useAdminProjectsListQuery,
  useAdminProjectsRetrieveQuery,
  useAdminSalesListQuery,
  useAdminSalesRetrieveQuery,
  useAdminSalesDeleteCreateMutation,
  useAdminTerritorialAuthorityAssignmentListQuery,
  useAdminTerritorialAuthorityAssignmentCreateMutation,
  useAdminTerritorialAuthorityAssignmentRetrieveQuery,
  useAdminTerritorialAuthorityAssignmentUpdateMutation,
  useAdminTerritorialAuthorityAssignmentPartialUpdateMutation,
  useAdminTerritorialAuthorityAssignmentDestroyMutation,
  useAdminUsageRetrieveQuery,
  useAdminUsersListQuery,
  useAlertListQuery,
  useAlertCreateMutation,
  useAlertRetrieveQuery,
  useAlertUpdateMutation,
  useAlertPartialUpdateMutation,
  useAlertDestroyMutation,
  useAnzsicListQuery,
  useAnzsicRetrieveQuery,
  useAssetPvsExportRetrieveQuery,
  useAssetsRetrieveQuery,
  useAssetsBulkCreateMutation,
  useAssetsBulkEditCreateMutation,
  useAssetsGenerateDescriptionCreateMutation,
  useAssetsRemainingGeometryCreateMutation,
  useAssetsTitleApportionmentRetrieveQuery,
  useBoundariesListQuery,
  useBoundariesRetrieveQuery,
  useBoundariesLookupListQuery,
  useBusinessUnitsListQuery,
  useCcraCcraListQuery,
  useCcraCcraCreateMutation,
  useCcraCcraRetrieveQuery,
  useCcraCcraUpdateMutation,
  useCcraCcraPartialUpdateMutation,
  useCcraCcraDestroyMutation,
  useCcraCcraUploadFileCreateMutation,
  useCcraCcraReportTypesListQuery,
  useCcraFilesListQuery,
  useCcraFilesRetrieveQuery,
  useCcraFilesDestroyMutation,
  useCcraFormOptionsListQuery,
  useCommercialInstructionLetterListQuery,
  useCommercialInstructionLetterCreateMutation,
  useCommercialInstructionLetterRetrieveQuery,
  useCommercialInstructionLetterUpdateMutation,
  useCommercialInstructionLetterPartialUpdateMutation,
  useCommercialInstructionLetterDestroyMutation,
  useCommercialInstructionLetterPdfDataRetrieveQuery,
  useCustomerListQuery,
  useCustomerGroupSearchListQuery,
  useCustomerGroupSearchRetrieveQuery,
  useCustomerGroupsListQuery,
  useCustomerGroupsRetrieveQuery,
  useCustomerGroupsLendingListQuery,
  useCustomerSearchListQuery,
  useCustomerDvrListQuery,
  useCustomerDvrAddMutation,
  useCustomerDvrRemoveMutation,
  useCustomerDvrSetMutation,
  useCustomerKpiListQuery,
  useCustomerKpiBenchmarkListQuery,
  useCustomerRetrieveQuery,
  useCustomerBalancesListQuery,
  useCustomerEmissionsRetrieveQuery,
  useCustomerGroupRetrieveQuery,
  useCustomerGroupLendingListQuery,
  useCustomerLendingListQuery,
  useCustomerProxyEmissionsListQuery,
  useCustomerValuationsListQuery,
  useCustomerGroupListQuery,
  useCustomerGroupRetrieve2Query,
  useCustomerGroupLendingList2Query,
  useCustomerReportTemplateListQuery,
  useCustomerReportTemplateCreateMutation,
  useCustomerReportTemplateRetrieveQuery,
  useCustomerReportTemplateUpdateMutation,
  useCustomerReportTemplatePartialUpdateMutation,
  useCustomerReportTemplateDestroyMutation,
  useDataSchemaQuery,
  useFormTemplateQuery,
  useDvrListQuery,
  useEmissionListQuery,
  useEmissionCreateMutation,
  useEmissionRetrieveQuery,
  useEmissionUpdateMutation,
  useEmissionPartialUpdateMutation,
  useEmissionDestroyMutation,
  useEmissionBenchmarkingRetrieveQuery,
  useEmissionFormOptionsListQuery,
  useEmissionScatterListQuery,
  useEntityGroupSearchListQuery,
  useEntityGroupSearchGroupCustomersListQuery,
  useEventsCreateMutation,
  useEventsErrorCreateMutation,
  useEventsPageLoadCreateMutation,
  useExplorerAddressesRetrieveQuery,
  useExplorerTitlesListQuery,
  useExplorerTlaListingsRetrieveQuery,
  useExplorerTlasRetrieveQuery,
  useFacilitiesUpdateMutation,
  useFacilitiesPartialUpdateMutation,
  useFrontlineSalesListQuery,
  useFrontlineSalesCreateMutation,
  useFrontlineSalesRetrieveQuery,
  useFrontlineSalesUpdateMutation,
  useFrontlineSalesPartialUpdateMutation,
  useFrontlineSalesDestroyMutation,
  useInstructionLetterListQuery,
  useInstructionLetterCreateMutation,
  useInstructionLetterRetrieveQuery,
  useInstructionLetterUpdateMutation,
  useInstructionLetterPartialUpdateMutation,
  useInstructionLetterDestroyMutation,
  useLayersListQuery,
  useLayersCreateMutation,
  useLayersRetrieveQuery,
  useGetDataLineageQuery,
  useLocationListQuery,
  useLocationCreateMutation,
  useLocationRetrieveQuery,
  useLocationUpdateMutation,
  useLocationPartialUpdateMutation,
  useLocationDestroyMutation,
  useLocationOptionsListQuery,
  useLocationSearchListQuery,
  useLookupSolarInstallersListQuery,
  useLookupSpeciesListQuery,
  useLossModelListQuery,
  useLossModelCreateMutation,
  useLossModelStepListQuery,
  useLossModelStepCreateMutation,
  useLossModelStepRetrieveQuery,
  useLossModelStepUpdateMutation,
  useLossModelStepPartialUpdateMutation,
  useLossModelStepDestroyMutation,
  useLossModelRetrieveQuery,
  useLossModelUpdateMutation,
  useLossModelPartialUpdateMutation,
  useLossModelDestroyMutation,
  useLossModelCloneCreateMutation,
  useNewsListQuery,
  useNewsCreateMutation,
  useNewsRetrieveQuery,
  useNewsUpdateMutation,
  useNewsPartialUpdateMutation,
  useNewsDestroyMutation,
  useNotificationsListQuery,
  useNotificationsRetrieveQuery,
  useNotificationsDestroyMutation,
  useNotificationsPinnedUpdateMutation,
  useNotificationsClearCreateMutation,
  useNotificationsCountRetrieveQuery,
  useNotificationsReadCreateMutation,
  usePanelPropertyTypesListQuery,
  usePanelPropertyTypesRetrieveQuery,
  usePanelRuralSpecialisationsListQuery,
  usePanelRuralSpecialisationsRetrieveQuery,
  usePanelValuerAttachmentsRetrieveQuery,
  usePanelValuerAttachmentsDestroyMutation,
  usePanelValuerRegionsListQuery,
  usePanelValuerRegionsRetrieveQuery,
  usePanelValuersListQuery,
  usePanelValuersCreateMutation,
  usePanelValuersRetrieveQuery,
  usePanelValuersUpdateMutation,
  usePanelValuersPartialUpdateMutation,
  usePanelValuersExportRetrieveQuery,
  usePerilListQuery,
  usePerilRetrieveQuery,
  usePerilAnzsicListQuery,
  usePerilCategoryStatisticsListQuery,
  usePerilHistogramListQuery,
  usePerilImpactedListQuery,
  usePerilImpactedCustomersListQuery,
  usePerilLocationRetrieveQuery,
  usePerilLocationLayerListQuery,
  usePerilMermaidRetrieveQuery,
  usePerilMvtStatsRetrieveQuery,
  usePerilSummaryRetrieveQuery,
  usePerilCategoryListQuery,
  usePerilCategoryRetrieveQuery,
  usePerilSourceListQuery,
  usePerilSourceRetrieveQuery,
  usePerilTypeListQuery,
  usePerilTypeRetrieveQuery,
  useProjectListQuery,
  useProjectCreateMutation,
  useProjectRetrieveQuery,
  useProjectUpdateMutation,
  useProjectPartialUpdateMutation,
  useProjectDestroyMutation,
  useProjectComplianceListQuery,
  useProjectHistoryListQuery,
  useProjectUploadFileCreateMutation,
  useProjectAnnualReportingListQuery,
  useProjectAnnualReportingCreateMutation,
  useProjectAnnualReportingRetrieveQuery,
  useProjectAnnualReportingUpdateMutation,
  useProjectAnnualReportingPartialUpdateMutation,
  useProjectAnnualReportingDestroyMutation,
  useAddressTitleListQuery,
  useProjectFeaturesListQuery,
  useProjectFormTemplateRetrieveQuery,
  useProjectImpactSummaryRetrieveQuery,
  useProjectLoansListQuery,
  useProjectApprovalsListQuery,
  useProjectApprovalsCreateMutation,
  useProjectAssetListQuery,
  useProjectAssetCreateMutation,
  useProjectAssetRetrieveQuery,
  useProjectAssetUpdateMutation,
  useProjectAssetPartialUpdateMutation,
  useProjectAssetDestroyMutation,
  useProjectAssetCategoryListQuery,
  useProjectCategoryListQuery,
  useProjectFilesListQuery,
  useProjectFilesRetrieveQuery,
  useProjectFilesDestroyMutation,
  useProjectFormOptionListQuery,
  useProjectStatsRegionalListQuery,
  useProjectStatsSroiListQuery,
  useProjectStatsSummaryListQuery,
  useProjectSubcategoryListQuery,
  useResourceConsentRecordsListQuery,
  useResourceConsentRecordsCreateMutation,
  useResourceConsentRecordsRetrieveQuery,
  useResourceConsentRecordsUpdateMutation,
  useResourceConsentRecordsPartialUpdateMutation,
  useResourceConsentRecordsDestroyMutation,
  useRuralInstructionLetterListQuery,
  useRuralInstructionLetterCreateMutation,
  useRuralInstructionLetterRetrieveQuery,
  useRuralInstructionLetterUpdateMutation,
  useRuralInstructionLetterPartialUpdateMutation,
  useRuralInstructionLetterDestroyMutation,
  useRuralInstructionLetterPdfDataRetrieveQuery,
  useSaleAttachmentsRetrieveQuery,
  useSaleAttachmentsDestroyMutation,
  useSaleExistsListQuery,
  useSalesListQuery,
  useSalesCreateMutation,
  useSalesRetrieveQuery,
  useSalesUpdateMutation,
  useSalesPartialUpdateMutation,
  useSalesAddressListQuery,
  useSalesAddressCreateMutation,
  useSalesAddressRetrieveQuery,
  useSalesAddressUpdateMutation,
  useSalesAddressPartialUpdateMutation,
  useSalesAddressDestroyMutation,
  useSalesDescriptionListQuery,
  useSalesDescriptionCreateMutation,
  useSalesDescriptionRetrieveQuery,
  useSalesDescriptionUpdateMutation,
  useSalesElevationListQuery,
  useSalesElevationCreateMutation,
  useSalesElevationRetrieveQuery,
  useSalesElevationUpdateMutation,
  useSalesElevationPartialUpdateMutation,
  useSalesElevationDestroyMutation,
  useSalesFilesListQuery,
  useSalesFilesCreateMutation,
  useSalesFilesRetrieveQuery,
  useSalesFilesUpdateMutation,
  useSalesFilesDestroyMutation,
  useSalesNeighboursListQuery,
  useSalesNeighboursCreateMutation,
  useSalesNeighboursRetrieveQuery,
  useSalesNeighboursUpdateMutation,
  useSalesNeighboursPartialUpdateMutation,
  useSalesNeighboursDestroyMutation,
  useSalesPdfListQuery,
  useSalesPdfCreateMutation,
  useSalesPdfRetrieveQuery,
  useSalesPdfUpdateMutation,
  useSalesPdfPartialUpdateMutation,
  useSalesPdfDestroyMutation,
  useSalesSmapFamilyListQuery,
  useSalesSmapFamilyCreateMutation,
  useSalesSmapFamilyRetrieveQuery,
  useSalesSmapFamilyUpdateMutation,
  useSalesSmapFamilyPartialUpdateMutation,
  useSalesSmapFamilyDestroyMutation,
  useSalesTitlesListQuery,
  useSalesTitlesUpdateMutation,
  useSalesTitlesRetrieveQuery,
  useSalesTitlesUpdate2Mutation,
  useSalesUnionListQuery,
  useSalesUnionCreateMutation,
  useSalesUnionRetrieveQuery,
  useSalesUnionUpdateMutation,
  useSalesUnionPartialUpdateMutation,
  useSalesUnionDestroyMutation,
  useSalesExportRetrieveQuery,
  useSalesBboxRetrieveQuery,
  useScenarioExportListQuery,
  useScenarioExportCreateMutation,
  useScenarioExportRetrieveQuery,
  useSmapSiblingsListQuery,
  useSmapSiblingsRetrieveQuery,
  useTitleListQuery,
  useTitleRetrieveQuery,
  useSelectedTitleDvrListQuery,
  useSelectedTitleExportPdfQuery,
  useSelectedTitleListQuery,
  useSelectedTitleMemorialListQuery,
  useTitleRetrieveByNumberRetrieveQuery,
  useTitleLikelyAddressListQuery,
  useTitleLikelyAddressRetrieveQuery,
  useTitleReverseSearchListQuery,
  useTitleReverseSearchRetrieveQuery,
  useTitleSearchListQuery,
  useTitleSearchRetrieveQuery,
  useTitlesBboxRetrieveQuery,
  useTradingGroupListQuery,
  useTradingGroupRetrieveQuery,
  useTradingGroupAddressesListQuery,
  useTradingGroupBenchmarkListQuery,
  useTradingGroupCustomersListQuery,
  useTradingGroupEmissionsBenchmarkRetrieveQuery,
  useTradingGroupLendingListQuery,
  useTradingGroupSequestrationRetrieveQuery,
  useTradingGroupValuationsListQuery,
  useTradingGroupAddressesList2Query,
  useTradingGroupAddressesCreateMutation,
  useTradingGroupAddressesRetrieveQuery,
  useTradingGroupAddressesUpdateMutation,
  useTradingGroupAddressesPartialUpdateMutation,
  useTradingGroupAddressesDestroyMutation,
  useTradingGroupPropertiesListQuery,
  useTradingGroupPropertiesRetrieveQuery,
  useTradingGroupLinkAddressCreateMutation,
  useTradingGroupRegionListQuery,
  useTradingGroupRegionRetrieveQuery,
  useUserCurrentRetrieveQuery,
  useUserUserSettingsUpdateMutation,
  useUserOptionsListQuery,
  useUsernamesListQuery,
  useUsernamesRetrieveQuery,
  useValidKpiMeasuresListQuery,
  useValidKpiMeasuresCascaderOptionsQuery,
  useValocitySalesListQuery,
  useValocitySalesRetrieveQuery,
  useValocitySalesUpdateMutation,
  useValocitySalesAddressListQuery,
  useValocitySalesAddressRetrieveQuery,
  useValocitySalesElevationListQuery,
  useValocitySalesElevationRetrieveQuery,
  useValocitySalesNeighboursListQuery,
  useValocitySalesNeighboursRetrieveQuery,
  useValocitySalesSmapFamilyListQuery,
  useValocitySalesSmapFamilyRetrieveQuery,
  useValocitySalesTitlesListQuery,
  useValocitySalesTitlesRetrieveQuery,
  useValocitySalesUnionListQuery,
  useValocitySalesUnionRetrieveQuery,
  useValocitySalesBboxRetrieveQuery,
  useValuationFirmsListQuery,
  useValuationFirmsRetrieveQuery,
  useValuationSearchListQuery,
  useValuationSearchCreateMutation,
  useValuationSearchRetrieveQuery,
  useValuationSearchUpdateMutation,
  useValuationSearchPartialUpdateMutation,
  useValuationSearchDestroyMutation,
  useValuationsListQuery,
  useValuationsCreateMutation,
  useValuationsRetrieveQuery,
  useValuationsUpdateMutation,
  useValuationsPartialUpdateMutation,
  useValuationsDestroyMutation,
  useValuationsPvsSummaryRetrieveQuery,
  useValuationsCombinedResourceConsentsListQuery,
  useValuationsCombinedResourceConsentsRetrieveQuery,
  useValuationsProjectedValuationSummariesListQuery,
  useValuationsProjectedValuationSummariesRetrieveQuery,
  useValuationsResourceConsentRecordsListQuery,
  useValuationsResourceConsentRecordsCreateMutation,
  useValuationsResourceConsentRecordsRetrieveQuery,
  useValuationsResourceConsentRecordsUpdateMutation,
  useValuationsResourceConsentRecordsPartialUpdateMutation,
  useValuationsResourceConsentRecordsDestroyMutation,
  useValuationsTitlesListQuery,
  useValuationsTitlesCreateMutation,
  useValuationsTitlesRetrieveQuery,
  useValuationsTitlesUpdateMutation,
  useValuationsTitlesPartialUpdateMutation,
  useValuationsTitlesDestroyMutation,
  useValuationsResourceConsentRecordsAttachmentsListQuery,
  useValuationsResourceConsentRecordsAttachmentsCreateMutation,
  useValuationsResourceConsentRecordsAttachmentsRetrieveQuery,
  useValuationsResourceConsentRecordsAttachmentsUpdateMutation,
  useValuationsResourceConsentRecordsAttachmentsPartialUpdateMutation,
  useValuationsResourceConsentRecordsAttachmentsDestroyMutation,
  useValuationsTitlesReviewsListQuery,
  useValuationsTitlesReviewsCreateMutation,
  useValuationsTitlesReviewsRetrieveQuery,
  useValuationsTitlesReviewsUpdateMutation,
  useValuationsTitlesReviewsPartialUpdateMutation,
  useValuationsTitlesReviewsDestroyMutation,
} = injectedRtkApi;
