import { InfoCircleOutlined, WarningOutlined } from '@ant-design/icons'
import type { AlertProps } from 'antd'
import React from 'react'
import { AlertText } from '@components/generic'

const ValuationResourceConsentAlert = ({
  consents,
  ...props
}: {
  consents: { holder?: string }[] | undefined
} & AlertProps) => {
  const hasExplicitOwners = consents?.every((consent) => !!consent.holder)
  const warning = !hasExplicitOwners

  const alertProps: AlertProps = {
    icon: <InfoCircleOutlined />,
    ...(warning && {
      icon: <WarningOutlined />,
      message: 'Warning',
      type: 'warning',
    }),
    ...(!consents && {
      message: 'No Consents Found',
      description: 'No consents were found within property boundary.',
    }),
    ...(hasExplicitOwners && {
      message: 'Owners Identified',
      description:
        'Explicit owners were declared in the resource consent data. If in doubt, please double check the data with the relevant local council.',
    }),
    ...(!hasExplicitOwners && {
      description:
        'Explicit ownership of consents was not specified for one or more consents listed above. If consent data is relied upon, please ensure that resource consent belongs to property.',
    }),
  }

  return <AlertText {...{ ...alertProps, ...props }} />
}

export default ValuationResourceConsentAlert
