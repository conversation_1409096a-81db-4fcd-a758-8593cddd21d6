import { startCase } from 'lodash'
import React from 'react'
import { PdfParagraph, PdfTable } from '@components/pdf'
import type {
  ValuationsPvsSummary,
  ValuationsSmapSiblingByFamily,
} from '@store/services/sdk'
import { truthy } from '@util/guards'
import { valueFrom } from '@util/helpers'
import { andJoin } from '@util/string'

type Props = { smapSiblings: ValuationsSmapSiblingByFamily[] }

const getSmapLabel = (key: string) => {
  switch (key) {
    case 'droughtVulnerability':
      return 'Drought Vulnerability (if not irrigated)'
    case 'name':
      return 'Key'
    default:
      return startCase(key)
  }
}

const fieldToColumn =
  (columnKey: string) =>
  (
    families: Record<string, string | null | undefined>,
    sibling: ValuationsSmapSiblingByFamily
  ) => ({
    ...families,
    [sibling.key]: sibling[columnKey as keyof ValuationsSmapSiblingByFamily],
  })

export const SoilTable = ({ smapSiblings }: Props) => {
  const [firstSibling] = smapSiblings

  const { key, family, ...properties } = {
    ...firstSibling,
  } as ValuationsSmapSiblingByFamily

  const columns = [
    {
      key: 'property',
      isHeader: true,
      title: 'Family',
    },
    ...smapSiblings
      .filter((smap) => truthy(smap.key))
      .map(({ key, family: title }) => ({
        key,
        title,
      })),
  ]

  const fill = Array.from(Array(7 - columns.length), (_, i) => ({
    key: `${columns[i]?.key || 'column-fill'}-${i}`,
    title: '',
  }))

  const rows = Object.keys(properties).map((columnKey) => ({
    property: getSmapLabel(columnKey),
    ...smapSiblings.reduce(fieldToColumn(columnKey), {}),
  }))

  return <PdfTable columns={[...columns, ...fill]} rows={rows} />
}

const Soils = ({
  smapSiblings,
  smapArea,
}: Props & { smapArea: ValuationsPvsSummary['smapArea'] }) => {
  const familyNames = andJoin([
    ...new Set(smapSiblings.map(valueFrom('family'))),
  ])

  return (
    <PdfParagraph>
      Manaaki Whenua Landcare Research S-map online shows the property as having
      predominantly {familyNames} soils, comprising {smapArea} hectares,
      together with smaller areas of “other” which make up the remaining land
      area.
    </PdfParagraph>
  )
}

export default Soils
