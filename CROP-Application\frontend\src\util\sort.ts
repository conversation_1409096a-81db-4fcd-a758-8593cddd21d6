import { getDateTextTime } from './string'
import type { KeysMatching } from './types'

export const SORT_DIRECTIONS = ['ascend' as const, 'descend' as const]

export type Sorter<T> = (a: T, b: T) => number

export type PropertySorter<T> = (property: keyof T) => Sorter<T>

export const sortBooleanProperty =
  <T>(property: keyof T) =>
  (a: T, b: T) =>
    Number(!!a[property]) - Number(!!b[property])

/**
 * @param a - '01/02/2000'
 * @param b - '02/02/2000'
 */
export const sortDateTextTime = (a: string, b: string) =>
  getDateTextTime(a) - getDateTextTime(b)

// TODO: Add guards
/**
 * @property property - 'date'
 * @returns  sortDateTextTime
 */
export const sortDateTextTimeProperty =
  <T>(property: KeysMatching<T, string | undefined>) =>
  (a: T, b: T) =>
    sortDateTextTime(a[property] as string, b[property] as string)

export const sortDateStringAscending = (
  ISODateStringA: string,
  ISODateStringB: string
) => Date.parse(ISODateStringB) - Date.parse(ISODateStringA)

export const sortDateStringPropertyAscending =
  <T>(property: KeysMatching<T, string | undefined | null>) =>
  (a: T, b: T) =>
    sortDateStringAscending(b[property] as string, a[property] as string)

export const sortDateStringDescending = (
  ISODateStringA: string,
  ISODateStringB: string
) => Date.parse(ISODateStringA) - Date.parse(ISODateStringB)

export const sortDateStringPropertyDescending =
  <T>(property: KeysMatching<T, string | undefined>) =>
  (a: T, b: T) =>
    sortDateStringDescending(b[property] as string, a[property] as string)

export const sortDigitProperty =
  <T>(property: keyof T) =>
  (a: T, b: T) =>
    Number(a[property]) - Number(b[property])

// TODO: Add guards
export const sortTextProperty =
  <T>(property: keyof T) =>
  (a: T, b: T) =>
    (a[property] ?? '').toString().localeCompare((b[property] ?? '').toString())
