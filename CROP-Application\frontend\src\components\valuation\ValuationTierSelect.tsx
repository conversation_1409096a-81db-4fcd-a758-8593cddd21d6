import { Select, type SelectProps } from 'antd'
import type { Valuations } from '@store/services/sdk'
import { PLACEHOLDER_CHAR } from '@util/const'

const values: Valuations['tier'][] = [1, 2, 3]

const options = values.map((value) => ({ label: value, value }))

const ValuationTierSelect = ({ ...props }: SelectProps) => {
  return <Select options={options} placeholder={PLACEHOLDER_CHAR} {...props} />
}

export default ValuationTierSelect
