import { Table } from 'antd'
import type { RowSelectMethod } from 'antd/lib/table/interface'
import type React from 'react'
import { Title } from '@components/typography'
import type {
  LinzTitlesMemorials,
  ValuationsSavedTitle,
} from '@store/services/sdk'

type Props = {
  defaultSelectedRowKeys: React.Key[]
  title: ValuationsSavedTitle
  onChange: (
    selectedRowKeys: React.Key[],
    selectedRows: LinzTitlesMemorials[],
    info: {
      type: RowSelectMethod
    }
  ) => void
}

const columns = [
  {
    dataIndex: 'instrumentNumber',
    title: 'Number',
    key: 'number',
  },
  {
    dataIndex: 'instrumentType',
    title: 'Type',
    key: 'type',
  },
  {
    dataIndex: 'instrumentLodgedDatetime',
    title: 'Lodged',
    key: 'lodged',
  },
]

const expandable = {
  expandedRowRender: (memorial: LinzTitlesMemorials) => (
    <dl style={{ margin: 8 }}>
      <dt>Description</dt>
      <dd>{memorial.memorialText}</dd>
    </dl>
  ),
}

const ValuationTitleMemorials = ({
  defaultSelectedRowKeys,
  title,
  onChange,
}: Props) => {
  return (
    <Table
      pagination={false}
      scroll={{ x: 100 }}
      title={() => (
        <Title style={{ margin: '0.5em 0 0' }}>{title.titleNo} Memorials</Title>
      )}
      dataSource={title.memorials}
      rowKey={(row) => row.fid}
      columns={columns}
      expandable={expandable}
      rowSelection={{
        defaultSelectedRowKeys,
        type: 'checkbox',
        onChange,
      }}
    />
  )
}

export default ValuationTitleMemorials
