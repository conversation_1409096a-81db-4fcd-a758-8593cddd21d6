import type BigNumber from 'bignumber.js'
import { ZERO, fromBigNumber, toBigNumber } from '@types'
import type { Asset } from './Asset'
import {
  type LandAssetMetric,
  type SerializableLandAssetMetric,
  deserializeLandAssetMetric,
  serializeLandAssetMetric,
} from './LandAssetMetric'
import type {
  HighestAndBestUseType,
  HighestAndBestUseTypeMap,
  LandClassType,
} from './ValuationTypes'

export interface SerializableLandAsset extends Asset {
  improvementCoveredAreaM2: number

  landClass: LandClassType
  landClassId?: number | string // used in form

  // land asset used as the sink for any remaining area on this asset
  balanceLandAsset: number | undefined
  // land class of the above land asset, this is also what is passed to the backend when creating an asset, since at that point the balance land asset may not exist.
  balanceLandClass: number | undefined

  nonTitledLandType: string | undefined

  isProductive: boolean

  hbsRemainingMetric?: number

  secondaryHighestAndBestUseType?: number

  metrics: SerializableLandAssetMetric[]

  // computed (on backend primarily - frontend when manipulating)
  totalArea: string
  includedRemainingArea: string

  totalEffectiveArea: string
  totalIneffectiveArea: string
}

export function deserializeLandAsset(landAsset: SerializableLandAsset) {
  const {
    metrics,
    totalArea,
    includedRemainingArea,
    totalEffectiveArea,
    totalIneffectiveArea,
    ...rest
  } = landAsset
  return {
    ...rest,
    metrics: metrics.map(deserializeLandAssetMetric),
    ...toBigNumber({
      totalArea,
      includedRemainingArea,
      totalEffectiveArea,
      totalIneffectiveArea,
    }),
  }
}

export type LandAsset = ReturnType<typeof deserializeLandAsset>

export function serializeLandAsset(
  landAsset: LandAsset
): SerializableLandAsset {
  const {
    metrics,
    totalArea,
    includedRemainingArea,
    totalEffectiveArea,
    totalIneffectiveArea,
    ...rest
  } = landAsset
  return {
    ...rest,
    metrics: metrics.map(serializeLandAssetMetric),
    ...fromBigNumber({
      totalArea,
      includedRemainingArea,
      totalEffectiveArea,
      totalIneffectiveArea,
    }),
  }
}

export const computeAssetMetricTotalArea = (
  metric: LandAssetMetric,
  totalAssetArea: BigNumber
) => {
  if (metric.isProductive) {
    if (metric?.byPercentage) {
      return (metric?.areaPercentage ?? ZERO)
        .div(100)
        .multipliedBy(totalAssetArea ?? ZERO)
    }
    return metric?.areaM2 ?? ZERO
  }
  return totalAssetArea ?? ZERO
}

export const computeLandAssetMetricDollarPerHectare = (
  landAsset: LandAsset,
  assetMetric: LandAssetMetric,
  highestAndBestUseType: HighestAndBestUseType
) => {
  const landClass = landAsset.landClass
  if (
    highestAndBestUseType.categories.indexOf('Horticulture & Viticulture') !==
    -1
  ) {
    if (landClass?.isProductive) {
      const totalValueHa = (assetMetric.licenceValueHa ?? ZERO)
        .plus(assetMetric.canopyPlantedValueHa ?? ZERO)
        .plus(assetMetric.vineStructuresHa ?? ZERO)
      return totalValueHa.isNaN() ? ZERO : totalValueHa
    }
    return assetMetric.dollarPerHectare.isNaN()
      ? ZERO
      : assetMetric.dollarPerHectare
  }
  return assetMetric.dollarPerHectare.isNaN()
    ? ZERO
    : assetMetric.dollarPerHectare
}

export const updateLandAssetMetric = (
  landAssetMetric: LandAssetMetric,
  landAsset: LandAsset,
  defaultHighestAndBestUseType: HighestAndBestUseType,
  valuationTypes: HighestAndBestUseTypeMap
): LandAssetMetric => {
  const bestUseType =
    valuationTypes?.[landAsset.secondaryHighestAndBestUseType ?? -1] ??
    defaultHighestAndBestUseType

  const dollarPerHectare = computeLandAssetMetricDollarPerHectare(
    landAsset,
    landAssetMetric,
    bestUseType
  )
  const totalArea = computeAssetMetricTotalArea(
    landAssetMetric,
    landAsset.totalArea
  )

  landAssetMetric.totalArea = totalArea
  landAssetMetric.totalHectares = landAssetMetric.totalArea.div(10000)

  if (landAssetMetric.isProductive) {
    landAssetMetric.total_AEP = (
      landAssetMetric.aepPerHectare ?? ZERO
    ).multipliedBy(landAssetMetric.totalArea.div(10000))
  } else {
    landAssetMetric.total_AEP = ZERO
  }

  if (landAsset.landClass.useTotalValue && landAssetMetric.totalValue) {
    landAssetMetric.total_LWB = landAssetMetric.totalValue
  } else {
    landAssetMetric.total_LWB = dollarPerHectare.multipliedBy(
      landAssetMetric.totalArea.div(10000)
    )
  }

  landAssetMetric.total_LWBBy_AEP = landAssetMetric.total_AEP.gt(0)
    ? landAssetMetric.total_LWB.div(landAssetMetric.total_AEP)
    : ZERO

  return landAssetMetric
}

type LandAssetMetricKeys = Array<keyof LandAssetMetric>
export function landAssetMetricMatches(
  first: LandAssetMetric,
  second: LandAssetMetric
) {
  const fieldsToMatch: LandAssetMetricKeys = [
    'landClassName',
    'irrigation',
    'irrigationPrimaryApplication',
    'irrigationSource',
    'irrigationWaterCost',
    'maturity',
    'coverType',
    'orchardType',
    'plantedYear',
    'plantingRegime',
    'plantingVariety',
  ]
  for (const field of fieldsToMatch) {
    if (first[field] !== second[field]) {
      return false
    }
  }
  return true
}
