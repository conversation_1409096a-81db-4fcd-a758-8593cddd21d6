import { getLegend } from '@/types/GISLayerDescriptors'
import { groupBy } from 'lodash'
import React, { memo } from 'react'
import { PdfTable } from '@components/pdf'
import type { ValuationsAnzUnionFeatureList } from '@store/services/sdk'
import { truthy } from '@util/guards'
import { sumProperty } from './helpers'

type Props = {
  union: ValuationsAnzUnionFeatureList
  totalHectares: string
}

const legend = getLegend('luc')

const getDescriptor = (key: string) =>
  (legend?.getDescriptor(key) ?? '').replace(/^.\s-\s/, '')

const columns = [
  {
    key: 'luc',
    title: 'LUC',
    isHeader: true,
    weighting: 0.3,
  },
  { key: 'definition', title: 'Definition' },
  { key: 'area', title: 'Area (ha)' },
  { key: 'areaPercent', title: '% of Calc. Area' },
]

// TODO: Do groupings on server
const ValuationPvsLuc = ({ union }: Props) => {
  const features = union.features.filter(
    (feature) => typeof feature.properties?.luc === 'string'
  )

  const groupedFeatures = groupBy(features, (f) => f.properties?.luc)

  if (!groupedFeatures) return null

  return (
    <>
      <PdfTable
        margins
        striped
        columns={columns}
        rows={Object.entries(groupedFeatures).map(([key, features]) => {
          const properties = features
            .map((feature) => feature.properties)
            .filter(truthy)
          const lucArea = sumProperty(properties, 'prorataArea') / 1e4
          const areaPercent = `${sumProperty(
            properties,
            'geometryAreaPercentage'
          ).toFixed(2)}%`

          return {
            luc: key,
            definition: getDescriptor(key),
            area: lucArea.toFixed(4),
            areaPercent,
          }
        })}
      />
    </>
  )
}

export default memo(ValuationPvsLuc)
