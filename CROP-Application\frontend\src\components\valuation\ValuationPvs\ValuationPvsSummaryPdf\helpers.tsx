import { Text } from '@react-pdf/renderer'
import { startCase } from 'lodash'
import converter from 'number-to-words'
import plural from 'pluralize'
import type React from 'react'
import { PdfText, rem, stylesheet } from '@components/pdf'
import type {
  ValuationsPvsSummary,
  ValuationsSavedTitleFeature,
  ValuationsSmapSiblingByFamily,
} from '@store/services/sdk'
import { access } from '@util/helpers'
import { formatNumber } from '@util/labels'
import { andJoin } from '@util/string'

const pluralize = plural as (word: string, count?: number) => string

export const marketValueToWords = (value: number) =>
  `${startCase(converter.toWords(value))} Dollars`

export const soilFamiliesList = (
  smapSiblings: ValuationsSmapSiblingByFamily[]
) => andJoin([...new Set(smapSiblings.map(access('family')))])

export const estateDescriptionLegalSubstring = (
  description: string | null | undefined
) => (description ?? '').split(', ').slice(2, -1).join(', ')

export type ValuationProps = {
  valuation: ValuationsPvsSummary
}

export type PvsSummary = ValuationsPvsSummary['summary']
export type PvsHighestAndBestUseSummaries =
  ValuationsPvsSummary['summary']['highestAndBestUseSummary']
export type PvsHighestAndBestUseSummary = PvsHighestAndBestUseSummaries[number]

export const asString = (text: unknown): string => {
  if (typeof text !== 'string') return ''
  return text
}

export const textInsertion = (text: unknown): string => {
  return `${asString(text).trim()} `
}

const matchesTitleType =
  (type: string) => (title: ValuationsSavedTitleFeature) =>
    title.properties?.type?.toLowerCase() === type
const isFreeholdTitle = matchesTitleType('freehold')
const isLeaseholdTitle = matchesTitleType('leasehold')
const sumTitleArea = (sum: number, title: ValuationsSavedTitleFeature) =>
  (sum ?? 0) + +(title.properties?.area ?? 0)

export const elevationSubstring = (climateString = '') =>
  climateString.match(/(The property).*(?= The data)/)?.[0]

export const sumPropertyReducer =
  <T extends object>(property: keyof T) =>
  (a: number, b: T) =>
    a + (+b[property] || 0)

export const sumProperty = <T extends object>(
  objArray: T[],
  property: keyof T
) => objArray.reduce(sumPropertyReducer(property), 0)

export const averageProperty = <T extends object>(
  objArray: T[],
  property: keyof T
) => {
  return (
    sumProperty(objArray, property) /
    objArray.filter((obj) => !Number.isNaN(+obj[property])).length
  )
}

export const mostOccurrences = (strings: string[]) =>
  Object.entries(
    strings.reduce<Record<string, number>>((map, str) => {
      map[str] = (map[str] || 0) + 1
      return map
    }, {})
  ).sort(([, aCount], [, bCount]) => bCount - aCount)[0][0]

export const getVerboseLabel = (key: string) => {
  switch (key) {
    case 'totalHectares':
      return 'Total Area (ha)'
    case 'totalEffectiveHectares':
      return 'Eff. Area (ha)'
    case 'totalAEP':
      return 'Average Efficient Production (AEP)'
    case 'total_LWB':
      return 'Land without Building Value (LWB)'
    case 'improvementsMarketValue':
      return 'Added Value of Buildings'
    default:
      return startCase(key)
  }
}

export const MarketValueSummary = ({
  summary,
}: {
  summary: ValuationsPvsSummary['summary']
}) => {
  const { marketValue, marketValueDollars } = summary
  const marketValueText = `${startCase(converter.toWords(marketValue))} Dollars`

  return (
    <>
      <Text
        style={{
          ...stylesheet.headingL,
          textAlign: 'center',
          lineHeight: 1,
          paddingTop: rem(2),
        }}
      >
        {marketValueText}
      </Text>
      <Text
        style={{
          ...stylesheet.headingL,
          textAlign: 'center',
          lineHeight: 1,
          paddingTop: 0,
          paddingBottom: rem(2),
        }}
      >
        {marketValueDollars} (plus GST if any)
      </Text>
    </>
  )
}

export const LegalDescription = ({ valuation }: ValuationProps) => {
  const titles = valuation?.titles.features || []
  const freeholdTitles = titles.filter(isFreeholdTitle)
  const leaseholdTitles = titles.filter(isLeaseholdTitle)
  const freeholdCount = freeholdTitles.length
  const leaseholdCount = leaseholdTitles.length
  const freeholdArea = freeholdTitles.reduce(sumTitleArea, 0).toFixed(4)
  const leaseholdArea = leaseholdTitles.reduce(sumTitleArea, 0).toFixed(4)

  const freeholdDesc = `a total freehold area of ${freeholdArea} ha and is currently held across ${freeholdCount} ${pluralize(
    'Record',
    freeholdCount
  )} of Title.`
  const leaseholdDesc = `There is also a leasehold title of which is ${leaseholdArea} ha held across ${leaseholdCount} ${pluralize(
    'Record',
    leaseholdCount
  )} of Title.`
  const legalDescription = `The property comprises ${freeholdDesc} ${
    leaseholdCount > 0 ? leaseholdDesc : ''
  }`

  return (
    <PdfText>
      {legalDescription} The legal descriptions as per the Records of Title are
      outlined in the following table:
    </PdfText>
  )
}

export const formattedAepPerHectare = (value: string, unit: string) =>
  `${formatNumber(value)} ${unit}`

export const replaceHbuGlyph = (hbu: string | undefined = '') =>
  hbu.replace(' →', ',')

export const replaceTrademark = (value: string | undefined = '') =>
  value.replace('™', ' TM')
