import { baseApi as api } from "../baseApi";
export const addTagTypes = ["esst-report"] as const;
const injectedRtkApi = api
  .enhanceEndpoints({
    addTagTypes,
  })
  .injectEndpoints({
    endpoints: (build) => ({
      esstReportItemsList: build.query<
        EsstReportItemsListApiResponse,
        EsstReportItemsListApiArg
      >({
        query: () => ({ url: `/api/esst/report-items/` }),
        providesTags: ["esst-report"],
      }),
      esstReportItemsCreate: build.mutation<
        EsstReportItemsCreateApiResponse,
        EsstReportItemsCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/`,
          method: "POST",
          body: queryArg.esstReportItemCreate,
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportItemsRetrieve: build.query<
        EsstReportItemsRetrieveApiResponse,
        EsstReportItemsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/${queryArg.pk}/`,
        }),
        providesTags: ["esst-report"],
      }),
      esstReportItemsUpdate: build.mutation<
        EsstReportItemsUpdateApiResponse,
        EsstReportItemsUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.esstReportItemUpdate,
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportItemsPartialUpdate: build.mutation<
        EsstReportItemsPartialUpdateApiResponse,
        EsstReportItemsPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedEsstReportItemUpdate,
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportItemsDestroy: build.mutation<
        EsstReportItemsDestroyApiResponse,
        EsstReportItemsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportItemsErrorsRetrieve: build.query<
        EsstReportItemsErrorsRetrieveApiResponse,
        EsstReportItemsErrorsRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/${queryArg.pk}/errors/`,
          params: { section_key: queryArg.sectionKey },
        }),
        providesTags: ["esst-report"],
      }),
      esstReportItemsFilteredSchemaRetrieve: build.query<
        EsstReportItemsFilteredSchemaRetrieveApiResponse,
        EsstReportItemsFilteredSchemaRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/${queryArg.pk}/filtered_schema/`,
        }),
        providesTags: ["esst-report"],
      }),
      esstReportItemsFormDataRetrieve: build.query<
        EsstReportItemsFormDataRetrieveApiResponse,
        EsstReportItemsFormDataRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/${queryArg.pk}/form_data/`,
        }),
        providesTags: ["esst-report"],
      }),
      esstReportItemsSummaryRetrieve: build.query<
        EsstReportItemsSummaryRetrieveApiResponse,
        EsstReportItemsSummaryRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/report-items/${queryArg.pk}/summary/`,
        }),
        providesTags: ["esst-report"],
      }),
      esstReportsList: build.query<
        EsstReportsListApiResponse,
        EsstReportsListApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/`,
          params: {
            current_user: queryArg.currentUser,
            customer_group_id: queryArg.customerGroupId,
            page: queryArg.page,
            size: queryArg.size,
            trading_group_id: queryArg.tradingGroupId,
            unlinked: queryArg.unlinked,
          },
        }),
        providesTags: ["esst-report"],
      }),
      esstReportsCreate: build.mutation<
        EsstReportsCreateApiResponse,
        EsstReportsCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/`,
          method: "POST",
          body: queryArg.esstReportCreate,
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportsRetrieve: build.query<
        EsstReportsRetrieveApiResponse,
        EsstReportsRetrieveApiArg
      >({
        query: (queryArg) => ({ url: `/api/esst/reports/${queryArg.pk}/` }),
        providesTags: ["esst-report"],
      }),
      esstReportsUpdate: build.mutation<
        EsstReportsUpdateApiResponse,
        EsstReportsUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/${queryArg.pk}/`,
          method: "PUT",
          body: queryArg.esstReport,
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportsPartialUpdate: build.mutation<
        EsstReportsPartialUpdateApiResponse,
        EsstReportsPartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/${queryArg.pk}/`,
          method: "PATCH",
          body: queryArg.patchedEsstReport,
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportsDestroy: build.mutation<
        EsstReportsDestroyApiResponse,
        EsstReportsDestroyApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/${queryArg.pk}/`,
          method: "DELETE",
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportsCompletePartialUpdate: build.mutation<
        EsstReportsCompletePartialUpdateApiResponse,
        EsstReportsCompletePartialUpdateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/${queryArg.pk}/complete/`,
          method: "PATCH",
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportsExportRetrieve: build.query<
        EsstReportsExportRetrieveApiResponse,
        EsstReportsExportRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/${queryArg.pk}/export/`,
        }),
        providesTags: ["esst-report"],
      }),
      esstReportsLinkEsstCreate: build.mutation<
        EsstReportsLinkEsstCreateApiResponse,
        EsstReportsLinkEsstCreateApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/${queryArg.pk}/link_esst/`,
          method: "POST",
          body: queryArg.linkEsst,
        }),
        invalidatesTags: ["esst-report"],
      }),
      esstReportsNextAuditDateRetrieve: build.query<
        EsstReportsNextAuditDateRetrieveApiResponse,
        EsstReportsNextAuditDateRetrieveApiArg
      >({
        query: (queryArg) => ({
          url: `/api/esst/reports/next_audit_date/`,
          params: {
            audit_pathway: queryArg.auditPathway,
            canterbury_fep_grade: queryArg.canterburyFepGrade,
            has_changed_management_or_farm_systems:
              queryArg.hasChangedManagementOrFarmSystems,
            irrigation_category: queryArg.irrigationCategory,
            last_audit_date: queryArg.lastAuditDate,
          },
        }),
        providesTags: ["esst-report"],
      }),
    }),
    overrideExisting: false,
  });
export { injectedRtkApi as enhancedApi };
export type EsstReportItemsListApiResponse =
  /** status 200  */ EsstReportItemExcerpt[];
export type EsstReportItemsListApiArg = void;
export type EsstReportItemsCreateApiResponse =
  /** status 201  */ EsstReportItemExcerpt;
export type EsstReportItemsCreateApiArg = {
  esstReportItemCreate: EsstReportItemCreate;
};
export type EsstReportItemsRetrieveApiResponse =
  /** status 200  */ EsstReportItem;
export type EsstReportItemsRetrieveApiArg = {
  /** A unique integer value identifying this esst report item. */
  pk: number;
};
export type EsstReportItemsUpdateApiResponse =
  /** status 200  */ EsstReportItemUpdate;
export type EsstReportItemsUpdateApiArg = {
  /** A unique integer value identifying this esst report item. */
  pk: number;
  esstReportItemUpdate: EsstReportItemUpdate;
};
export type EsstReportItemsPartialUpdateApiResponse =
  /** status 200  */ EsstReportItemUpdate;
export type EsstReportItemsPartialUpdateApiArg = {
  /** A unique integer value identifying this esst report item. */
  pk: number;
  patchedEsstReportItemUpdate: PatchedEsstReportItemUpdate;
};
export type EsstReportItemsDestroyApiResponse = unknown;
export type EsstReportItemsDestroyApiArg = {
  /** A unique integer value identifying this esst report item. */
  pk: number;
};
export type EsstReportItemsErrorsRetrieveApiResponse =
  /** status 200  */ FieldErrors;
export type EsstReportItemsErrorsRetrieveApiArg = {
  /** A unique integer value identifying this esst report item. */
  pk: number;
  sectionKey?: string;
};
export type EsstReportItemsFilteredSchemaRetrieveApiResponse =
  /** status 200  */ FieldNode;
export type EsstReportItemsFilteredSchemaRetrieveApiArg = {
  /** A unique integer value identifying this esst report item. */
  pk: number;
};
export type EsstReportItemsFormDataRetrieveApiResponse =
  /** status 200  */ EsstReportItemFormData;
export type EsstReportItemsFormDataRetrieveApiArg = {
  /** A unique integer value identifying this esst report item. */
  pk: number;
};
export type EsstReportItemsSummaryRetrieveApiResponse =
  /** status 200  */ FieldNode;
export type EsstReportItemsSummaryRetrieveApiArg = {
  /** A unique integer value identifying this esst report item. */
  pk: number;
};
export type EsstReportsListApiResponse =
  /** status 200  */ PaginatedEsstReportList;
export type EsstReportsListApiArg = {
  currentUser?: boolean;
  customerGroupId?: number;
  /** A page number within the paginated result set. */
  page?: number;
  /** Number of results to return per page. */
  size?: number;
  tradingGroupId?: string;
  unlinked?: boolean;
};
export type EsstReportsCreateApiResponse = /** status 201  */ EsstReport;
export type EsstReportsCreateApiArg = {
  esstReportCreate: EsstReportCreate;
};
export type EsstReportsRetrieveApiResponse = /** status 200  */ EsstReport;
export type EsstReportsRetrieveApiArg = {
  /** A unique integer value identifying this esst report. */
  pk: number;
};
export type EsstReportsUpdateApiResponse = /** status 200  */ EsstReport;
export type EsstReportsUpdateApiArg = {
  /** A unique integer value identifying this esst report. */
  pk: number;
  esstReport: EsstReport;
};
export type EsstReportsPartialUpdateApiResponse = /** status 200  */ EsstReport;
export type EsstReportsPartialUpdateApiArg = {
  /** A unique integer value identifying this esst report. */
  pk: number;
  patchedEsstReport: PatchedEsstReport;
};
export type EsstReportsDestroyApiResponse = unknown;
export type EsstReportsDestroyApiArg = {
  /** A unique integer value identifying this esst report. */
  pk: number;
};
export type EsstReportsCompletePartialUpdateApiResponse = /** status 200  */ {
  [key: string]: any;
};
export type EsstReportsCompletePartialUpdateApiArg = {
  /** A unique integer value identifying this esst report. */
  pk: number;
};
export type EsstReportsExportRetrieveApiResponse =
  /** status 200  */ EsstReportExport;
export type EsstReportsExportRetrieveApiArg = {
  /** A unique integer value identifying this esst report. */
  pk: number;
};
export type EsstReportsLinkEsstCreateApiResponse = unknown;
export type EsstReportsLinkEsstCreateApiArg = {
  /** A unique integer value identifying this esst report. */
  pk: number;
  linkEsst: LinkEsst;
};
export type EsstReportsNextAuditDateRetrieveApiResponse =
  /** status 200  */ string;
export type EsstReportsNextAuditDateRetrieveApiArg = {
  auditPathway: string;
  canterburyFepGrade?: string;
  hasChangedManagementOrFarmSystems?: string;
  irrigationCategory?: string;
  lastAuditDate: string;
};
export type Username = {
  username: string;
  id: number;
  pk: number;
  firstName?: string;
  name: string;
  lastName?: string;
  title: string;
  mobileNumber: string;
  email?: string;
};
export type EsstReportable = {
  id: number;
  name: string;
  esstReportItemId: number;
};
export type EsstReportItemExcerpt = {
  id: number;
  position: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  esstReportId: number;
  createdBy: Username;
  updatedBy: Username;
  reportables: EsstReportable[];
  anzsics: string[];
};
export type CustomerProxy = {
  realCustomer?: number;
  placeholderCustomer?: number;
  entityId?: number;
  entitySource: "system" | "placeholder";
};
export type EsstCustomerCreate = {
  esstCustomerGroupId: number;
  customer: CustomerProxy;
};
export type EsstCustomerPropertyCreate = {
  esstCustomerGroupId: number;
  physicalAddress: string;
  propertyId?: string;
};
export type EsstReportItemCreate = {
  esstReportId: number;
  esstCustomers?: EsstCustomerCreate[];
  esstCustomerProperties?: EsstCustomerPropertyCreate[];
  additionalAnzsicIds?: number[];
};
export type EsstReportValue = {
  id: number;
  esstReportItemId: number;
  fieldNodeId: number;
  value?: string;
  fieldKey: string;
  sectionKey: string;
};
export type EsstReportItem = {
  id: number;
  position: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  createdBy: Username;
  updatedBy: Username;
  fieldSchemaId: number;
  esstReportId: number;
  reportGroupType: "ENTITIES" | "PROPERTIES" | "GROUP";
  esstCustomerIds?: number[];
  esstCustomerPropertyIds?: number[];
  reportables: EsstReportable[];
  anzsics: string[];
  values?: EsstReportValue[];
  valid: boolean;
  completed: boolean;
};
export type EsstReportItemUpdate = {
  id: number;
  esstCustomerIds?: number[];
  esstCustomerPropertyIds?: number[];
  values?: EsstReportValue[];
};
export type PatchedEsstReportItemUpdate = {
  id?: number;
  esstCustomerIds?: number[];
  esstCustomerPropertyIds?: number[];
  values?: EsstReportValue[];
};
export type FieldErrors = {
  fieldKey: string;
  description: string;
  error: string | null;
  sectionKey: string;
  sectionDescription: string;
  descendants: FieldErrors[];
};
export type FieldNode = {
  id: number;
  fieldKey: string;
  fieldType:
    | "DOCUMENT"
    | "SECTION"
    | "STRING"
    | "TEXT"
    | "BOOLEAN"
    | "RADIO"
    | "SELECT"
    | "SELECT_MANY"
    | "ANY_SELECT"
    | "DATE";
  description: string;
  value: string;
  ancestorId: number | null;
  descendants: FieldNode[];
  metadata?: {
    [key: string]: any;
  };
};
export type EsstReportItemFormData = {
  id: number;
  esstReportId: number;
  reportGroupType: "ENTITIES" | "PROPERTIES" | "GROUP";
  values: EsstReportValue[];
  updatedAt: string;
};
export type PlaceholderCustomer = {
  pk: number;
  anzsicId: number;
  fullName: string;
};
export type PlaceholderCustomerGroupWrite = {
  pk: number;
  businessDomain: "COMMERCIAL" | "AGRI" | "CORPORATE";
  name: string;
  anzsic: string;
  anzsicId: number;
  customers: PlaceholderCustomer[];
};
export type EntityProxy = {
  entityId?: number;
  entitySource: "system" | "placeholder";
};
export type EsstCustomerOption = {
  esstReportItemId: number;
  customer: EntityProxy;
  name: string;
};
export type EsstCustomerPropertyOption = {
  esstReportItemId: number;
  propertyId: string;
  physicalAddress: string;
};
export type EsstCustomerGroup = {
  id: number;
  esstCustomerProperties: EsstReportable[];
  additionalAnzsicIds?: number[];
  customerGroupId?: number | null;
  tradingGroupId?: string | null;
  placeholderCustomerGroup?: PlaceholderCustomerGroupWrite | null;
  esstReportId: number;
  reportGroupType: "ENTITIES" | "PROPERTIES" | "GROUP";
  esstCustomers: EsstReportable[];
  name: string;
  anzsic: string;
  customers: EsstCustomerOption[];
  properties: EsstCustomerPropertyOption[];
  unlinked: boolean;
};
export type EsstReport = {
  id: number;
  name: string;
  fieldSchemaId: number;
  esstCustomerGroup: EsstCustomerGroup;
  esstReportItems: EsstReportItemExcerpt[];
  createdAt: string;
  updatedAt: string;
  status: string;
  createdBy: Username;
  updatedBy: Username;
  completedAt: string;
  unlinked: boolean;
  anzsics: string[];
};
export type PaginatedEsstReportList = {
  count?: number;
  next?: string | null;
  previous?: string | null;
  results?: EsstReport[];
  currentPage?: number;
  numPages?: number;
  firstPage?: string;
  lastPage?: string;
};
export type ExistingGroup = {
  entityGroupId?: string;
  entityType: "customerGroup" | "tradingGroup";
};
export type EsstCustomerGroupCreate = {
  id: number;
  esstCustomerProperties: EsstReportable[];
  additionalAnzsicIds?: number[];
  entityGroupType: "existing" | "unlinked";
  existingGroup?: ExistingGroup;
  customerGroupId: number | null;
  tradingGroupId: string | null;
  placeholderCustomerGroup?: PlaceholderCustomerGroupWrite | null;
  esstReportId: number;
  reportGroupType: "ENTITIES" | "PROPERTIES" | "GROUP";
  esstCustomers: EsstReportable[];
  name: string;
  anzsic: string;
  customers: EsstCustomerOption[];
  properties: EsstCustomerPropertyOption[];
  unlinked: boolean;
};
export type EsstReportCreate = {
  esstCustomerGroup: EsstCustomerGroupCreate;
};
export type PatchedEsstReport = {
  id?: number;
  name?: string;
  fieldSchemaId?: number;
  esstCustomerGroup?: EsstCustomerGroup;
  esstReportItems?: EsstReportItemExcerpt[];
  createdAt?: string;
  updatedAt?: string;
  status?: string;
  createdBy?: Username;
  updatedBy?: Username;
  completedAt?: string;
  unlinked?: boolean;
  anzsics?: string[];
};
export type EsstReportItemExport = {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  createdBy: Username;
  updatedBy: Username;
  reportables: string;
  summary: FieldNode;
};
export type EsstReportExport = {
  id: number;
  name: string;
  esstCustomerGroup: EsstCustomerGroup;
  esstReportItems: EsstReportItemExport[];
  createdAt: string;
  updatedAt: string;
  createdBy: Username;
  updatedBy: Username;
  status: string;
  completedAt: string;
  anzsics: string[];
};
export type LinkEsstCustomer = {
  placeholderCustomer: number;
  customer: number;
};
export type LinkEsst = {
  entityGroupId?: string;
  groupType: "customer_group" | "trading_group";
  entities: LinkEsstCustomer[];
};
export const {
  useEsstReportItemsListQuery,
  useLazyEsstReportItemsListQuery,
  useEsstReportItemsCreateMutation,
  useEsstReportItemsRetrieveQuery,
  useLazyEsstReportItemsRetrieveQuery,
  useEsstReportItemsUpdateMutation,
  useEsstReportItemsPartialUpdateMutation,
  useEsstReportItemsDestroyMutation,
  useEsstReportItemsErrorsRetrieveQuery,
  useLazyEsstReportItemsErrorsRetrieveQuery,
  useEsstReportItemsFilteredSchemaRetrieveQuery,
  useLazyEsstReportItemsFilteredSchemaRetrieveQuery,
  useEsstReportItemsFormDataRetrieveQuery,
  useLazyEsstReportItemsFormDataRetrieveQuery,
  useEsstReportItemsSummaryRetrieveQuery,
  useLazyEsstReportItemsSummaryRetrieveQuery,
  useEsstReportsListQuery,
  useLazyEsstReportsListQuery,
  useEsstReportsCreateMutation,
  useEsstReportsRetrieveQuery,
  useLazyEsstReportsRetrieveQuery,
  useEsstReportsUpdateMutation,
  useEsstReportsPartialUpdateMutation,
  useEsstReportsDestroyMutation,
  useEsstReportsCompletePartialUpdateMutation,
  useEsstReportsExportRetrieveQuery,
  useLazyEsstReportsExportRetrieveQuery,
  useEsstReportsLinkEsstCreateMutation,
  useEsstReportsNextAuditDateRetrieveQuery,
  useLazyEsstReportsNextAuditDateRetrieveQuery,
} = injectedRtkApi;
