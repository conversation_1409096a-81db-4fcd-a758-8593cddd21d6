#!/usr/bin/env bash
set -e

SERVICE_NAME=$1

if [ -z "$SERVICE_NAME" ]; then
  echo "Usage: $0 <service_name>"
  exit 1
fi

echo "Creating Django Ninja microservice: $SERVICE_NAME"

mkdir -p "$SERVICE_NAME"
pushd "$SERVICE_NAME"

uv init --name "$SERVICE_NAME" --bare

uv add django django-ninja

uv run django-admin startproject "$SERVICE_NAME" .

uv run python manage.py startapp api

sed -i '' "/INSTALLED_APPS = \[/a\\
    'api',
" $SERVICE_NAME/settings.py

cat <<EOF > api/views.py
from ninja import NinjaAPI
from django.http import JsonResponse

api = NinjaAPI()

@api.get("/ping")
def ping(request):
    return {"message": "pong"}
EOF

sed -i '' "/from django.urls import path/i\\
from api.views import api
" $SERVICE_NAME/urls.py

sed -i '' "/urlpatterns = \[/a\\
    path(\"api/\", api.urls),
" $SERVICE_NAME/urls.py

echo "$SERVICE_NAME created."

popd 
echo "Creating root level compose.$SERVICE_NAME.yml"

cat <<EOF>> compose.$SERVICE_NAME.yml
services:
  backend_$SERVICE_NAME:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        DJANGO_APP: $SERVICE_NAME
    container_name: $SERVICE_NAME_api
    depends_on:
      db:
        condition: service_healthy
    environment:
      DATABASE_USER: crop
      DATABASE_PASSWORD: crop
      DATABASE_PORT: 5432
      DATABASE_HOST: db
      DJANGO_SECRET: django-insecure-@#)kh9*8zl&&(fhjlbsn2_yh3zkcas+mx983ged3qi&&5u&w=d
    networks:
      - crop_network
    ports:
      - "8001:8000"
    volumes:
      - ./crop/$SERVICE_NAME:/app/$SERVICE_NAME
      - ./crop/common:/app/common
      - ./crop/manage.py:/app/manage.py
EOF
echo "Created root level compose.$SERVICE_NAME.yml File."

echo "Updating compose.apps.yml"
if grep -q "^include:" compose.apps.yml; then
  echo "  - compose.$SERVICE_NAME.yml" >> compose.apps.yml
else
  echo "include:" >> compose.apps.yml
  echo "  - compose.$SERVICE_NAME.yml" >> compose.apps.yml
fi
echo "Done updating compose.apps.yml"
