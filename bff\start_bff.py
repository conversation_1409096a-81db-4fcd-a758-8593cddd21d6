#!/usr/bin/env python3
"""
BFF Startup Script
This script helps start the BFF server with proper configuration
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if required packages are installed"""
    try:
        import django
        import ninja
        import corsheaders
        import dotenv
        import requests
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing required package: {e}")
        print("Please install requirements: pip install -r requirements.txt")
        return False

def check_environment():
    """Check environment configuration"""
    print("🔍 Checking environment configuration...")
    
    # Load .env file if it exists
    env_file = Path('.env')
    if env_file.exists():
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Loaded .env file")
    else:
        print("⚠️  No .env file found (using defaults)")
    
    # Check critical settings
    critical_settings = [
        'SECRET_KEY',
        'BACKEND_API_URL',
    ]
    
    missing_settings = []
    for setting in critical_settings:
        if not os.getenv(setting):
            missing_settings.append(setting)
    
    if missing_settings:
        print(f"❌ Missing critical environment variables: {', '.join(missing_settings)}")
        return False
    
    print("✅ Environment configuration looks good")
    return True

def run_migrations():
    """Run Django migrations"""
    print("🔄 Running Django migrations...")
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'migrate'
        ], check=True, capture_output=True, text=True)
        print("✅ Migrations completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Migration failed: {e.stderr}")
        return False

def start_server(host='localhost', port=8080, debug=False):
    """Start the Django development server"""
    print(f"🚀 Starting BFF server on {host}:{port}")
    
    # Set debug mode
    if debug:
        os.environ['DEBUG'] = 'True'
    
    try:
        subprocess.run([
            sys.executable, 'manage.py', 'runserver', f'{host}:{port}'
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 BFF server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")

def create_sample_env():
    """Create a sample .env file"""
    sample_env = """# BFF Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Backend Configuration
BACKEND_API_URL=http://localhost:8000/api
DJANGO_NINJA_BASE_URL=http://localhost:8000
DJANGO_NINJA_API_PATH=/api
DJANGO_NINJA_TIMEOUT=30

# PingFederate Configuration (update with your values)
PINGFEDERATE_BASE_URL=https://your-pingfederate-url
PINGFEDERATE_CLIENT_ID=your-client-id
PINGFEDERATE_CLIENT_SECRET=your-client-secret
PINGFEDERATE_REDIRECT_URI=http://localhost:8080/api/auth/callback

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate Limiting
NINJA_RATE_LIMIT_USER=100/hour
NINJA_RATE_LIMIT_ANON=20/hour
"""
    
    env_file = Path('.env')
    if not env_file.exists():
        with open(env_file, 'w') as f:
            f.write(sample_env)
        print("✅ Created sample .env file")
        print("⚠️  Please update the .env file with your actual configuration")
        return True
    else:
        print("⚠️  .env file already exists")
        return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='BFF Startup Script')
    parser.add_argument('--host', default='localhost', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8080, help='Port to bind to')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--create-env', action='store_true', help='Create sample .env file')
    parser.add_argument('--skip-checks', action='store_true', help='Skip environment checks')
    parser.add_argument('--skip-migrations', action='store_true', help='Skip migrations')
    
    args = parser.parse_args()
    
    print("🏗️  BFF (Backend for Frontend) Startup")
    print("=" * 50)
    
    # Create sample .env if requested
    if args.create_env:
        create_sample_env()
        return
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check environment
    if not args.skip_checks and not check_environment():
        print("\n💡 Tip: Use --create-env to create a sample .env file")
        sys.exit(1)
    
    # Run migrations
    if not args.skip_migrations and not run_migrations():
        sys.exit(1)
    
    # Start server
    print("\n" + "=" * 50)
    start_server(args.host, args.port, args.debug)

if __name__ == "__main__":
    main()
