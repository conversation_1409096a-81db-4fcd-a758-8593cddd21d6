#!/usr/bin/env python3
"""
Test script for BFF Django Ninja integration
This script tests various endpoints to ensure the BFF is properly forwarding requests
"""

import requests
import json
import sys
from typing import Dict, Any

# Configuration
BFF_BASE_URL = "http://localhost:8080"
DJANGO_NINJA_BASE_URL = "http://localhost:8000"

class BFFTester:
    def __init__(self, bff_url: str = BFF_BASE_URL):
        self.bff_url = bff_url.rstrip('/')
        self.session = requests.Session()
        self.test_results = []
    
    def test_endpoint(self, endpoint: str, method: str = 'GET', expected_status: int = 200, 
                     data: Dict[str, Any] = None, description: str = None) -> bool:
        """Test a single endpoint"""
        url = f"{self.bff_url}{endpoint}"
        test_name = description or f"{method} {endpoint}"
        
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=10)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=10)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data, timeout=10)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            success = response.status_code == expected_status
            result = {
                'test': test_name,
                'url': url,
                'method': method,
                'expected_status': expected_status,
                'actual_status': response.status_code,
                'success': success,
                'response_size': len(response.content),
                'error': None if success else response.text[:200]
            }
            
            self.test_results.append(result)
            
            status_icon = "✅" if success else "❌"
            print(f"{status_icon} {test_name}: {response.status_code} (expected {expected_status})")
            
            if not success:
                print(f"   Error: {response.text[:100]}...")
            
            return success
            
        except Exception as e:
            result = {
                'test': test_name,
                'url': url,
                'method': method,
                'expected_status': expected_status,
                'actual_status': None,
                'success': False,
                'response_size': 0,
                'error': str(e)
            }
            
            self.test_results.append(result)
            print(f"❌ {test_name}: Exception - {str(e)}")
            return False
    
    def run_health_checks(self):
        """Test all health check endpoints"""
        print("\n🏥 Testing Health Check Endpoints...")
        
        # Core API health check
        self.test_endpoint("/api/health", description="Core API Health Check")
        
        # Module health checks
        self.test_endpoint("/riskradar/health", description="RiskRadar Health Check")
        self.test_endpoint("/green/health", description="Green Module Health Check")
        self.test_endpoint("/finance/health", description="Finance Module Health Check")
        self.test_endpoint("/ccra/health", description="CCRA Module Health Check")
        self.test_endpoint("/propertyflow/health", description="PropertyFlow Module Health Check")
    
    def run_core_api_tests(self):
        """Test core API endpoints"""
        print("\n🏠 Testing Core API Endpoints...")
        
        # Test addresses endpoint
        self.test_endpoint("/api/addresses/", description="List Addresses")
        
        # Test sales endpoint
        self.test_endpoint("/api/sales/", description="List Sales")
        
        # Test valuations endpoint
        self.test_endpoint("/api/valuations/", description="List Valuations")
        
        # Test customers endpoint
        self.test_endpoint("/api/customers/", description="List Customers")
    
    def run_riskradar_tests(self):
        """Test RiskRadar module endpoints"""
        print("\n⚠️ Testing RiskRadar Endpoints...")
        
        # Test RiskRadar endpoints
        self.test_endpoint("/riskradar/locations/", description="RiskRadar Locations")
        self.test_endpoint("/riskradar/perils/", description="RiskRadar Perils")
        self.test_endpoint("/riskradar/loss-models/", description="RiskRadar Loss Models")
        self.test_endpoint("/riskradar/risk-groups/", description="RiskRadar Risk Groups")
        self.test_endpoint("/riskradar/exposures/", description="RiskRadar Exposures")
        self.test_endpoint("/riskradar/analytics/", description="RiskRadar Analytics")
    
    def run_generic_proxy_tests(self):
        """Test generic proxy endpoint"""
        print("\n🔄 Testing Generic Proxy Endpoint...")
        
        # Test generic proxy with various endpoints
        self.test_endpoint("/ninja/health", description="Generic Proxy - Health")
        self.test_endpoint("/ninja/addresses/", description="Generic Proxy - Addresses")
        self.test_endpoint("/ninja/riskradar/health", description="Generic Proxy - RiskRadar Health")
    
    def run_authentication_tests(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints...")
        
        # Test auth endpoints (these might return different status codes)
        self.test_endpoint("/api/auth/authorize", description="OAuth Authorize", expected_status=200)
        
        # Test protected endpoint without auth (should fail)
        self.test_endpoint("/api/aggregated-data", description="Protected Endpoint (no auth)", expected_status=401)
    
    def run_rate_limiting_tests(self):
        """Test rate limiting (basic test)"""
        print("\n🚦 Testing Rate Limiting...")
        
        # Make multiple requests quickly to test rate limiting
        success_count = 0
        for i in range(5):
            if self.test_endpoint("/api/health", description=f"Rate Limit Test {i+1}"):
                success_count += 1
        
        print(f"   Rate limiting test: {success_count}/5 requests succeeded")
    
    def run_all_tests(self):
        """Run all tests"""
        print(f"🚀 Starting BFF Integration Tests")
        print(f"BFF URL: {self.bff_url}")
        print("=" * 60)
        
        # Run test suites
        self.run_health_checks()
        self.run_core_api_tests()
        self.run_riskradar_tests()
        self.run_generic_proxy_tests()
        self.run_authentication_tests()
        self.run_rate_limiting_tests()
        
        # Print summary
        self.print_summary()
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['test']}: {result['error'] or 'Status ' + str(result['actual_status'])}")
        
        print("\n" + "=" * 60)
        
        # Return exit code
        return 0 if failed_tests == 0 else 1

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test BFF Django Ninja Integration')
    parser.add_argument('--bff-url', default=BFF_BASE_URL, help='BFF base URL')
    parser.add_argument('--suite', choices=['health', 'core', 'riskradar', 'proxy', 'auth', 'rate', 'all'], 
                       default='all', help='Test suite to run')
    
    args = parser.parse_args()
    
    tester = BFFTester(args.bff_url)
    
    if args.suite == 'health':
        tester.run_health_checks()
    elif args.suite == 'core':
        tester.run_core_api_tests()
    elif args.suite == 'riskradar':
        tester.run_riskradar_tests()
    elif args.suite == 'proxy':
        tester.run_generic_proxy_tests()
    elif args.suite == 'auth':
        tester.run_authentication_tests()
    elif args.suite == 'rate':
        tester.run_rate_limiting_tests()
    else:
        tester.run_all_tests()
    
    exit_code = tester.print_summary()
    sys.exit(exit_code)

if __name__ == "__main__":
    main()
