import type ReactPDF from '@react-pdf/renderer'
import { View } from '@react-pdf/renderer'
import type { ReactNode } from 'react'
import { PdfTable } from '@components/pdf'

const Row = ({ children }: ReactPDF.ViewProps & { children: ReactNode }) => (
  <View
    wrap={false}
    style={{
      display: 'flex',
      flexDirection: 'row',
      alignItems: 'stretch',
    }}
  >
    {children}
  </View>
)

const Column = ({
  children,
  flex = 1,
}: ReactPDF.ViewProps & { children: ReactNode; flex?: number }) => (
  <View style={{ display: 'flex', flex }}>{children}</View>
)

const StockExclusionTable = () => (
  <View
    style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'flex-start',
    }}
  >
    <Row>
      <Column>
        <PdfTable
          isNested
          stretch
          striped
          columns={[{ key: 'waterways' }]}
          rows={[
            {
              waterways:
                'Stock must be excluded and kept at least 3 meters from the edge of a lake or a river with a bed greater than 1 meter wide at any point on the parcel of land – includes intermittent streams but excludes drains',
            },
          ]}
        />
      </Column>
      <Column flex={3}>
        <PdfTable
          isNested
          stretch
          striped
          columns={[{ key: 'stockClass' }, { key: 'terrain' }, { key: 'date' }]}
          rows={[
            {
              stockClass: 'Dairy cattle',
              terrain: 'Any newly developed farmland',
              date: '3 September 2020',
            },
            {
              stockClass: 'Dairy cattle, pigs',
              terrain: 'Any terrain',
              date: '1 July 2023',
            },
            {
              stockClass: 'Dairy support cattle',
              terrain: 'Any terrain',
              date: '1 July 2025',
            },
            {
              stockClass:
                'Beef cattle and deer intensively grazing (break-fed on annual forage crops or irrigated pasture)',
              terrain: 'Any terrain',
              date: '1 July 2023',
            },
            {
              stockClass: 'Beef cattle and deer',
              terrain: 'Low slope land',
              date: '1 July 2025',
            },
          ]}
        />
      </Column>
    </Row>
    <Row>
      <Column>
        <PdfTable
          hideHeaderRow
          indexOffset={5}
          isNested
          stretch
          striped
          columns={[{ key: 'waterways' }, { key: 'stockClass' }]}
          rows={[
            {
              waterways:
                'Stock must be excluded and kept at least 3 meters from the edge of a lake or a river with a bed greater than 1 meter wide at any point on the parcel of land – includes intermittent streams but excludes drains',
              stockClass: 'All cattle, deer and pigs',
            },
          ]}
        />
      </Column>
      <Column>
        <PdfTable
          hideHeaderRow
          indexOffset={5}
          isNested
          stretch
          striped
          columns={[{ key: 'terrain' }, { key: 'date' }]}
          rows={[
            {
              terrain: 'Any newly developed farm',
              date: '3 September 2020',
            },
            {
              terrain:
                'Any natural wetland identified in the regional or district plan as of 3 September 2020 ',
              date: '1 July 2023',
            },
            {
              terrain:
                'Any wetland greater than 0.05 hectares on low slope land or with a population of threatened species',
              date: '1 July 2025',
            },
          ]}
        />
      </Column>
    </Row>
  </View>
)

export default StockExclusionTable
