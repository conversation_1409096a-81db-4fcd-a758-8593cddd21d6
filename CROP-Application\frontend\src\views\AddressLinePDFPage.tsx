import React from 'react'
import AddressLinePDF from '@components/addresses/AddressLinePDF'
import { LoadingContainer } from '@components/layout/LoadingContainer'
import PDFView from '@components/pdf/PDFView'
import useQuery from '@hooks/useQuery'
import sdk from '@store/services/sdk'

const AddressLinePDFPage = () => {
  const query = useQuery()
  const ids = query.get('ids')?.split(',')

  const { data: addresses } = sdk.useAddressesSummaryListQuery(
    { ids },
    { skip: !ids }
  )

  if (!addresses) return <LoadingContainer loading />

  return (
    <PDFView>
      <AddressLinePDF addresses={addresses} />
    </PDFView>
  )
}

export default AddressLinePDFPage
