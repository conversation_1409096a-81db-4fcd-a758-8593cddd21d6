import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import { Form, type FormItemProps, Input, Switch, type FormProps } from 'antd'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import type { NamePath } from 'rc-field-form/es/interface'
import sdk from '@store/services/finance'
import {
  type AgriUplift,
  type AgriUpliftEmissionsSource,
  type PatchedAgriUplift,
  useFinanceAgriUpliftRetrieveQuery,
} from '@store/services/finance/codegen'
import useFormFinish from '@hooks/useFormFinish'
import { ENV_DEVELOPMENT, ENV_UAT } from '@util/const'
import { access, equalsProperty, insertIf, skipArgObject } from '@util/helpers'
import { sortDateTextTime } from '@util/sort'
import { ACRONYM, HOST_PRODUCT_IDENTIFIER, INVALID_SOURCE_NAME } from '../const'
import ApprovedMarginPercent from './ApprovedMarginPercentInput'
import CustomerHeading from './CustomerHeading'
import EmissionsSources from './EmissionsSources'
import Facilities from './Facilities'
import Submit from './Submit'
import Divider from './Divider'
import Heading from './Heading'
import {
  type AgriUpliftFormValues,
  emissionsSourcesArrayToMap,
  getInvalidEmissionsSourceIndexes,
  prepareFormValues,
} from './helpers'
import { emissionsMetricsArrayToMap } from '@components/emissions-metrics/util'

type AgriUpliftSave =
  | ((data: AgriUplift) => Promise<AgriUplift>)
  | ((data: PatchedAgriUplift) => Promise<AgriUplift>)

type AgriUpliftFormProps = FormProps<AgriUplift> & {
  loading?: boolean
  save?: AgriUpliftSave
}

export const getPathArray = (path: NamePath) =>
  Array.isArray(path) ? path : [path]

export const concatPaths = (...paths: NamePath[]) => paths.flatMap(getPathArray)

function AgriUpliftForm({
  initialValues = {},
  loading,
  save,
  ...props
}: AgriUpliftFormProps) {
  const navigate = useNavigate()
  const [form] = Form.useForm<AgriUplift>()

  const [emissionSourceErrorIndexes, setEmissionsSourceErrorIndexes] = useState<
    number[]
  >([])

  const initialEmissionsSourceMap: Record<number, AgriUpliftEmissionsSource> =
    useMemo(
      () => emissionsSourcesArrayToMap(initialValues.emissionsSources),
      [initialValues]
    )

  const isDraftOrNew = !initialValues.pk || initialValues.status === 'draft'

  const { onFinish, onFinishFailed } = useFormFinish(form, save)

  const doFinish = useCallback(
    async (formValues: AgriUplift, status: AgriUplift['status']) => {
      const updatedValues = prepareFormValues(
        initialEmissionsSourceMap,
        // Need to clean this up
        formValues as unknown as AgriUpliftFormValues,
        status
      )
      const res = await onFinish(updatedValues)
      if (!res?.pk) return
      navigate(`/finance/agri-uplift/${res.pk}`)
    },
    [navigate, onFinish, initialEmissionsSourceMap]
  )

  const handleFinish = useCallback(
    (formValues: AgriUplift) => {
      doFinish(formValues, 'completed')
    },
    [doFinish]
  )

  const saveDraft = useCallback(() => {
    const formValues = form.getFieldsValue()
    doFinish(formValues, 'draft')
  }, [form, doFinish])

  // biome-ignore lint/correctness/useExhaustiveDependencies:
  useEffect(() => {
    // Reset for the facilities to update,
    // would like to provide a draft with facilities from the server instead
    form.resetFields()
    form.setFieldValue('attestation', initialValues.status === 'completed')
  }, [form, initialValues.customerId])

  const invalidSource: Omit<AgriUpliftEmissionsSource, 'key'> = {
    pk: Date.now(),
    recordId: Date.now(),
    name: 'Invalid',
    facilities: [],
  }

  const initialEmissionsSources: AgriUpliftEmissionsSource[] =
    initialValues.emissionsSources || []

  const computedInitialValues = {
    ...initialValues,
    emissionsSources: [
      ...insertIf(
        !initialEmissionsSources.some(equalsProperty('name', 'Invalid')),
        invalidSource
      ),
      ...initialEmissionsSources,
    ],
  }

  return (
    <Form
      key={initialValues?.updatedDatetime || initialValues.customerId}
      {...props}
      form={form}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
      initialValues={computedInitialValues}
      onValuesChange={(changedValues, allValues) => {
        if (ENV_DEVELOPMENT) {
          console.info(`${ACRONYM} Values:`, changedValues, allValues)
        }
        // Require attestation confirm if values change
        if (!('attestation' in changedValues)) {
          form.resetFields(['attestation'])
        }
      }}
      onFieldsChange={(changedFields) => {
        setEmissionsSourceErrorIndexes(
          getInvalidEmissionsSourceIndexes(changedFields)
        )
      }}
      disabled={loading || !isDraftOrNew}
      layout="vertical"
      className="unstyle"
    >
      <Heading>Customer</Heading>
      <CustomerHeading pk={initialValues.tradingGroupId} />
      <Divider />
      <FormItem name="pk" hidden>
        <Input />
      </FormItem>
      <FormItem name="tradingGroupId" hidden>
        <Input />
      </FormItem>
      <Heading>Customer Emissions Data</Heading>
      <FormItem name="emissionsSources">
        <EmissionsSources
          tradingGroupId={initialValues.tradingGroupId}
          errorIndexes={emissionSourceErrorIndexes}
        />
      </FormItem>
      <Divider />
      <Heading>Pricing</Heading>
      <ApprovedMarginPercent />
      <Divider />
      <Heading>{HOST_PRODUCT_IDENTIFIER} Facilities</Heading>
      <FormItem
        name="emissionsSources"
        valuePropName="emissionsSources"
        rules={[
          {
            warningOnly: true,
            message:
              'Warning: A source has added or removed a facility that will change its expiration date.',
            validator: (_, value: AgriUpliftEmissionsSource[]) => {
              const significantlyChanged = value.some((source) => {
                if (source.name === INVALID_SOURCE_NAME) return false
                const matchingInitialSource =
                  initialEmissionsSourceMap[source.pk]
                if (
                  !matchingInitialSource?.facilities?.length ||
                  !source.facilities?.length
                )
                  return false
                const initialDrawdownDates = matchingInitialSource?.facilities
                  ?.map(access('drawdownDate'))
                  .sort(sortDateTextTime)
                const newDrawdownDates = source.facilities
                  ?.map(access('drawdownDate'))
                  .sort(sortDateTextTime)
                return initialDrawdownDates?.[0] !== newDrawdownDates?.[0]
              })
              if (significantlyChanged) return Promise.reject()
              return Promise.resolve()
            },
          },
        ]}
      >
        <Facilities
          tradingGroupId={initialValues.tradingGroupId}
          recordId={initialValues?.pk}
        />
      </FormItem>
      <Divider />
      <Heading>Attestation</Heading>
      <Form.Item
        name="attestation"
        rules={[
          {
            required: true,
            message: 'attestation is required',
            validator: (_, value) =>
              !value ? Promise.reject() : Promise.resolve(),
          },
        ]}
        label="Do you attest that the above is correct?"
        valuePropName="checked"
        className="unstyle"
      >
        <Switch
          checkedChildren={<CheckOutlined />}
          unCheckedChildren={<CloseOutlined />}
        />
      </Form.Item>
      {ENV_UAT && (
        <details>
          <summary style={{ background: 'gold', padding: 'var(--space-1)' }}>
            Debug
          </summary>
          <pre style={{ fontSize: '1em' }}>
            {JSON.stringify(initialValues, null, 2)}
          </pre>
        </details>
      )}
      <Submit
        pk={initialValues.pk}
        isDraftOrNew={isDraftOrNew}
        loading={loading}
        saveDraft={saveDraft}
      />
    </Form>
  )
}

export function AgriUpliftCreateForm({
  tradingGroupId,
  ...props
}: AgriUpliftFormProps & { tradingGroupId: string }) {
  const [save, { isLoading }] = sdk.useAgriUpliftMultipartCreateMutation()

  return (
    <AgriUpliftForm
      initialValues={{ tradingGroupId }}
      save={(data: AgriUplift) => save(data).unwrap()}
      loading={isLoading}
      {...props}
    />
  )
}

export function AgriUpliftUpdateForm({
  pk,
  ...props
}: AgriUpliftFormProps & { pk: number }) {
  const {
    data,
    isLoading: retrieving,
    fulfilledTimeStamp,
  } = useFinanceAgriUpliftRetrieveQuery(skipArgObject({ pk }))

  const [save, { isLoading: saving }] =
    sdk.useAgriUpliftMultipartPartialUpdateMutation()

  // Temporary fix for items being reordered
  const emissionsSources = data?.emissionsSources?.map((source) => {
    if (!source?.emissions?.reportType) return source
    // making a map of these values so the reportType definition can dictate their order
    const metrics = emissionsMetricsArrayToMap(source.emissions.metrics)
    const updatedSource = {
      ...source,
      emissions: {
        ...source.emissions,
        metrics,
      },
    }
    return updatedSource
  })

  return (
    <AgriUpliftForm
      key={fulfilledTimeStamp}
      initialValues={{
        ...data,
        emissionsSources,
      }}
      save={(data: AgriUplift) => save(data).unwrap()}
      loading={saving || retrieving}
      {...props}
    />
  )
}

function FormItem({
  name,
  ...props
}: Omit<FormItemProps, 'name'> & { name: keyof AgriUplift }) {
  return <Form.Item name={name} {...props} />
}

export default AgriUpliftForm
