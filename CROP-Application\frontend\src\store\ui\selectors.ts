import { createSelector } from '@reduxjs/toolkit'
import BigNumber from 'bignumber.js'
import { get } from 'lodash'
import moment from 'moment'
import { ZERO } from '@types'
import { DEFAULT_STYLES } from '@util/layers'
import { RootState } from '..'
import {
  ComparableSaleAdjustment,
  ComparableSaleAssetMetricAdjustment,
  deserializeComparableSaleAdjustment,
  deserializeComparableSaleAssetMetricAdjustment,
} from '../../models/assets/ComparableSaleAdjustment'
import { ExplorerLayer, LayerContext, LayerStyles } from '../features/map/types'
import {
  SelectedIDType,
  getSelectedItemsByType,
  isItemOfTypeSelected,
} from './helpers'
import { DEFAULT_SALES_AND_LISTING_STATE, initialUIState } from './slice'
import { LayerState, UIState } from './types'

export const getUIState = (state: RootState) => {
  return state.ui
}

export const getSelectedIdsByType = (type: SelectedIDType) =>
  createSelector(getUIState, (state: UIState) => [
    ...getSelectedItemsByType(state, type),
  ])

export const getTradingGroupPageTab = (state: RootState) => {
  return state.ui.temporal.tradingGroup.tab
}

export const getSalesAndListingState = createSelector(
  getUIState,
  (state: UIState) => {
    return state.salesAndListings || DEFAULT_SALES_AND_LISTING_STATE
  }
)

export const getSavedSettings = (state: RootState) => {
  return state.ui.saved
}

export const getMapContainerRect = (state: RootState) => {
  return state.ui.temporal.map.mapContainerRect
}

export const getMapState = (state: RootState) => {
  return state.ui.saved.map
}

export const getViewport = (state: RootState) => {
  return (
    state.ui.saved.map?.viewport || {
      zoom: undefined,
      bounds: undefined,
      center: undefined,
    }
  )
}

export const getWidgetState = (state: RootState, key: string) => {
  return state.ui.saved.map?.widgetState[key]
}

export const getFilterState = (state: RootState, pageName: string) => {
  return get(state, `ui.saved.filters.${pageName}`, {}) as Record<string, any>
}

export const getLayoutState = (state: RootState, pageName: string) => {
  if (!state.ui.saved.layouts || !state.ui.saved.layouts[pageName]) {
    return {}
  }
  return state.ui.saved.layouts[pageName]
}

export const getLatestPage = (state: RootState) => {
  return get(state, 'ui.saved.layouts.superNav.latestPage', '') as string
}

export const getMapBaseLayer = (state: RootState) => {
  return state.ui.saved.map?.baseLayer ?? 'ESRI Satellite'
}

export const getExplorerFilters = (state: RootState) => {
  return state.ui.saved.map?.filters ?? initialUIState.saved.map.filters
}

export const getExplorersFilterDirty = (state: RootState) => {
  if (!state.ui.saved.map?.filters) {
    return false
  }

  const currentState = {
    saleStartDate: moment(state.ui.saved.map?.filters.saleStartDate),
    saleEndDate: !state.ui.saved.map?.filters.saleEndDate
      ? state.ui.saved.map?.filters.saleEndDate
      : moment(state.ui.saved.map?.filters.saleEndDate),
    keyword: state.ui.saved.map?.filters.keyword ?? '',
  }

  return (
    JSON.stringify(currentState) !==
    JSON.stringify(initialUIState.saved.map.filters)
  )
}

export const getInfoWidgetState = (state: RootState) => {
  return state.ui.temporal.map.infoState
}

export const getSelectedAddressColor = (
  state: RootState,
  addressId: string
) => {
  return state.ui.saved.map?.addressColors[addressId]
}

export const getSelectedSaleColor = (state: RootState, saleId: string) => {
  return state.ui.saved.map?.saleColors[saleId]
}

export const getSelectedLayerStyles = (
  state: RootState,
  context: LayerContext,
  id: number
): LayerStyles => {
  return Object.entries(
    get(state, `ui.saved.map.layerStyles.${context}.${id}`, {}) as LayerStyles
  )
    .filter(([, value]) => !!value)
    .reduce((obj, [k, v]) => ({ ...obj, [k]: v }), DEFAULT_STYLES)
}

export const getSelectedTab = (state: RootState) => {
  return state.ui.saved.map?.selected.tab
}

export const getStraightLineToolState = (state: RootState) => {
  return state.ui.saved.map?.toolState.straightLineMeasure
}

export const getStraightLineDraftState = (state: RootState) => {
  return state.ui.saved.map?.toolState.draft
}

export const getLayerState = (state: RootState, key: string) => {
  return state.ui.saved.map?.layerState[key]
}

export const getSelectedAddressIds = (state: RootState) => {
  return state.ui.saved.map?.selected.addressIds || []
}

export const getSelectedSaleIds = (state: RootState) => {
  return state.ui.saved.map?.selected.saleIds || []
}

export const getSelectedListingIds = (state: RootState) => {
  return state.ui.saved.map?.selected.listingIds || []
}

export const getSelectedConsentIds = (state: RootState) => {
  return state.ui.saved.map?.selected.consentIds || []
}

export const getSelectedValocityListingIds = (state: RootState) => {
  return state.ui.saved.map?.selected.valocityListingIds || []
}

export const getSelectedValocitySaleIds = (state: RootState) => {
  return state.ui.saved.map?.selected.valocitySaleIds || []
}

export const getUser = (state: RootState) => {
  return state.ui.user
}

export const isAdmin = (state: RootState) => {
  return ['client:riskradar:admin:view'].some((entitlement) =>
    state.ui.user?.entitlements.includes(entitlement)
  )
}

export const isRiskRadarUser = (state: RootState) => {
  return ['client:riskradar:all:view'].some((entitlement) =>
    state.ui.user?.entitlements.includes(entitlement)
  )
}

export const isAuthenticated = (state: RootState) => {
  return !!state.ui.user?.username
}

export const isAddressSelected = (state: RootState, id: string) => {
  return isItemOfTypeSelected(getUIState(state), 'addressIds', String(id))
}

export const isValuer = (state: RootState) => {
  return [
    'client:propertyflow:ria:approve',
    'client:propertyflow:rvr:approve',
    'client:propertyflow:prts:approve',
  ].some((entitlement) => state.ui.user?.entitlements.includes(entitlement))
}

export const getSalesAndListingsCurrentTab = createSelector(
  getUIState,
  (state: UIState) => {
    return state.salesAndListings.currentTab
  }
)

export const getSaleEditState = createSelector(getUIState, (state: UIState) => {
  return state.salesAndListings.sales.saleEdit
})

export const getSalesAndListingsLayerVisibility = createSelector(
  getUIState,
  (state: UIState) => {
    return state.salesAndListings.layerVisibility
  }
)

export const getSelectedSale = (state: RootState) => {
  return state.ui.salesAndListings.selectedSale
}

export const getSelectedSaleRowIndex = (state: RootState) => {
  return state.ui.salesAndListings.selectedSaleRowIndex
}

export const getSelectedComparableSaleId = (state: RootState) => {
  return state.ui.salesAndListings.selectedComparableSaleId
}

export const getComparableSaleAdjustments = (state: RootState) => {
  return state.ui.salesAndListings.comparableSaleAdjustments.map(
    deserializeComparableSaleAdjustment
  )
}

export const getComparableSaleAssetMetricAdjustments = (state: RootState) => {
  return state.ui.salesAndListings.comparableSaleAssetMetricAdjustments.map(
    deserializeComparableSaleAssetMetricAdjustment
  )
}

const percentToComparability = (percent: BigNumber): string => {
  if (percent.gt(0)) {
    return 'Superior'
  } else if (percent.lt(0)) {
    return 'Inferior'
  } else {
    return 'Comparable'
  }
}

export const computeSaleAdjustmentSum = (
  adjustments: ComparableSaleAdjustment[]
) => {
  const totalAdjustmentPercent = adjustments.reduce(
    (accum: BigNumber, adjustment) => {
      let percent: BigNumber = adjustment.adjustmentPercent
      if (adjustment.comparability === 'Inferior') {
        percent = percent.multipliedBy(-1)
      }
      return !percent.isNaN() ? accum.plus(percent) : accum
    },
    ZERO
  )

  const lwbAdjustmentPercent = adjustments.reduce(
    (accum: BigNumber, adjustment) => {
      if (adjustment.adjustmentType === 'Buildings') {
        return accum
      }
      let percent = adjustment.adjustmentPercent
      if (adjustment.comparability === 'Inferior') {
        percent = percent.multipliedBy(-1)
      }
      return !percent.isNaN() ? accum.plus(percent) : accum
    },
    ZERO
  )

  const productivityAdjustment = adjustments.find(
    (adjustment) => adjustment.adjustmentType === 'Productivity'
  )
  const productivityAdjustmentPercent =
    (productivityAdjustment?.comparability === 'Inferior'
      ? productivityAdjustment?.adjustmentPercent?.multipliedBy(-1)
      : productivityAdjustment?.adjustmentPercent) ?? ZERO

  return {
    overallComparability: percentToComparability(totalAdjustmentPercent),
    lwbComparability: percentToComparability(lwbAdjustmentPercent),
    productivityAdjustmentPercent, // signed productivityAdjustmentPercent to allow individual land class adjustments to use lwbAdjustmentPercent without the overall productivity adjustment
    totalAdjustmentPercent,
    lwbAdjustmentPercent,
  }
}

export const getComparableSaleAdjustmentSum = createSelector(
  getComparableSaleAdjustments,
  computeSaleAdjustmentSum
)

export const getComparableSaleAssetMetricAdjustment = createSelector(
  (_state: RootState, landAssetMetricId: number) => landAssetMetricId,
  (state: RootState) => getComparableSaleAssetMetricAdjustments(state),
  (
    landAssetMetricId: number,
    assetMetricAdjustments: ComparableSaleAssetMetricAdjustment[]
  ) => {
    return assetMetricAdjustments.find(
      (adjustment) => adjustment.landAssetMetric === landAssetMetricId
    )
  }
)

export const getEnabledLayers = createSelector(getUIState, (state: UIState) => {
  const layerState = get(state, 'saved.map.layerState', {}) as LayerState
  const selected = Object.entries(layerState)
    .filter(([, value]) => !!value)
    .map(([key]) => key as ExplorerLayer)
  return selected
})

export const getLayerOrder = createSelector(getUIState, (state) => {
  return get(state, 'saved.layouts.map.order', []) as ExplorerLayer[]
})

export const getGreenDrawerVisible = (state: RootState) => {
  return state.ui.temporal.green?.drawer?.visible ?? false
}

export const getGreenDrawerState = (state: RootState) => {
  return state.ui.temporal.green?.drawer
}

export const getValocitySaleSearchFilters = (
  state: RootState,
  pageName: string
) => {
  return state.ui.saved.valocitySaleSearchFilters[pageName] ?? {}
}

export const getSaleSearchFilters = (state: RootState, pageName: string) => {
  return state.ui.saved.saleSearchFilters[pageName] ?? {}
}
