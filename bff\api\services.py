import requests
import json
from django.conf import settings
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class BackendServiceProxy:
    """Proxy class to forward requests to Django Ninja backend services"""
    
    @staticmethod
    def forward_request(
        endpoint: str, 
        method: str = 'GET',
        headers: Optional[Dict[str, str]] = None,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        timeout: int = None
    ) -> Dict[str, Any]:
        """
        Forward request to Django Ninja backend service
        
        Args:
            endpoint: The API endpoint path (e.g., 'health', 'addresses/', 'riskradar/locations/')
            method: HTTP method (GET, POST, PUT, DELETE, etc.)
            headers: Optional headers to include in the request
            data: Optional data for POST/PUT requests
            params: Optional query parameters
            timeout: Request timeout in seconds
            
        Returns:
            Dict containing 'status_code', 'data', and 'headers'
        """
        try:
            # Construct full URL using Django Ninja configuration
            base_url = getattr(settings, 'DJANGO_NINJA_BASE_URL', settings.BACKEND_API_URL).rstrip('/')
            api_path = getattr(settings, 'DJANGO_NINJA_API_PATH', '/api').rstrip('/')

            if not endpoint.startswith('/'):
                endpoint = '/' + endpoint
            url = f"{base_url}{api_path}{endpoint}"
            
            # Prepare headers
            request_headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            }
            if headers:
                request_headers.update(headers)
            
            # Get timeout from settings or use provided value
            if timeout is None:
                timeout = getattr(settings, 'DJANGO_NINJA_TIMEOUT', 30)

            # Prepare request arguments
            request_kwargs = {
                'headers': request_headers,
                'timeout': timeout,
            }
            
            if params:
                request_kwargs['params'] = params
                
            if data and method.upper() in ['POST', 'PUT', 'PATCH']:
                request_kwargs['json'] = data
            
            # Make the request
            logger.info(f"Forwarding {method} request to {url}")
            response = requests.request(method, url, **request_kwargs)
            
            # Parse response
            try:
                response_data = response.json() if response.content else {}
            except json.JSONDecodeError:
                response_data = {'raw_content': response.text}
            
            return {
                'status_code': response.status_code,
                'data': response_data,
                'headers': dict(response.headers)
            }
            
        except requests.exceptions.Timeout:
            logger.error(f"Timeout forwarding request to {endpoint}")
            return {
                'status_code': 504,
                'data': {'error': 'Backend service timeout'},
                'headers': {}
            }
        except requests.exceptions.ConnectionError:
            logger.error(f"Connection error forwarding request to {endpoint}")
            return {
                'status_code': 502,
                'data': {'error': 'Backend service unavailable'},
                'headers': {}
            }
        except Exception as e:
            logger.error(f"Error forwarding request to {endpoint}: {str(e)}")
            return {
                'status_code': 500,
                'data': {'error': f'Internal proxy error: {str(e)}'},
                'headers': {}
            }
    
    @staticmethod
    def forward_get(endpoint: str, headers: Optional[Dict[str, str]] = None, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Convenience method for GET requests"""
        return BackendServiceProxy.forward_request(endpoint, 'GET', headers, params=params)
    
    @staticmethod
    def forward_post(endpoint: str, data: Dict[str, Any], headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Convenience method for POST requests"""
        return BackendServiceProxy.forward_request(endpoint, 'POST', headers, data)
    
    @staticmethod
    def forward_put(endpoint: str, data: Dict[str, Any], headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Convenience method for PUT requests"""
        return BackendServiceProxy.forward_request(endpoint, 'PUT', headers, data)
    
    @staticmethod
    def forward_delete(endpoint: str, headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Convenience method for DELETE requests"""
        return BackendServiceProxy.forward_request(endpoint, 'DELETE', headers)
