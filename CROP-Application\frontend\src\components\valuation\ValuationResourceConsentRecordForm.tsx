import { Button, DatePicker, Form, Input, Select } from 'antd'
import { startCase } from 'lodash'
import type React from 'react'
import { type FormItem, FormItems } from '@components/form'
import useMutationForm from '@hooks/useMutationForm'
import sdk, { type ValuationsResourceConsentRecord } from '@store/services/sdk'
import styles from './ValuationResourceConsentRecordForm.module.scss'
import { useValuation } from './context'

const createRecord = sdk.useValuationsResourceConsentRecordsNestedCreateMutation

const BODY_PATH = 'valuationsResourceConsentRecord'

const body: FormItem<ValuationsResourceConsentRecord>[] = [
  {
    name: 'number',
    label: 'Consent No.',
  },
  {
    name: 'purpose',
  },
  {
    name: 'status',
    initialValue: 'unknown',
    children: (
      <Select
        options={['unknown', 'ceased', 'current', 'pending'].map((value) => ({
          value,
          label: startCase(value),
        }))}
      />
    ),
  },
  {
    name: 'holder',
  },
]

const dates = [
  {
    name: 'commencement_date' as const,
    children: <DatePicker />,
  },
  {
    name: 'expiry_date' as const,
    children: <DatePicker />,
  },
]

const ValuationResourceConsentRecordForm = () => {
  const { id: valuationPk } = useValuation()
  const { status, form, ...formProps } = useMutationForm(createRecord, {
    reset: true,
  })

  function handleUploadChange(e: React.ChangeEvent<HTMLInputElement>) {
    form.setFieldValue([BODY_PATH, 'attachments'], e.target.files?.item(0))
  }

  if (!valuationPk) return null

  return (
    <Form
      {...formProps}
      form={form}
      initialValues={{ valuationPk }}
      layout="vertical"
      className={styles.container}
    >
      <Form.Item hidden name="valuationPk">
        <Input />
      </Form.Item>
      <FormItems items={body} path={BODY_PATH} />
      <div className={styles.dates}>
        <FormItems items={dates} path={BODY_PATH} />
      </div>
      <div className={styles.upload}>
        <Form.Item hidden name={[BODY_PATH, 'attachments']}>
          <Input />
        </Form.Item>
        <input
          title="Upload Record"
          id="addRecordFormUploadInput"
          type="file"
          onChange={handleUploadChange}
        />
      </div>
      <Form.Item>
        <Button type="primary" htmlType="submit" loading={status.isLoading}>
          Save
        </Button>
      </Form.Item>
    </Form>
  )
}

export default ValuationResourceConsentRecordForm
