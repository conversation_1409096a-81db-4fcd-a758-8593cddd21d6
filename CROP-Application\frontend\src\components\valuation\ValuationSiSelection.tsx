import { Select, type SelectProps } from 'antd'
import type { Valuations } from '@store/services/sdk'
import { PLACEHOLDER_CHAR } from '@util/const'

const values: Valuations['si'][] = ['A', 'B', 'C', 'D', 'E', 'F', 'G']

const options = values.map((value) => ({ label: value, value }))

const ValuationSiSelect = ({ ...props }: SelectProps) => {
  return <Select options={options} placeholder={PLACEHOLDER_CHAR} {...props} />
}
export default ValuationSiSelect
