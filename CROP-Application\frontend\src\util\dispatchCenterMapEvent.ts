import { LatLng } from 'leaflet'

export const CENTER_MAP_ON_SELECTED = 'centerMapOnSelected'

export interface CenterMap {
  position: LatLng
  id: string
}

function dispatchCenterMapEvent(id: string, lat: number, lng: number) {
  dispatchEvent(
    new CustomEvent<CenterMap>(CENTER_MAP_ON_SELECTED, {
      detail: {
        position: new LatLng(lat, lng),
        id,
      },
    })
  )
}

export default dispatchCenterMapEvent
