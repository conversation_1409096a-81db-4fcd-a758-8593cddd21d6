import { getLegend } from '@/types/GISLayerDescriptors'
import { groupBy } from 'lodash'
import type { ValuationsAnzUnionFeatureList } from '@store/services/sdk'
import { truthy } from '@util/guards'
import { sumProperty } from './helpers'

const legend = getLegend('luc')

const getDescriptor = (key: string) =>
  (legend?.getDescriptor(key) ?? '').replace(/^.\s-\s/, '')

function useLuc(union: ValuationsAnzUnionFeatureList) {
  const features = union.features.filter(
    (feature) => typeof feature.properties?.luc === 'string'
  )

  const groupedFeatures = groupBy(features, (f) => f.properties?.luc)

  return Object.entries(groupedFeatures).map(([key, features]) => {
    const properties = features
      .map((feature) => feature.properties)
      .filter(truthy)
    const lucArea = sumProperty(properties, 'prorataArea') / 1e4
    const areaPercent = `${sumProperty(
      properties,
      'geometryAreaPercentage'
    ).toFixed(2)}%`

    return {
      luc: key,
      definition: getDescriptor(key),
      area: lucArea.toFixed(4),
      areaPercent,
    }
  })
}

export default useLuc
