.darkMode {
  --color-semantic-green: #25b896;
  --color-semantic-red: #fe7c7c;
  --color-semantic-orange: #f0a222;
}

.greyBackground {
  --color-highlight: var(--color-ui-blue);
  --color-background: var(--color-grey-1);
}

.clearSkyBackground {
  --color-highlight: var(--color-ui-blue);
  --color-background: var(--color-clear-skies-ultrasoft);
}

.pacificBlueBackground {
  --color-primary: var(--color-white);
  --color-secondary: var(--color-white);
  --color-highlight: var(--color-white);
  --color-accent: var(--color-white);
  --color-background: var(--color-pacific-blue);
}

.primary {
  color: var(--color-primary);
}

.white {
  color: var(--color-white);
}

.muted {
  color: var(--color-secondary);
}

.dark {
  color: var(--color-dark);
}

.danger {
  color: var(--color-semantic-red);
}

.ui {
  color: var(--color-ui-blue);
}

.pacific {
  color: var(--color-pacific-blue);
}

.deepSea {
  color: var(--color-deep-sea);
}

.hoverline {
  border-bottom: 2px solid transparent;

  &:hover {
    border-bottom-color: currentColor;
  }
}

.button {
  line-height: 1.2;
  font-weight: 500;
  color: var(--color-white) !important;
  background: var(--color-ui-blue) ;
  padding: 0.55em 1.25em;
  border-radius: 2em;
  /*min-width: 180px;*/
  border: 2px var(--color-ui-blue) solid;

  &:hover {
    color: var(--color-ui-blue) !important;
    background-color: transparent;
    text-decoration: none;
  }

  &:disabled {
    pointer-events: none;
    background-color: var(--color-grey-3);
    border-color: var(--color-grey-3);
  }
}

.radioContainer {
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  align-items: baseline;
  column-gap: 0.5em;
  row-gap: 0.5em;
  margin-bottom: 0;
  cursor: pointer;
  /*text-transform: capitalize;*/
}

.radio {
  position: relative;
  width: 0;
  height: 0;
  margin-left: 18px;
  overflow: visible;

  &:after {
    position: absolute;
    top: 3px;
    right: 0px;
    color: var(--color-ui-blue);
    content: url('/static/icons/Unselected.svg');
    display: block;
    width: 18px;
    height: 18px;
    cursor: pointer;
  }

  &:checked:after {
    content: url('/static/icons/Radio.svg');
  }
}

.select {
  font-size: 16px;

  :global(.ant-select-item), :global(.ant-input-affix-wrapper), :global(.ant-input) {
    font-size: 16px
  }
}
