import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Build paths inside the project
BASE_DIR = Path(__file__).resolve().parent.parent

# Secret key for production - for dev use only
SECRET_KEY = os.getenv('SECRET_KEY', 'xx')

# Do not run with debug turned on in production!!!
DEBUG = os.getenv('DEBUG', 'False') == 'True'

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',
    'api',  # BFF API app
    ]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'api.middleware.BFFSessionMiddleware',
    ]

ROOT_URLCONF = 'bff_project.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': ['django.template.context_processors.debug', 'django.template.context_processors.request', 'django.contrib.auth.context_processors.auth', 'django.contrib.messages.context_processors.messages',
            ],},},]

WSGI_APPLICATION = 'bff_project.wsgi.application'

DATABASES = {'default': {'ENGINE': 'django.db.backends.sqlite3','NAME': BASE_DIR / 'db.sqlite3',}}

AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator'},
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
    ]

LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# CORS settings
CORS_ALLOWED_ORIGINS = os.getenv('CORS_ALLOWED_ORIGINS', 'http://localhost:3000').split(',')
CORS_ALLOW_CREDENTIALS = True

# BFF specific settings
BFF_SESSION_COOKIE_NAME = 'bff_session'
BFF_SESSION_EXPIRY = 3600
BFF_SESSION_COOKIE_SECURE = not DEBUG

# Backend service URLs
BACKEND_API_URL = os.getenv('BACKEND_API_URL', 'http://localhost:8000/api')
PINGFEDERATE_URL = os.getenv('PINGFEDERATE_URL', 'https://pingfederate.example.com') # Need to check with platform on the URL

# Rate limiting settings
NINJA_RATE_LIMIT_USER = '100/hour'
NINJA_RATE_LIMIT_ANON = '20/hour'

# Add PingFederate specific settings
PINGFEDERATE_BASE_URL = os.getenv('PINGFEDERATE_BASE_URL', 'https://idpengine.coz.dev-5.stau.np.au1.aws.anz.com')
PINGFEDERATE_ISSUER = os.getenv('PINGFEDERATE_ISSUER', 'https://iamidentity.federate.dev.service.dev')
PINGFEDERATE_CLIENT_ID = os.getenv('PINGFEDERATE_CLIENT_ID', '')
PINGFEDERATE_CLIENT_SECRET = os.getenv('PINGFEDERATE_CLIENT_SECRET', '')
PINGFEDERATE_TOKEN_ENDPOINT = os.getenv('PINGFEDERATE_TOKEN_ENDPOINT', '/as/token.oauth2')
PINGFEDERATE_USERINFO_ENDPOINT = os.getenv('PINGFEDERATE_USERINFO_ENDPOINT', '/idp/userinfo.openid')
PINGFEDERATE_INTROSPECTION_ENDPOINT = os.getenv('PINGFEDERATE_INTROSPECTION_ENDPOINT', '/as/introspect.oauth2')
PINGFEDERATE_AUTHORIZATION_ENDPOINT = os.getenv('PINGFEDERATE_AUTHORIZATION_ENDPOINT', '/as/authorization.oauth2')
PINGFEDERATE_JWKS_ENDPOINT = os.getenv('PINGFEDERATE_JWKS_ENDPOINT', '/ext/pf/JWKS')
PINGFEDERATE_REDIRECT_URI = os.getenv('PINGFEDERATE_REDIRECT_URI', 'http://localhost:8080/api/auth/callback')
