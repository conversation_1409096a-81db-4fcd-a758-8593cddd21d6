import { from<PERSON>ig<PERSON><PERSON>ber, toBig<PERSON>umber } from '@types'

export interface SerializableComparableSaleAdjustment {
  id: number
  comparableSale?: number

  adjustmentType: string
  buildingAdjustment: boolean

  adjustmentPercent: string

  comparability: string
  comparabilityWithPercentage: string
  comments: string
}

export function deserializeComparableSaleAdjustment({
  adjustmentPercent,
  ...rest
}: SerializableComparableSaleAdjustment) {
  return {
    ...rest,
    ...toBigNumber({
      adjustmentPercent,
    }),
  }
}

export type ComparableSaleAdjustment = ReturnType<
  typeof deserializeComparableSaleAdjustment
>

export function serializeComparableSaleAdjustment({
  adjustmentPercent,
  ...rest
}: ComparableSaleAdjustment): SerializableComparableSaleAdjustment {
  return {
    ...rest,
    ...fromBigNumber({
      adjustmentPercent,
    }),
  }
}

// more specific adjustment made on individual land classes of a comparable sale to enable the valuer
// to compare a sale to a subject property on specific parts of the land breakdown. Used in cases where the subject property
// is quite complex and so the comparable sales aren't necessarily like-for-like, so it is useful to be able to do a comparability analysis
// on individual land classes.
export interface SerializableComparableSaleAssetMetricAdjustment {
  id: number | undefined
  comparableSale?: number
  landAssetMetric: number

  comparability: string
  comparableSubjectAep: string
  productivityAdjustmentPercent: string
}

export function deserializeComparableSaleAssetMetricAdjustment({
  comparableSubjectAep,
  productivityAdjustmentPercent,
  ...rest
}: SerializableComparableSaleAssetMetricAdjustment) {
  return {
    ...rest,
    ...toBigNumber({
      comparableSubjectAep,
      productivityAdjustmentPercent,
    }),
  }
}

export type ComparableSaleAssetMetricAdjustment = ReturnType<
  typeof deserializeComparableSaleAssetMetricAdjustment
>

export function serializeComparableSaleAssetMetricAdjustment({
  comparableSubjectAep,
  productivityAdjustmentPercent,
  ...rest
}: ComparableSaleAssetMetricAdjustment): SerializableComparableSaleAssetMetricAdjustment {
  return {
    ...rest,
    ...fromBigNumber({
      comparableSubjectAep,
      productivityAdjustmentPercent,
    }),
  }
}
