.sans,
.headingOne,
.headingTwo,
.headingThree,
.headingFour,
.headingFive,
.headingSix,
.body,
.bodySmall,
.bodyLarge,
.medium,
.menu,
.aside {
  font-family: Aeonik;
  line-height: 1.5;
}

.body,
.bodySmall,
.bodyLarge,
.aside {
  font-weight: 400;
  /*-webkit-font-smoothing: auto;*/
}

.headingOne,
.headingTwo,
.headingThree,
.headingFour,
.headingFive,
.headingSix,
.menu,
.medium {
  font-weight: 500;
  letter-spacing: -.005em;
  /*-webkit-font-smoothing: antialiased;*/
}

.bold {
  font-weight: 700;
  /*-webkit-font-smoothing: antialiased;*/
}

.headingOne {
  font-size: 60px;
}

.headingTwo {
  font-size: 48px;
}

.headingThree {
  font-size: 40px;
}

.headingFour {
  font-size: 32px;
  /* Experimenting: */
}

.headingFive {
  font-size: 24px;
}

.headingSix {
  font-size: 20px;
}

.body {
  font-size: 16px;
}

.bodySmall,
.aside {
  font-size: 14px;
}

.bodyLarge {
  font-size: 18px;
}

.menu {
  font-size: 13px;
}

.aside {
  line-height: 1.285;
  letter-spacing: 0.002em;
  word-break: break-word;
}

.anchor,
.anchorUnderlined {
  color: var(--color-interactive);

  &:hover {
    color: var(--color-interactive--hover);
  }
}

.anchorUnderlined {
  text-decoration: underline;
}

.formatting {
  p {
    margin: 0;
  }

  p + p {
    margin-top: 1em;
  }

  ul {
    margin: 1em 0;
    padding-left: 1em;
    list-style: disc;

    &:last-child {
      margin-bottom: 0;
    }
  }

  li + li {
    margin-top: 0.5em;
  }
}

.aside {
  p + p {
    margin-top: 1em;
  }
}
