import { type ReactNode, createContext, useContext } from 'react'

interface State {
  id: number | undefined
}

interface Props extends State {
  children: ReactNode
}

const ValuationContext = createContext<State>({ id: undefined })

export function ValuationProvider({ children, id }: Props) {
  return (
    <ValuationContext.Provider value={{ id }}>
      {children}
    </ValuationContext.Provider>
  )
}

export function useValuation() {
  const context = useContext(ValuationContext)

  if (context === undefined) {
    throw new Error('useValuation must be used with a ValuationProvider')
  }

  return context
}
