from ninja import NinjaAP<PERSON>, Router
from ninja.errors import HttpError
from django.http import HttpRequest, HttpResponse
from typing import List, Dict, Any
import json
import uuid
import time
from django.shortcuts import redirect
from django.urls import reverse
from .auth import PingFederate<PERSON>uth
from .rate_limiting import UserRateThrottle, AnonymousRateThrottle
from .services import BackendServiceProxy
from .pingfederate_client import PingFederateClient
from .schemas import (
    ErrorResponse, UserProfile, AggregatedData, 
    Service1Data, Service2Data, PublicData,
    TokenResponse, LoginRequest, AuthorizationResponse
)

api = NinjaAPI(
    title="BFF API",
    description="Backend for Frontend API with PingFederate authentication",
    version="1.0.0",
    auth=PingFederateAuth(),
)

# Create a router for auth endpoints that don't require authentication
auth_router = Router(tags=["Authentication"])

# Rate Limiting
user_throttle = UserRateThrottle()
anon_throttle = AnonymousRateThrottle()

@api.exception_handler(HttpError)
def http_error_handler(request, exc):
    return api.create_response(
        request, {"error": str(exc)}, status=exc.status_code,
    )

# Check rate limits and raise HttpError if exceeded
def check_rate_limit(request: HttpRequest) -> None:
    if hasattr(request, 'user') and request.user.is_authenticated:
        if not user_throttle.allow_request(request):
            raise HttpError(429, "Rate limit exceeded")
    else:
        if not anon_throttle.allow_request(request):
            raise HttpError(429, "Rate limit exceeded")

# Extract authentication headers to forward to backend services
def get_auth_headers(request: HttpRequest) -> Dict[str, str]:
    if hasattr(request, 'bff_session') and request.bff_session:
        return {'Authorization': f"Bearer {request.bff_session['access_token']}"}
    return {}

@auth_router.post("/login", response={200: TokenResponse, 401: ErrorResponse, 429: ErrorResponse})
def login(request: HttpRequest, data: LoginRequest):
    """ Here login will be done using LAN ID username and password"""
    check_rate_limit(request)
    try:
        # This is a placeholder. Need to implement usage of LAN ID and password
        # Returning a mock token response
        raise HttpError(401, "Direct login not supported. Use /auth/authorize for OAuth flow.")
    except HttpError:
        raise
    except Exception as e:
        raise HttpError(500, str(e))

@auth_router.get("/authorize", response={200: AuthorizationResponse, 429: ErrorResponse})
def authorize(request: HttpRequest):
    """Start OAuth 2.0 Authorization Code flow"""
    check_rate_limit(request)
    try:
        # Generate state parameter to prevent CSRF
        state = str(uuid.uuid4())
        request.session['oauth_state'] = state
        # Get authorization URL
        authorization_url = PingFederateClient.get_authorization_url(state)
        return {"authorization_url": authorization_url}
    except Exception as e:
        raise HttpError(500, str(e))

@auth_router.get("/callback", response={302: None, 400: ErrorResponse, 429: ErrorResponse})
def callback(request: HttpRequest):
    check_rate_limit(request)
    try:
        # Get authorization code from query parameters
        code = request.GET.get('code')
        state = request.GET.get('state')
        
        # Validate state parameter to prevent CSRF
        if not state or state != request.session.get('oauth_state'):
            raise HttpError(400, "Invalid state parameter")
        
        # Clear state from session
        if 'oauth_state' in request.session:
            del request.session['oauth_state']
        
        if not code:
            raise HttpError(400, "Authorization code not provided")
        
        # Exchange code for tokens
        token_response = PingFederateClient.exchange_code_for_token(code)
        
        if not token_response:
            raise HttpError(400, "Failed to exchange code for tokens")
        
        # Store tokens in session
        request.session['access_token'] = token_response.get('access_token')
        request.session['refresh_token'] = token_response.get('refresh_token')
        request.session['token_expiry'] = time.time() + token_response.get('expires_in', 3600)
        
        # Get user info
        user_info = PingFederateClient.get_user_info(token_response.get('access_token'))
        
        if user_info:
            # Create or get user
            from .auth import PingFederateAuth
            auth = PingFederateAuth()
            auth_result = auth.authenticate(request, token_response.get('access_token'))
            
            if auth_result:
                # Store auth data for middleware
                request.auth_data = {
                    'access_token': token_response.get('access_token'),
                    'refresh_token': token_response.get('refresh_token'),
                    'expires_at': time.time() + token_response.get('expires_in', 3600),
                }
        
        # Redirect to frontend
        frontend_url = request.session.get('frontend_redirect_url', '/')
        return redirect(frontend_url)
    except HttpError:
        raise
    except Exception as e:
        raise HttpError(500, str(e))

@auth_router.post("/logout", response={200: None, 429: ErrorResponse})
def logout(request: HttpRequest):
    check_rate_limit(request)
    try:
        # Clear session
        request.session.flush()
        
        # Do we need to revoke token at PingFederate?
        return {}
    except Exception as e:
        raise HttpError(500, str(e))

api.add_router("/auth", auth_router)

@api.get("/aggregated-data", response={200: AggregatedData, 400: ErrorResponse, 429: ErrorResponse, 502: ErrorResponse})
def aggregated_data(request: HttpRequest):
    """Get aggregated data from multiple backend services"""
    check_rate_limit(request)
    
    try:
        # Get data from riskradar service 1
        service1_response = BackendServiceProxy.forward_request(
            'service1/data/', headers=get_auth_headers(request)
        )
        
        # Get data from riskradar service 2
        service2_response = BackendServiceProxy.forward_request(
            'service2/data/', headers=get_auth_headers(request)
        )
        
        # Check if both requests were successful
        if service1_response['status_code'] != 200 or service2_response['status_code'] != 200:
            raise HttpError(502, "Failed to fetch data from backend services")
        
        # Combine and transform riskradar data for frontend
        return {
            'service1': service1_response['data'],
            'service2': service2_response['data'],
        }
    except HttpError:
        raise
    except Exception as e:
        raise HttpError(500, str(e))

@api.get("/user/profile", response={200: UserProfile, 401: ErrorResponse, 429: ErrorResponse})
def user_profile(request: HttpRequest):
    """Get user profile data"""
    check_rate_limit(request)
    
    try:
        # Get user profile data from backend
        response = BackendServiceProxy.forward_request(
            'users/profile/', headers=get_auth_headers(request)
            )
        if response['status_code'] != 200:
            raise HttpError(response['status_code'], "Failed to fetch user profile")
        
        # Return the profile data
        return response['data']
    except HttpError:
        raise
    except Exception as e:
        raise HttpError(500, str(e))

@api.get("/public/data", auth=None, response={200: PublicData, 429: ErrorResponse})
def public_data(request: HttpRequest):
    """Get public data that doesn't require authentication"""
    check_rate_limit(request)
    try:
        # Get public data from backend
        response = BackendServiceProxy.forward_request('public/data/')
        if response['status_code'] != 200:
            raise HttpError(response['status_code'], "Failed to fetch public data")
        return response['data']
    except HttpError:
        raise
    except Exception as e:
        raise HttpError(500, str(e))


# ============================================================================
# DJANGO NINJA API PROXY ENDPOINTS
# ============================================================================

# Create a generic proxy router for Django Ninja endpoints
proxy_router = Router(tags=["Django Ninja Proxy"])

@proxy_router.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
def proxy_django_ninja(request: HttpRequest, path: str):
    """
    Generic proxy endpoint for all Django Ninja API endpoints
    This forwards all requests to the Django Ninja backend
    """
    check_rate_limit(request)

    try:
        # Get request method
        method = request.method

        # Get query parameters
        params = dict(request.GET.items()) if request.GET else None

        # Get request body for POST/PUT/PATCH requests
        data = None
        if method in ['POST', 'PUT', 'PATCH'] and request.body:
            try:
                data = json.loads(request.body.decode('utf-8'))
            except json.JSONDecodeError:
                data = {'raw_body': request.body.decode('utf-8')}

        # Forward the request
        response = BackendServiceProxy.forward_request(
            endpoint=path,
            method=method,
            headers=get_auth_headers(request),
            data=data,
            params=params
        )

        # Return response with appropriate status code
        if response['status_code'] >= 400:
            raise HttpError(response['status_code'], response['data'].get('error', 'Backend service error'))

        return response['data']

    except HttpError:
        raise
    except Exception as e:
        raise HttpError(500, str(e))

# Add the proxy router to the main API
api.add_router("/ninja", proxy_router)

# ============================================================================
# SPECIFIC MODULE PROXY ENDPOINTS
# ============================================================================

# Core API endpoints proxy
core_router = Router(tags=["Core API"])

@core_router.get("/health")
def health_check_proxy(request: HttpRequest):
    """Proxy for core API health check"""
    check_rate_limit(request)
    response = BackendServiceProxy.forward_get("health", headers=get_auth_headers(request))
    if response['status_code'] != 200:
        raise HttpError(response['status_code'], "Backend health check failed")
    return response['data']

# Address endpoints proxy
@core_router.api_route("/addresses/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def addresses_proxy(request: HttpRequest, path: str = ""):
    """Proxy for address endpoints"""
    check_rate_limit(request)
    endpoint = f"addresses/{path}" if path else "addresses/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'Address service error'))

    return response['data']

# Sales endpoints proxy
@core_router.api_route("/sales/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def sales_proxy(request: HttpRequest, path: str = ""):
    """Proxy for sales endpoints"""
    check_rate_limit(request)
    endpoint = f"sales/{path}" if path else "sales/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'Sales service error'))

    return response['data']

# Valuations endpoints proxy
@core_router.api_route("/valuations/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def valuations_proxy(request: HttpRequest, path: str = ""):
    """Proxy for valuations endpoints"""
    check_rate_limit(request)
    endpoint = f"valuations/{path}" if path else "valuations/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'Valuations service error'))

    return response['data']

# Customers endpoints proxy
@core_router.api_route("/customers/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def customers_proxy(request: HttpRequest, path: str = ""):
    """Proxy for customers endpoints"""
    check_rate_limit(request)
    endpoint = f"customers/{path}" if path else "customers/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'Customers service error'))

    return response['data']

api.add_router("/api", core_router)

# ============================================================================
# RISKRADAR MODULE PROXY ENDPOINTS
# ============================================================================

riskradar_router = Router(tags=["RiskRadar API"])

@riskradar_router.get("/health")
def riskradar_health_proxy(request: HttpRequest):
    """Proxy for RiskRadar health check"""
    check_rate_limit(request)
    response = BackendServiceProxy.forward_get("riskradar/health", headers=get_auth_headers(request))
    if response['status_code'] != 200:
        raise HttpError(response['status_code'], "RiskRadar health check failed")
    return response['data']

# RiskRadar locations proxy
@riskradar_router.api_route("/locations/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def riskradar_locations_proxy(request: HttpRequest, path: str = ""):
    """Proxy for RiskRadar locations endpoints"""
    check_rate_limit(request)
    endpoint = f"riskradar/locations/{path}" if path else "riskradar/locations/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'RiskRadar locations service error'))

    return response['data']

# RiskRadar perils proxy
@riskradar_router.api_route("/perils/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def riskradar_perils_proxy(request: HttpRequest, path: str = ""):
    """Proxy for RiskRadar perils endpoints"""
    check_rate_limit(request)
    endpoint = f"riskradar/perils/{path}" if path else "riskradar/perils/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'RiskRadar perils service error'))

    return response['data']

# RiskRadar loss models proxy
@riskradar_router.api_route("/loss-models/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def riskradar_loss_models_proxy(request: HttpRequest, path: str = ""):
    """Proxy for RiskRadar loss models endpoints"""
    check_rate_limit(request)
    endpoint = f"riskradar/loss-models/{path}" if path else "riskradar/loss-models/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'RiskRadar loss models service error'))

    return response['data']

# RiskRadar risk groups proxy
@riskradar_router.api_route("/risk-groups/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def riskradar_risk_groups_proxy(request: HttpRequest, path: str = ""):
    """Proxy for RiskRadar risk groups endpoints"""
    check_rate_limit(request)
    endpoint = f"riskradar/risk-groups/{path}" if path else "riskradar/risk-groups/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'RiskRadar risk groups service error'))

    return response['data']

# RiskRadar exposures proxy
@riskradar_router.api_route("/exposures/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def riskradar_exposures_proxy(request: HttpRequest, path: str = ""):
    """Proxy for RiskRadar exposures endpoints"""
    check_rate_limit(request)
    endpoint = f"riskradar/exposures/{path}" if path else "riskradar/exposures/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'RiskRadar exposures service error'))

    return response['data']

# RiskRadar analytics proxy
@riskradar_router.api_route("/analytics/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def riskradar_analytics_proxy(request: HttpRequest, path: str = ""):
    """Proxy for RiskRadar analytics endpoints"""
    check_rate_limit(request)
    endpoint = f"riskradar/analytics/{path}" if path else "riskradar/analytics/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'RiskRadar analytics service error'))

    return response['data']

api.add_router("/riskradar", riskradar_router)

# ============================================================================
# OTHER MODULE PROXY ENDPOINTS
# ============================================================================

# Green module proxy
green_router = Router(tags=["Green API"])

@green_router.get("/health")
def green_health_proxy(request: HttpRequest):
    """Proxy for Green module health check"""
    check_rate_limit(request)
    response = BackendServiceProxy.forward_get("green/health", headers=get_auth_headers(request))
    if response['status_code'] != 200:
        raise HttpError(response['status_code'], "Green module health check failed")
    return response['data']

@green_router.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def green_proxy(request: HttpRequest, path: str = ""):
    """Proxy for Green module endpoints"""
    check_rate_limit(request)
    endpoint = f"green/{path}" if path else "green/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'Green module service error'))

    return response['data']

# Finance module proxy
finance_router = Router(tags=["Finance API"])

@finance_router.get("/health")
def finance_health_proxy(request: HttpRequest):
    """Proxy for Finance module health check"""
    check_rate_limit(request)
    response = BackendServiceProxy.forward_get("finance/health", headers=get_auth_headers(request))
    if response['status_code'] != 200:
        raise HttpError(response['status_code'], "Finance module health check failed")
    return response['data']

@finance_router.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def finance_proxy(request: HttpRequest, path: str = ""):
    """Proxy for Finance module endpoints"""
    check_rate_limit(request)
    endpoint = f"finance/{path}" if path else "finance/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'Finance module service error'))

    return response['data']

# CCRA module proxy
ccra_router = Router(tags=["CCRA API"])

@ccra_router.get("/health")
def ccra_health_proxy(request: HttpRequest):
    """Proxy for CCRA module health check"""
    check_rate_limit(request)
    response = BackendServiceProxy.forward_get("ccra/health", headers=get_auth_headers(request))
    if response['status_code'] != 200:
        raise HttpError(response['status_code'], "CCRA module health check failed")
    return response['data']

@ccra_router.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def ccra_proxy(request: HttpRequest, path: str = ""):
    """Proxy for CCRA module endpoints"""
    check_rate_limit(request)
    endpoint = f"ccra/{path}" if path else "ccra/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'CCRA module service error'))

    return response['data']

# PropertyFlow module proxy
propertyflow_router = Router(tags=["PropertyFlow API"])

@propertyflow_router.get("/health")
def propertyflow_health_proxy(request: HttpRequest):
    """Proxy for PropertyFlow module health check"""
    check_rate_limit(request)
    response = BackendServiceProxy.forward_get("propertyflow/health", headers=get_auth_headers(request))
    if response['status_code'] != 200:
        raise HttpError(response['status_code'], "PropertyFlow module health check failed")
    return response['data']

@propertyflow_router.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
def propertyflow_proxy(request: HttpRequest, path: str = ""):
    """Proxy for PropertyFlow module endpoints"""
    check_rate_limit(request)
    endpoint = f"propertyflow/{path}" if path else "propertyflow/"

    method = request.method
    params = dict(request.GET.items()) if request.GET else None
    data = None

    if method in ['POST', 'PUT', 'PATCH'] and request.body:
        try:
            data = json.loads(request.body.decode('utf-8'))
        except json.JSONDecodeError:
            pass

    response = BackendServiceProxy.forward_request(
        endpoint=endpoint,
        method=method,
        headers=get_auth_headers(request),
        data=data,
        params=params
    )

    if response['status_code'] >= 400:
        raise HttpError(response['status_code'], response['data'].get('error', 'PropertyFlow module service error'))

    return response['data']

# Add all module routers
api.add_router("/green", green_router)
api.add_router("/finance", finance_router)
api.add_router("/ccra", ccra_router)
api.add_router("/propertyflow", propertyflow_router)