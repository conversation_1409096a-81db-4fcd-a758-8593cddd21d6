import time
from typing import Dict, Optional
from django.conf import settings
from django.http import HttpRequest
from django.core.cache import cache
import logging

logger = logging.getLogger(__name__)

class RateLimiter:
    """Base rate limiter class"""
    
    def __init__(self, rate: str):
        """
        Initialize rate limiter
        
        Args:
            rate: Rate limit string in format "requests/period" (e.g., "100/hour", "20/minute")
        """
        self.requests, self.period = self._parse_rate(rate)
        self.window_size = self._get_window_size(self.period)
    
    def _parse_rate(self, rate: str) -> tuple:
        """Parse rate string into requests and period"""
        try:
            requests_str, period = rate.split('/')
            return int(requests_str), period.lower()
        except (ValueError, AttributeError):
            raise ValueError(f"Invalid rate format: {rate}. Expected format: 'requests/period'")
    
    def _get_window_size(self, period: str) -> int:
        """Convert period to seconds"""
        period_map = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400,
        }
        return period_map.get(period, 3600)  # Default to 1 hour
    
    def _get_cache_key(self, identifier: str) -> str:
        """Generate cache key for rate limiting"""
        return f"rate_limit:{identifier}:{int(time.time() // self.window_size)}"
    
    def allow_request(self, identifier: str) -> bool:
        """
        Check if request is allowed based on rate limit
        
        Args:
            identifier: Unique identifier for the client (user ID, IP address, etc.)
            
        Returns:
            True if request is allowed, False otherwise
        """
        cache_key = self._get_cache_key(identifier)
        
        try:
            # Get current count from cache
            current_count = cache.get(cache_key, 0)
            
            if current_count >= self.requests:
                logger.warning(f"Rate limit exceeded for {identifier}: {current_count}/{self.requests}")
                return False
            
            # Increment counter
            cache.set(cache_key, current_count + 1, self.window_size)
            return True
            
        except Exception as e:
            logger.error(f"Error checking rate limit for {identifier}: {str(e)}")
            # Allow request if cache fails (fail open)
            return True
    
    def get_remaining_requests(self, identifier: str) -> int:
        """Get remaining requests for identifier"""
        cache_key = self._get_cache_key(identifier)
        current_count = cache.get(cache_key, 0)
        return max(0, self.requests - current_count)
    
    def get_reset_time(self, identifier: str) -> int:
        """Get timestamp when rate limit resets"""
        current_window = int(time.time() // self.window_size)
        return (current_window + 1) * self.window_size


class UserRateThrottle(RateLimiter):
    """Rate limiter for authenticated users"""
    
    def __init__(self):
        rate = getattr(settings, 'NINJA_RATE_LIMIT_USER', '100/hour')
        super().__init__(rate)
    
    def allow_request(self, request: HttpRequest) -> bool:
        """Check if request is allowed for authenticated user"""
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return True  # Let anonymous throttle handle it
        
        identifier = f"user:{request.user.id}"
        return super().allow_request(identifier)
    
    def get_identifier(self, request: HttpRequest) -> Optional[str]:
        """Get identifier for authenticated user"""
        if hasattr(request, 'user') and request.user.is_authenticated:
            return f"user:{request.user.id}"
        return None


class AnonymousRateThrottle(RateLimiter):
    """Rate limiter for anonymous users"""
    
    def __init__(self):
        rate = getattr(settings, 'NINJA_RATE_LIMIT_ANON', '20/hour')
        super().__init__(rate)
    
    def allow_request(self, request: HttpRequest) -> bool:
        """Check if request is allowed for anonymous user"""
        identifier = self._get_client_ip(request)
        return super().allow_request(identifier)
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        return f"ip:{ip}"
    
    def get_identifier(self, request: HttpRequest) -> str:
        """Get identifier for anonymous user"""
        return self._get_client_ip(request)


class EndpointRateThrottle(RateLimiter):
    """Rate limiter for specific endpoints"""
    
    def __init__(self, rate: str):
        super().__init__(rate)
    
    def allow_request(self, request: HttpRequest, endpoint: str) -> bool:
        """Check if request is allowed for specific endpoint"""
        if hasattr(request, 'user') and request.user.is_authenticated:
            identifier = f"user:{request.user.id}:endpoint:{endpoint}"
        else:
            ip = self._get_client_ip(request)
            identifier = f"{ip}:endpoint:{endpoint}"
        
        return super().allow_request(identifier)
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        return f"ip:{ip}"


# Rate limiting decorators and utilities
def rate_limit_key(request: HttpRequest) -> str:
    """Generate a rate limiting key for the request"""
    if hasattr(request, 'user') and request.user.is_authenticated:
        return f"user:{request.user.id}"
    else:
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', 'unknown')
        return f"ip:{ip}"


def check_rate_limit(request: HttpRequest, rate: str = None) -> bool:
    """
    Check if request is within rate limit
    
    Args:
        request: Django HTTP request
        rate: Optional rate override (e.g., "10/minute")
        
    Returns:
        True if request is allowed, False otherwise
    """
    if rate:
        limiter = RateLimiter(rate)
        identifier = rate_limit_key(request)
        return limiter.allow_request(identifier)
    
    # Use default throttles
    if hasattr(request, 'user') and request.user.is_authenticated:
        throttle = UserRateThrottle()
        return throttle.allow_request(request)
    else:
        throttle = AnonymousRateThrottle()
        return throttle.allow_request(request)
