import type { Sale } from '@store/services/sdk'

export type SaleEditStateType = 'ADD' | 'NEW' | 'REMOVE' | 'EXISTING'

export interface SaleEditState {
  [saleCacheId: string]: {
    state: SaleEditStateType
    sale: Sale
    isCompatibleWithSubject: boolean
    saleHighestAndBestUseType?: number
    highestAndBestUseType?: number
    lsdbId?: string
  }
}

export const getSaleCacheId = (
  saleId: number | string,
  highestAndBestUseTypeId: number | undefined
) => {
  return `${saleId}-${highestAndBestUseTypeId ?? -1}`
}
