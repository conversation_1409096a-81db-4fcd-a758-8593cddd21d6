import KeyValueSummaryTable from './KeyValueSummaryTable'

type Props = {
  valuationDate?: string
  inspectionDate?: string
  reportDate?: string
  approach?: string
  bestUse?: string
}

const ReportTable = ({
  valuationDate,
  inspectionDate,
  reportDate,
  approach,
  bestUse,
}: Props) => {
  const rows = [
    { key: 'Valuation Date', value: valuationDate },
    { key: 'Inspection Date', value: inspectionDate },
    { key: 'Report Date', value: reportDate },
    { key: 'Approach', value: approach },
    { key: 'Highest and Best Use', value: bestUse },
  ]

  return <KeyValueSummaryTable rows={rows} />
}

export default ReportTable
