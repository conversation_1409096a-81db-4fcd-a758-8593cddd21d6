import { useCallback, useEffect, useMemo } from 'react'

/*
Listens for broadcast events of type `event`, and passes the parameter of the event into the effect.
note: events are broadcast as `event:<event_type>:parameter`
*/
export const useTabBroadcastChannel = (
  channelName: string,
  onMessage?: (message: MessageEvent) => void
): {
  broadcastChannel: BroadcastChannel
  postMessage: (message: unknown) => void
} => {
  const bc = useMemo(() => {
    return new BroadcastChannel(channelName)
  }, [channelName])

  useEffect(() => {
    return () => {
      console.info('bc closed')
      bc.close()
    }
  }, [bc])

  useEffect(() => {
    bc.onmessage = onMessage ?? null
  }, [bc, onMessage])

  const postMessage = useCallback(
    (msg: unknown) => {
      bc.postMessage(msg)
    },
    [bc]
  )

  return {
    postMessage: postMessage,
    broadcastChannel: bc,
  }
}
