import { FetchArgs } from '@reduxjs/toolkit/dist/query'

export const createMultipartQuery =
  <QueryArg extends Record<string, unknown>>(
    url: string | ((queryArg: QueryArg) => string),
    method: 'POST' | 'PATCH' | 'PUT' = 'POST'
  ) =>
  (queryArg: QueryArg): string | FetchArgs => {
    const formData = new FormData()

    for (const [key, value] of Object.entries(queryArg)) {
      if (value === undefined) continue
      if (value instanceof FileList) {
        for (const file of value) {
          formData.append(key, file)
        }
      } else {
        let data
        if (typeof value === 'string') {
          data = value
        } else if (Array.isArray(value)) {
          for (const arrayValue of value) {
            if (arrayValue !== undefined) formData.append(key, arrayValue)
          }
        } else {
          JSON.stringify(value)
        }
        if (data !== undefined) formData.append(key, data)
      }
    }

    return {
      method,
      url: typeof url === 'string' ? url : url(queryArg),
      body: formData,
      headers: {
        'Content-Type': undefined,
      },
    }
  }
