export type AddressesDashboardTabOption = 'CORELOGIC' | 'VALOCITY' | 'FRONTLINE'
export type AddressesDashboardSelectedView = 'QUEUE' | 'SEARCH'
export type HomePageSelectedTab =
  | 'RECENT_ADDRESSES'
  | 'RECENT_TRADING_GROUPS'
  | 'RECENT_VALUATIONS'
export type TabOption = 'CORELOGIC' | 'VALOCITY' | 'FRONTLINE'
export type SelectedView = 'USER' | 'SEARCH' | 'QUEUE'
export type ValuationsPageTabOption = 'USER' | 'RECENT' | 'PENDING'

export interface AddressesDashboardSettings {
  rightPane: {
    width: number
  }
  leftPane: AddressesDashboardLeftPaneSettings
}

export interface AddressesDashboardRightPaneSettings {
  width: number
}

export interface AddressesDashboardLeftPaneSettings
  extends AddressesDashboardRightPaneSettings {
  selectedView: AddressesDashboardSelectedView
  lat: number
  lng: number
  filters: unknown
}
export interface SalesAndListingsDashboardSettings {
  rightPane: {
    width: number
  }
  leftPane: SalesAndListingsDashboardLeftPaneSettings
}

export interface SalesAndListingsDashboardRightPaneSettings {
  width: number
}

export interface SalesAndListingsDashboardLeftPaneSettings
  extends SalesAndListingsDashboardRightPaneSettings {
  selectedView: SelectedView
  salesFilters: unknown
  listingsFilters: unknown
}

export interface ValuationPageSettings {
  width: number
  selectedView: string
}

export interface ValuationsDashboardSettings {
  rightPane: {
    width: number
  }
  leftPane: ValuationsDashboardLeftPaneSettings
}

export interface ValuationsDashboardRightPaneSettings {
  width: number
}

export interface ValuationsDashboardLeftPaneSettings
  extends ValuationsDashboardRightPaneSettings {
  selectedView: SelectedView
  userValuationsFilters: unknown
  filters: unknown
}

export interface HomePageSettings {
  width: number
  selectedView: string
  recentValuations?: unknown[]
  recentTradingGroups?: unknown[]
  recentAddresses?: unknown[]
}

export interface Settings {
  isValuer?: boolean
}
