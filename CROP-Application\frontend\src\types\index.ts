import { SerializedError } from '@reduxjs/toolkit'
import { Feature, Geometry } from 'geojson'
import { LatLngExpression } from 'leaflet'
import { User, ValuationInspection, Valuations } from '@store/services/sdk'
import { DistrictValuationRoll } from '@models/dvr/DistrictValuationRoll'
import { AnzUnionFeatureCollection } from '@models/gis/AnzUnionFeatureCollection'
import {
  TitleFeature,
  TitleProperties,
} from '@models/title/TitleFeatureCollection'
import { RVRValuation } from '@models/valuations/RVRValuation'
import { LABEL_DISPLAY_OPTIONS } from '@util/const'
import { HighestAndBestUseType } from '../models/assets/ValuationTypes'

export * from './Address'
export * from './BN'

export type LabelDisplayOption = (typeof LABEL_DISPLAY_OPTIONS)[number]

export type PotentialGeometryFeature = Feature<Geometry | null>

export interface LatLngLiteral {
  lat: number
  lng: number
}

export type ValidParams = {
  [key: string]: string[]
}

export interface SearchOption {
  value: string
  label: string
}

export interface PanelState {
  layerVisible: boolean
  panelExpanded: boolean
}
export interface AddressOption {
  value: string
  label: string
}

export interface SearchResults<T, P = ValidParams> {
  count: number
  totalPages: number
  firstPage: string
  lastPage: string
  nextPage: string
  previousPage: string
  currentPage: number
  results: T[]
  validSearchParams: P
}

export type ErrorMessage = string

export interface SearchResultsState<T> {
  isFetching: boolean
  error?: Error
  currentPage: number
  count: number
  nextPageUrl: string
  totalPages: number
  previousPageUrl: string
  lastPageUrl: string
  firstPageUrl: string
  data: {
    [id: string]: T
  }
  pages: {
    [pageNumber: number]: string[]
  }
}

export type FetchResult<T> = {
  state: 'loading' | 'loaded' | 'failed'
  data?: T
  error?: ErrorMessage
}

export interface GeoFeature<T = any> {
  type: 'Feature'
  id?: string | number | undefined
  properties: T
  geometry: any
}

export interface GeoFeatureGeometry {
  type: string
  coordinates: LatLngExpression[]
}

export interface Rainfall {
  summer: number
  autumn: number
  winter: number
  spring: number
}

export interface Title extends TitleProperties {
  feature: GeoFeature
}

export interface Property {
  address: string
  totalHa: number
  district: string
  localDistrict: string
  anzBranchRegion: string
  landUse: string
  category: string
  geom: GeoFeatureGeometry
}

export interface SelectedFeaturesValuePayload {
  layerSourceId?: string
  layerType: string
  type: string
  value: string
}

export interface PropertySale extends Property {
  id: string
  saleId: string
  settlementDate: string
  grossSalesPrice: number
  saleComplexity: string
}

export interface SaleFeature {
  titles: Title[]
  geoFeatures: GeoFeatureGroup
}

export interface PropertyListing extends Property {
  id: string
  listingDate: string
  listingId: string
  listingPrice: number
}

export type SaleSearchResultTab = 'sales' | 'listings'

export interface Selectable<T> {
  selected: boolean
  item: T
}

export interface Address {
  address: string
  addressId: string
  cnt: number
  districtValuationRoll: Partial<DistrictValuationRoll>
  landArea?: number
  lat: number
  lng: number
  linked?: boolean
  owners?: string
  fullAddress: string
  mortgagee?: string | null
  surveyArea: number
  titles: string[]
  totalArea?: number
  tlaName?: string
  union?: AnzUnionFeatureCollection
  tradingGroupId: number | null
  source?: string | null

  // DVR properties, should probably be on their own interface and under a key
  cv?: string
  iv?: string
  lv?: string
  landZone?: string
  lastSaleDate?: string
  lastSaleGrossSalesPrice?: number
  lastSaleSource?: string
  lastSaleId?: string
  landUse?: string
}

/**
 * @deprecated Should be using codegen types
 */
export interface TitleMemorial {
  id: string
  titleNo: string
  landDistrict: string
  memorialText: string
  current: string
  instrumentNumber: string
  instrumentLodgedDatetime: string
  instrumentType: string
  encumbrancees: string
  mortgageInstrumentNumber: string
}

export interface ValuationTitleMemorial extends TitleMemorial {
  valuation: number
}

export interface GeoFeatureGroup<T = any> {
  type: 'FeatureCollection'
  features: GeoFeature<T>[]
  properties?: {
    excluded_area?: number
  }
}

export interface GeoFeatureMap<T = { [key: string]: string }> {
  [id: string]: GeoFeature<T>
}

export interface ElevationFeature {
  elevation: number
  area: number
}

export interface ProfileSetting {
  isFetching?: boolean
}

export interface AppProps {
  authenticated: boolean
  user?: User
  currentAddress?: Address
  addressTitles: Title[]
}

export type TitleEditState = 'NEW' | 'ADD' | 'EXISTING' | 'DELETE'

export interface TitleEdit {
  state: TitleEditState
  title: TitleFeature
}

export interface AddressTitleEditMap {
  [titleId: string]: TitleEdit
}

export interface GreenMarketValue {
  total: number
}

export interface SidebarState {
  toggleState: boolean | undefined
  containerSpacerValue: string | undefined
  buttonSpacerValue: string | undefined
}

export type TreeSpecies = 'Radiata '

// TODO: Work out if we should componentize these interfaces (separate concerns, help enforce module boundaries)

export interface ChartDataset {
  backgroundColor: string[]
  data: number[]
  label: string
}

export interface ChartData {
  datasets: ChartDataset[]
  labels: string[]
}

export interface Forest {
  id?: string
  area: number
  forestType: string
  addressId: string
}

export interface Valuation {
  id: number
  address: string
  approachName: string
  valuationId: string
  valuationType: 'RIA' | 'RVR'
  saleId?: string
  // center: { lat: number; lng: number; }
  creator: string
  creatorName?: string
  valuationName: string
  /**
   * @deprecated
   **/
  valuationReference: string
  farmType?: string
  elevationStep?: number

  highestAndBestUseType?: HighestAndBestUseType
  highestAndBestUseTypeId?: number

  addressId: string
  createdDate: string
  updatedDate: string
  deletedDate?: string
  completedDate: string
  fullAddress: string
  frontlineCreated?: string
  tradingGroup?: string
  inspection: ValuationInspection
  linkedToProspect?: boolean
  transferAddressId?: string
  tradingGroupId?: string | null
  tradingGroupName?: string
  sourceSaleId?: string

  titleReviewStatus?: 0 | 1 | 2
  titleReviewComments?: number

  waterSecurityReviewStatus?: number
  waterSecurityReviewComments?: number

  benchmarkComparableSale?: number

  titleMatchingRatingValuation: DistrictValuationRoll | null

  totalSurveyArea: number
  totalArea: number
  totalHa?: number

  rvrValuation?: RVRValuation

  linzTitles: number[]
  hasKiwifruitBestUse: boolean

  remarksAndActions: Valuations['remarksAndActions']
  remarksAndActionsReportInclude: Valuations['remarksAndActionsReportInclude']
  marketValue?: string
  priorMarketValue: Valuations['priorMarketValue']
  tier: Valuations['tier']
  si: Valuations['si']
  lvr: Valuations['lvr']
  ccr: Valuations['ccr']
}

export interface AddressServiceCentre {
  address_id: string
  rank: number
  generalized_classification: string
  classification_code: number
  name: string
  distance: number
}

export interface AsyncState<T> {
  data?: T
  isFetching: boolean
  didInvalidate: boolean
  error?: SerializedError
  requestId?: string
  lastUpdated?: Date
}

export interface ElevationLimits {
  currentMin: number
  currentMax: number
  min: number
  max: number
  step: number
}

export const defaultAsyncState = <T>(defaultData: T): AsyncState<T> => ({
  data: defaultData,
  isFetching: false,
  didInvalidate: true,
})

export const defaultTradingGroup = {
  tradingGroupId: 'string',
  tradingGroupNumber: 'string',
  tradingGroupName: 'string',
  dairyCompanyId: 'string',
  districtId: 'string',
  industryId: 'string',
  controllingBranchId: 'string',
}

export interface InProgressMeasurement {
  type: string
  drawing: boolean
  measurement: number
}

export interface LucFeature {
  objectid: number
  luc: string
  lcorr: string
  ccto: string
  newZealandCorrelationUnit: string
  newZealandCorrelationUnitDescription: string
  erosionDescription: string
  nzCapabilityClass: string
  length: number
  area: number
}

export type PageState =
  | 'workflow'
  | 'search'
  | 'sales'
  | 'distances'
  | 'smap'
  | 'luc'
  | undefined

export interface UnionFeature {
  valuation: string
  contour: number
  vegetation: number
  ps: string
  luc: string
  area: number
}

export interface ResourceConsent {
  category: string
  irisId: string
  consentId: string
  subtype: string
  location: string
  status: string
  expiryDate: Date
  commencementDate: Date
  holder: string
  description: string
  longitude: string
  latitude: string
  council: string
  geometry: GeoFeatureGeometry
}
