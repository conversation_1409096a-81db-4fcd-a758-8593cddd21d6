/**
 * Helper functions for converting search params to/from state
 * should be replaced with nuqs when we can!
 */

import type { SetURLSearchParams } from 'react-router-dom'
import { isFunction } from 'lodash'

export const toSearchParams = <T extends Record<string, unknown>>(
  state: T
): URLSearchParams => {
  const searchParams = new URLSearchParams()
  for (const [key, value] of Object.entries(state)) {
    if (Array.isArray(value)) {
      for (const v of value) {
        if (v != null) {
          searchParams.append(key, v.toString())
        }
      }
    } else {
      if (value != null) {
        searchParams.append(key, value.toString())
      }
    }
  }
  return searchParams
}

export type SearchParamConfig<T> = Record<keyof T, 'array' | 'value'>

export const fromSearchParams = <T extends Record<string, unknown>>(
  searchParams: URLSearchParams,
  config: SearchParamConfig<T>
): T => {
  const state: Record<string, unknown | unknown[]> = {}
  for (const [key, value] of searchParams.entries()) {
    if (config[key] === 'array') {
      if (!state[key]) {
        state[key] = [value]
      } else {
        ;(state[key] as unknown[]).push(value)
      }
    } else {
      state[key] = value
    }
  }
  return state as T
}

export function createSetFilterState<T extends Record<string, unknown>>(
  config: SearchParamConfig<T>,
  setSearchParams: SetURLSearchParams
) {
  return (state: React.SetStateAction<T>) => {
    if (isFunction(state)) {
      setSearchParams((prev: URLSearchParams) => {
        const prevState = fromSearchParams<T>(prev, config)
        const updatedState = toSearchParams<T>(state(prevState))
        return updatedState
      })
    } else {
      setSearchParams(toSearchParams(state))
    }
    return state
  }
}
