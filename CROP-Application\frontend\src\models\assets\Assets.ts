import type BigNumber from 'bignumber.js'
import type { Feature, FeatureCollection, Geometry } from 'geojson'
import { BN, ZERO, fromBigNumber, toBigNumber } from '@types'
import { roundToNearestBN } from '@util'
import { emptyFeatureCollection, mapFeatureCollection } from '@util/geoUtil'
import {
  type HighestAndBestUseRemainingMetric,
  type SerializableHighestAndBestUseRemainingMetric,
  deserializeHighestAndBestUseRemainingMetric,
  serializeHighestAndBestUseRemainingMetric,
} from './HighestAndBestUseRemainingMetric'
import {
  type ImprovementAsset,
  type SerializableImprovementAsset,
  deserializeImprovementAsset,
  serializeImprovementAsset,
} from './ImprovementAsset'
import {
  type LandAsset,
  type SerializableLandAsset,
  deserializeLandAsset,
  serializeLandAsset,
} from './LandAsset'
import type { LandAssetMetric } from './LandAssetMetric'
import type { ValuationAdjustment } from './ValuationAdjustment'
import type { ValuationDescription } from './ValuationDescription'
import type { SerializableValuationSummary } from './ValuationTypes'
import type {
  HighestAndBestUseType,
  HighestAndBestUseTypeMap,
  SerializableHighestAndBestUseSummary,
} from './ValuationTypes'

export interface SerializableAssets {
  features: {
    Land: FeatureCollection<Geometry, SerializableLandAsset>
    Improvement: FeatureCollection<Geometry, SerializableImprovementAsset>
  }
  valuationSummary?: SerializableValuationSummary
  valuationDescriptions: ValuationDescription[]

  highestAndBestUseRemainingMetrics: SerializableHighestAndBestUseRemainingMetric[]
}

export function deserializeAssets(assets: SerializableAssets) {
  const {
    features: { Land, Improvement },
    highestAndBestUseRemainingMetrics,
    valuationDescriptions,
    valuationSummary,
    ...rest
  } = assets

  const deserializedRemainingMetrics = highestAndBestUseRemainingMetrics.map(
    deserializeHighestAndBestUseRemainingMetric
  )

  return {
    ...rest,
    valuationSummary: valuationSummary
      ? deserializeValuationSummary(valuationSummary)
      : undefined,
    highestAndBestUseRemainingMetrics: Object.fromEntries(
      deserializedRemainingMetrics.map((metric) => [
        metric.highestAndBestUseType,
        metric,
      ])
    ),
    valuationDescriptions: Object.fromEntries(
      valuationDescriptions.map((valuationDescription) => [
        valuationDescription.highestAndBestUseType,
        valuationDescription,
      ])
    ),
    features: {
      Land: mapFeatureCollection(Land, deserializeLandAsset),
      Improvement: mapFeatureCollection(
        Improvement,
        deserializeImprovementAsset
      ),
    },
  }
}

export type Assets = ReturnType<typeof deserializeAssets>

export function serializeAssets(assets: Assets): SerializableAssets {
  const {
    features: { Land, Improvement },
    highestAndBestUseRemainingMetrics,
    valuationDescriptions,
    valuationSummary,
    ...rest
  } = assets
  const serializedHighestAndBestUseRemainingMetrics = Object.values(
    highestAndBestUseRemainingMetrics
  ).map(serializeHighestAndBestUseRemainingMetric)
  return {
    ...rest,
    valuationSummary: valuationSummary
      ? serializeValuationSummary(valuationSummary)
      : undefined,
    highestAndBestUseRemainingMetrics:
      serializedHighestAndBestUseRemainingMetrics,
    valuationDescriptions: Object.values(valuationDescriptions),
    features: {
      Land: mapFeatureCollection(Land, serializeLandAsset),
      Improvement: mapFeatureCollection(Improvement, serializeImprovementAsset),
    },
  }
}

export type AssetsFeatureKey = keyof Assets['features']

export interface Edits<T> {
  add: Feature<Geometry, Partial<T>>[]
  edit: Feature<Geometry, Partial<T>>[]
  delete: string[]
}

export interface AssetsChanges {
  Land?: Edits<LandAsset>
  Improvement?: Edits<ImprovementAsset>
}

export const createEmptyAssetsObj = (): Assets => {
  return {
    features: {
      Land: emptyFeatureCollection<Geometry, LandAsset>(),
      Improvement: emptyFeatureCollection<Geometry, ImprovementAsset>(),
    },
    valuationSummary: undefined,
    valuationDescriptions: {},
    highestAndBestUseRemainingMetrics: {},
  }
}

export const hasAssetsAssigned = (assets: Assets): boolean => {
  return (
    assets.features.Improvement.features.length > 0 ||
    assets.features.Land.features.length > 0 ||
    Object.values(assets.highestAndBestUseRemainingMetrics).length > 0
  )
}

export type AssetsEdits = {
  highestAndBestUseRemainingMetrics: HighestAndBestUseRemainingMetric[]
  valuationDescriptions?: ValuationDescription[]
  assets: {
    Land: Partial<LandAsset>[]
    Improvement: Partial<ImprovementAsset>[]
  }
}

export function deserializeHighestAndBestUseSummary(
  hbsSummary: SerializableHighestAndBestUseSummary
) {
  const {
    AEPByEffectiveHectare,
    AEPByImprovementsValue,
    LWBByEffectiveHectare,
    LWBByTotalHectare,
    LWBBy_AEP,
    improvementsAreaM2,
    improvementsMarketValue,
    improvementsPercentageOfMarketValue,
    marketValue,
    marketValueByEffectiveHectare,
    marketValueByTotalHectare,
    marketValueBy_AEP,

    pvRatio,
    totalFarmIncome,

    totalHectares,
    totalEffectiveHectares,
    totalIneffectiveHectares,
    totalNonTitledHectares,
    totalNonTitledEffectiveHectares,
    totalNonTitledIneffectiveHectares,
    total_AEP,
    total_LWB,
    totalCanopyHectares,
    totalCanopyValue,
    nonCanopyValue,
    canopyValueByTotalHectare,
    canopyValueByCanopyHectare,
    orchardValueByCanopyHectare,
    AEPByCanopyHectare,
    canopyValueBy_AEP,
    ...rest
  } = hbsSummary
  return {
    ...rest,
    ...toBigNumber({
      AEPByEffectiveHectare,
      AEPByImprovementsValue,
      LWBByEffectiveHectare,
      LWBByTotalHectare,
      LWBBy_AEP,
      improvementsAreaM2,
      improvementsMarketValue,
      improvementsPercentageOfMarketValue,
      marketValue,
      marketValueByEffectiveHectare,
      marketValueByTotalHectare,
      marketValueBy_AEP,

      pvRatio,
      totalFarmIncome,

      totalEffectiveHectares,
      totalHectares,
      totalIneffectiveHectares,
      totalNonTitledHectares,
      totalNonTitledEffectiveHectares,
      totalNonTitledIneffectiveHectares,
      total_AEP,
      total_LWB,
      totalCanopyHectares,
      totalCanopyValue,
      nonCanopyValue,
      canopyValueByTotalHectare,
      canopyValueByCanopyHectare,
      orchardValueByCanopyHectare,
      AEPByCanopyHectare,
      canopyValueBy_AEP,
    }),
  }
}

export type HighestAndBestUseSummary = ReturnType<
  typeof deserializeHighestAndBestUseSummary
>

export function serializeHighestAndBestUseSummary(
  hbsSummary: HighestAndBestUseSummary
): SerializableHighestAndBestUseSummary {
  const {
    AEPByEffectiveHectare,
    AEPByImprovementsValue,
    LWBByEffectiveHectare,
    LWBByTotalHectare,
    LWBBy_AEP,
    improvementsAreaM2,
    improvementsMarketValue,
    improvementsPercentageOfMarketValue,
    marketValue,
    marketValueByEffectiveHectare,
    marketValueByTotalHectare,
    marketValueBy_AEP,

    pvRatio,
    totalFarmIncome,

    totalHectares,
    totalEffectiveHectares,
    totalIneffectiveHectares,
    totalNonTitledHectares,
    totalNonTitledEffectiveHectares,
    totalNonTitledIneffectiveHectares,
    total_AEP,
    total_LWB,
    totalCanopyHectares,
    totalCanopyValue,
    nonCanopyValue,
    canopyValueByTotalHectare,
    canopyValueByCanopyHectare,
    orchardValueByCanopyHectare,
    AEPByCanopyHectare,
    canopyValueBy_AEP,
    ...rest
  } = hbsSummary
  return {
    ...rest,
    ...fromBigNumber({
      AEPByEffectiveHectare,
      AEPByImprovementsValue,
      LWBByEffectiveHectare,
      LWBByTotalHectare,
      LWBBy_AEP,
      improvementsAreaM2,
      improvementsMarketValue,
      improvementsPercentageOfMarketValue,
      marketValue,
      marketValueByEffectiveHectare,
      marketValueByTotalHectare,
      marketValueBy_AEP,

      pvRatio,
      totalFarmIncome,

      totalHectares,
      totalEffectiveHectares,
      totalIneffectiveHectares,
      totalNonTitledHectares,
      totalNonTitledEffectiveHectares,
      totalNonTitledIneffectiveHectares,
      total_AEP,
      total_LWB,
      totalCanopyHectares,
      totalCanopyValue,
      nonCanopyValue,
      canopyValueByTotalHectare,
      canopyValueByCanopyHectare,
      orchardValueByCanopyHectare,
      AEPByCanopyHectare,
      canopyValueBy_AEP,
    }),
  }
}

export function deserializeValuationSummary(
  valuationSummary: SerializableValuationSummary
) {
  const {
    highestAndBestUseSummary,

    improvementsAreaM2,
    improvementsMarketValue,
    marketValue,
    pvRatio,
    totalHectares,
    totalEffectiveHectares,
    totalIneffectiveHectares,
    totalNonTitledHectares,
    totalNonTitledEffectiveHectares,
    totalNonTitledIneffectiveHectares,
    totalUnallocatedHectares,
    total_LWB,

    totalAdjustmentPercent,
    lwbAdjustmentPercent,
    ...rest
  } = valuationSummary
  return {
    ...rest,
    highestAndBestUseSummary: highestAndBestUseSummary.map(
      deserializeHighestAndBestUseSummary
    ),
    ...toBigNumber({
      improvementsAreaM2,
      improvementsMarketValue,
      marketValue,
      pvRatio,
      totalHectares,
      totalEffectiveHectares,
      totalIneffectiveHectares,
      totalNonTitledHectares,
      totalNonTitledEffectiveHectares,
      totalNonTitledIneffectiveHectares,
      totalUnallocatedHectares,
      total_LWB,

      totalAdjustmentPercent,
      lwbAdjustmentPercent,
    }),
  }
}

export type ValuationSummary = ReturnType<typeof deserializeValuationSummary>

export function serializeValuationSummary(valuationSummary: ValuationSummary) {
  const {
    highestAndBestUseSummary,

    improvementsAreaM2,
    improvementsMarketValue,
    marketValue,
    pvRatio,
    totalHectares,
    totalEffectiveHectares,
    totalIneffectiveHectares,
    totalNonTitledHectares,
    totalNonTitledEffectiveHectares,
    totalNonTitledIneffectiveHectares,
    totalUnallocatedHectares,
    total_LWB,

    totalAdjustmentPercent,
    lwbAdjustmentPercent,
    ...rest
  } = valuationSummary
  return {
    ...rest,
    highestAndBestUseSummary: highestAndBestUseSummary.map(
      serializeHighestAndBestUseSummary
    ),
    ...fromBigNumber({
      improvementsAreaM2,
      improvementsMarketValue,
      marketValue,
      pvRatio,
      totalHectares,
      totalEffectiveHectares,
      totalIneffectiveHectares,
      totalUnallocatedHectares,
      totalNonTitledHectares,
      totalNonTitledEffectiveHectares,
      totalNonTitledIneffectiveHectares,
      total_LWB,

      totalAdjustmentPercent,
      lwbAdjustmentPercent,
    }),
  }
}

export const recalculateImprovementsSummaryValues = (
  improvementAssets: ImprovementAsset[]
) => {
  return {
    improvementsMarketValue: improvementAssets.reduce(
      (accum, improvementAsset) => accum.plus(improvementAsset.value ?? ZERO),
      ZERO
    ),
    improvementsAreaM2: improvementAssets.reduce(
      (accum, improvementAsset) =>
        accum.plus(new BN(improvementAsset.area ?? 0)),
      ZERO
    ),
  }
}

export const recalculateHighestBestUseSummary = (
  bestUse: HighestAndBestUseType,
  assetMetrics: LandAssetMetric[],
  bestUseRemainingMetric: HighestAndBestUseRemainingMetric,
  improvementAssets: ImprovementAsset[]
): HighestAndBestUseSummary => {
  let total_AEP = assetMetrics.reduce(
    (accum, landAssetMetric: LandAssetMetric) =>
      accum.plus(landAssetMetric.total_AEP),
    ZERO
  )

  let total_LWB = assetMetrics.reduce(
    (accum, landAssetMetric: LandAssetMetric) =>
      accum.plus(landAssetMetric.total_LWB),
    ZERO
  )

  const {
    improvementsMarketValue: initialImprovementsMarketValue,
    improvementsAreaM2,
  } = recalculateImprovementsSummaryValues(improvementAssets)
  let improvementsMarketValue = initialImprovementsMarketValue

  let marketValue = total_LWB.plus(improvementsMarketValue)

  total_AEP = roundToNearestBN(total_AEP, bestUse.aepRounding)
  total_LWB = roundToNearestBN(total_LWB, 5000)
  improvementsMarketValue = roundToNearestBN(improvementsMarketValue, 1000)
  marketValue = total_LWB.plus(improvementsMarketValue)

  const totalArea = assetMetrics.reduce(
    (accum: BigNumber, landAssetMetric: LandAssetMetric) =>
      accum.plus(landAssetMetric.totalArea),
    ZERO
  )

  const isNonTitledLand = (assetMetric: LandAssetMetric) =>
    !!assetMetric.nonTitledLandType

  const totalNonTitledArea = assetMetrics
    .filter(isNonTitledLand)
    .reduce(
      (accum: BigNumber, landAssetMetric: LandAssetMetric) =>
        accum.plus(landAssetMetric.totalArea),
      ZERO
    )

  const totalEffectiveArea = assetMetrics.reduce(
    (accum: BigNumber, landAssetMetric: LandAssetMetric) =>
      landAssetMetric.isProductive
        ? accum.plus(landAssetMetric.totalArea)
        : accum,
    ZERO
  )

  const totalNonTitledEffectiveArea = assetMetrics
    .filter(isNonTitledLand)
    .reduce(
      (accum: BigNumber, landAssetMetric: LandAssetMetric) =>
        landAssetMetric.isProductive
          ? accum.plus(landAssetMetric.totalArea)
          : accum,
      ZERO
    )

  const totalIneffectiveArea = totalArea.minus(totalEffectiveArea)
  const totalNonTitledIneffectiveArea = totalNonTitledArea.minus(
    totalNonTitledEffectiveArea
  )

  const totalHectares = totalArea.div(10000)
  const totalEffectiveHectares = totalEffectiveArea.div(10000)
  const totalIneffectiveHectares = totalIneffectiveArea.div(10000)

  const totalNonTitledHectares = totalNonTitledArea.div(10000)
  const totalNonTitledEffectiveHectares = totalNonTitledEffectiveArea.div(10000)
  const totalNonTitledIneffectiveHectares =
    totalNonTitledIneffectiveArea.div(10000)

  const AEPByEffectiveHectare = total_AEP.div(totalEffectiveHectares)
  const LWBByEffectiveHectare = total_LWB.div(totalEffectiveHectares)
  const LWBByTotalHectare = total_LWB.div(totalHectares)

  const totalFarmIncome = total_AEP.multipliedBy(
    bestUseRemainingMetric?.commodityPrice ?? ZERO
  )
  let pvRatio = ZERO
  if (bestUse.categories.indexOf('Horticulture & Viticulture') !== -1) {
    pvRatio = totalFarmIncome.gt(0) ? total_LWB.div(totalFarmIncome) : ZERO
  } else {
    pvRatio = totalFarmIncome.gt(0) ? marketValue.div(totalFarmIncome) : ZERO
  }

  const marketValueBy_AEP = total_AEP.gt(0) ? marketValue.div(total_AEP) : ZERO
  const LWBBy_AEP = total_LWB.gt(0) ? total_LWB.div(total_AEP) : ZERO
  const AEPByImprovementsValue = total_AEP.gt(0)
    ? total_AEP.div(improvementsMarketValue)
    : ZERO

  const improvementsPercentageOfMarketValue = marketValue.gt(0)
    ? improvementsMarketValue.div(marketValue)
    : ZERO

  const marketValueByEffectiveHectare = marketValue.div(totalEffectiveHectares)
  const marketValueByTotalHectare = marketValue.div(totalHectares)

  const totalCanopyArea = assetMetrics.reduce(
    (accum: BigNumber, landAssetMetric: LandAssetMetric) =>
      landAssetMetric.isProductive && landAssetMetric.isCanopy
        ? accum.plus(landAssetMetric.totalArea)
        : accum,
    ZERO
  )

  const totalCanopyHectares = totalCanopyArea.div(10000)

  let totalCanopyValue = assetMetrics.reduce(
    (accum, landAssetMetric: LandAssetMetric) =>
      landAssetMetric.isProductive && landAssetMetric.isCanopy
        ? accum.plus(landAssetMetric.total_LWB)
        : accum,
    ZERO
  )

  // total effective canopy + value of headlands
  let totalOrchardValue = totalCanopyValue.plus(
    assetMetrics.reduce(
      (accum, landAssetMetric: LandAssetMetric) =>
        landAssetMetric.landClassName === 'Headlands/Fallow'
          ? accum.plus(landAssetMetric.total_LWB)
          : accum,
      ZERO
    )
  )

  totalCanopyValue = roundToNearestBN(totalCanopyValue, 5000)
  totalOrchardValue = roundToNearestBN(totalOrchardValue, 5000)

  const canopyValueByTotalHectare = totalCanopyValue.div(totalHectares)
  const canopyValueByCanopyHectare = totalCanopyValue.div(totalCanopyHectares)
  const orchardValueByCanopyHectare = totalOrchardValue.div(totalCanopyHectares)
  const AEPByCanopyHectare = total_AEP.div(totalCanopyHectares)
  const canopyValueBy_AEP = total_AEP.gt(0)
    ? totalCanopyValue.div(total_AEP)
    : ZERO

  const nonCanopyValue = total_LWB
    .minus(totalCanopyValue)
    .plus(improvementsMarketValue)

  const bestUseSummary: HighestAndBestUseSummary = {
    id: -1,

    valuationSummary: -1,
    highestAndBestUseType: bestUse.id,
    total_AEP,
    total_LWB,
    totalHectares,
    totalEffectiveHectares,
    totalIneffectiveHectares,
    totalNonTitledHectares,
    totalNonTitledEffectiveHectares,
    totalNonTitledIneffectiveHectares,
    improvementsAreaM2,
    improvementsMarketValue,
    AEPByEffectiveHectare,
    AEPByImprovementsValue,
    LWBByEffectiveHectare,
    LWBByTotalHectare,
    LWBBy_AEP,
    improvementsPercentageOfMarketValue,
    marketValue,
    marketValueByEffectiveHectare,
    marketValueByTotalHectare,
    marketValueBy_AEP,
    pvRatio,
    totalFarmIncome,
    totalCanopyHectares,
    totalCanopyValue,
    nonCanopyValue,
    canopyValueByCanopyHectare,
    orchardValueByCanopyHectare,
    canopyValueByTotalHectare,
    AEPByCanopyHectare,
    canopyValueBy_AEP,
  }

  return bestUseSummary
}

export const recalculateValuationSummary = (
  originalSummary: ValuationSummary,
  assets: Omit<Assets, 'valuationSummary'>,
  defaultHighestAndBestUseType: HighestAndBestUseType,
  valuationTypes: HighestAndBestUseTypeMap | undefined
): ValuationSummary => {
  const bestUseAssetMetrics: { [bestUseId: string]: LandAssetMetric[] } = {}
  const bestUseImprovementAssets: {
    [bestUseId: string]: ImprovementAsset[]
  } = {}

  const bestUses: { [bestUseId: string]: HighestAndBestUseType } = {}

  // const highestAndBestUseRemainingMetric = assets.highestAndBestUseRemainingMetrics[highestAndBestUse.id];

  for (const landAssetFeature of assets.features.Land.features) {
    const landAsset = landAssetFeature.properties

    const highestAndBestUse =
      valuationTypes?.[landAsset.secondaryHighestAndBestUseType ?? -1] ??
      defaultHighestAndBestUseType

    bestUses[highestAndBestUse.id] = highestAndBestUse

    if (bestUseAssetMetrics[highestAndBestUse.id] === undefined) {
      bestUseAssetMetrics[highestAndBestUse.id] = []
    }

    for (const assetMetric of landAsset.metrics) {
      bestUseAssetMetrics[highestAndBestUse.id].push(assetMetric)
    }
  }

  for (const improvementAsset of assets.features.Improvement.features) {
    const highestAndBestUseId =
      improvementAsset.properties.secondaryHighestAndBestUseType ??
      defaultHighestAndBestUseType.id
    if (bestUseImprovementAssets[highestAndBestUseId] === undefined) {
      bestUseImprovementAssets[highestAndBestUseId] = []
    }

    bestUseImprovementAssets[highestAndBestUseId].push(
      improvementAsset.properties
    )
  }

  const bestUseSummaries = []

  const bestUseArray = [
    defaultHighestAndBestUseType,
    ...Object.values(bestUses).filter(
      (bestUseType) => bestUseType.id !== defaultHighestAndBestUseType.id
    ),
  ]

  for (const bestUse of bestUseArray) {
    const assetMetrics = bestUseAssetMetrics[bestUse.id] ?? []
    const improvementAssets = bestUseImprovementAssets[bestUse.id] ?? []
    const bestUseSummary = recalculateHighestBestUseSummary(
      bestUse,
      assetMetrics,
      assets.highestAndBestUseRemainingMetrics[bestUse.id],
      improvementAssets
    )
    bestUseSummaries.push(bestUseSummary)
  }

  const totalEffectiveHectares = bestUseSummaries.reduce(
    (accum, bestUseSummary) =>
      accum.plus(bestUseSummary.totalEffectiveHectares),
    ZERO
  )
  const totalIneffectiveHectares = bestUseSummaries.reduce(
    (accum, bestUseSummary) =>
      accum.plus(bestUseSummary.totalIneffectiveHectares),
    ZERO
  )
  const totalNonTitledEffectiveHectares = bestUseSummaries.reduce(
    (accum, bestUseSummary) =>
      accum.plus(bestUseSummary.totalNonTitledEffectiveHectares),
    ZERO
  )
  const totalNonTitledIneffectiveHectares = bestUseSummaries.reduce(
    (accum, bestUseSummary) =>
      accum.plus(bestUseSummary.totalNonTitledIneffectiveHectares),
    ZERO
  )

  const valuationSummary = {
    ...originalSummary,
    highestAndBestUseSummary: bestUseSummaries,

    improvementsAreaM2: bestUseSummaries.reduce(
      (accum, bestUseSummary) => accum.plus(bestUseSummary.improvementsAreaM2),
      ZERO
    ),
    improvementsMarketValue: bestUseSummaries.reduce(
      (accum, bestUseSummary) =>
        accum.plus(bestUseSummary.improvementsMarketValue),
      ZERO
    ),
    marketValue: bestUseSummaries.reduce(
      (accum, bestUseSummary) => accum.plus(bestUseSummary.marketValue),
      ZERO
    ),
    pvRatio: bestUseSummaries.reduce(
      (accum, bestUseSummary) => accum.plus(bestUseSummary.pvRatio),
      ZERO
    ), // .div(bestUseSummaries.length),
    totalEffectiveHectares: totalEffectiveHectares,
    totalIneffectiveHectares: totalIneffectiveHectares,
    totalHectares: totalEffectiveHectares.plus(totalIneffectiveHectares),
    totalNonTitledEffectiveHectares: totalNonTitledEffectiveHectares,
    totalNonTitledIneffectiveHectares: totalNonTitledIneffectiveHectares,
    totalNonTitledHectares: totalNonTitledEffectiveHectares.plus(
      totalNonTitledIneffectiveHectares
    ),
    // totalUnallocatedHectares: , (doesnt change)
    total_LWB: bestUseSummaries.reduce(
      (accum, bestUseSummary) => accum.plus(bestUseSummary.total_LWB),
      ZERO
    ),
  }

  return valuationSummary
}

export const applyAdjustment = (
  value: BigNumber,
  adjustmentPercent: BigNumber
) => {
  return value.multipliedBy(adjustmentPercent.multipliedBy(-1).div(100).plus(1))
}

const applyAdjustments = (
  unadjustedSummary: HighestAndBestUseSummary,
  subjectSummary: HighestAndBestUseSummary,
  valuationAdjustments: ValuationAdjustment
): HighestAndBestUseSummary => {
  const adjustedSummary = { ...unadjustedSummary }
  adjustedSummary.improvementsMarketValue = applyAdjustment(
    adjustedSummary.improvementsMarketValue,
    valuationAdjustments.totalAdjustmentPercent
  )
  adjustedSummary.marketValue = applyAdjustment(
    adjustedSummary.marketValue,
    valuationAdjustments.totalAdjustmentPercent
  )
  adjustedSummary.marketValueByEffectiveHectare = applyAdjustment(
    adjustedSummary.marketValueByEffectiveHectare,
    valuationAdjustments.totalAdjustmentPercent
  )
  adjustedSummary.marketValueByTotalHectare = applyAdjustment(
    adjustedSummary.marketValueByTotalHectare,
    valuationAdjustments.totalAdjustmentPercent
  )

  adjustedSummary.total_LWB = applyAdjustment(
    adjustedSummary.total_LWB,
    valuationAdjustments.lwbAdjustmentPercent
  )
  adjustedSummary.LWBByEffectiveHectare = applyAdjustment(
    adjustedSummary.LWBByEffectiveHectare,
    valuationAdjustments.lwbAdjustmentPercent
  )
  adjustedSummary.LWBByTotalHectare = applyAdjustment(
    adjustedSummary.LWBByTotalHectare,
    valuationAdjustments.lwbAdjustmentPercent
  )

  adjustedSummary.LWBBy_AEP = adjustedSummary.LWBByEffectiveHectare.div(
    subjectSummary.AEPByEffectiveHectare
  )
  adjustedSummary.marketValueBy_AEP =
    adjustedSummary.marketValueByEffectiveHectare.div(
      subjectSummary.AEPByEffectiveHectare
    )

  adjustedSummary.AEPByImprovementsValue = adjustedSummary.total_AEP.div(
    adjustedSummary.improvementsMarketValue
  )

  if (subjectSummary.totalFarmIncome.gt(0)) {
    adjustedSummary.pvRatio = adjustedSummary.marketValueByEffectiveHectare
      .multipliedBy(subjectSummary.totalEffectiveHectares)
      .div(subjectSummary.totalFarmIncome)
  } else {
    adjustedSummary.pvRatio = ZERO
  }

  adjustedSummary.totalCanopyValue = applyAdjustment(
    adjustedSummary.totalCanopyValue,
    valuationAdjustments.lwbAdjustmentPercent
  )
  adjustedSummary.nonCanopyValue = applyAdjustment(
    adjustedSummary.nonCanopyValue,
    valuationAdjustments.totalAdjustmentPercent
  )
  adjustedSummary.canopyValueByTotalHectare = applyAdjustment(
    adjustedSummary.canopyValueByTotalHectare,
    valuationAdjustments.lwbAdjustmentPercent
  )
  adjustedSummary.canopyValueByCanopyHectare = applyAdjustment(
    adjustedSummary.canopyValueByCanopyHectare,
    valuationAdjustments.lwbAdjustmentPercent
  )
  adjustedSummary.canopyValueBy_AEP =
    adjustedSummary.canopyValueByCanopyHectare.div(
      subjectSummary.AEPByCanopyHectare
    )

  return adjustedSummary
}

export const calculateAdjustedValuationSummary = (
  unadjustedValuationSummary: ValuationSummary,
  subjectValuationSummary: ValuationSummary,
  valuationAdjustments: ValuationAdjustment,
  subjectHighestAndBestUseType: number,
  saleHighestAndBestUseType: number
) => {
  // TODO: need to know what H&BS is being adjusted to what
  const adjustedBestUseSummaries = []
  for (const bestUseSummary of unadjustedValuationSummary.highestAndBestUseSummary) {
    if (bestUseSummary.highestAndBestUseType !== saleHighestAndBestUseType) {
      continue
    }

    const subjectBestUseSummary =
      subjectValuationSummary.highestAndBestUseSummary.find(
        (hbsSummary) =>
          hbsSummary.highestAndBestUseType === subjectHighestAndBestUseType
      )

    if (!subjectBestUseSummary) {
      continue
    }
    const adjustedSummary = applyAdjustments(
      bestUseSummary,
      subjectBestUseSummary,
      valuationAdjustments
    )
    adjustedBestUseSummaries.push(adjustedSummary)
  }

  return {
    ...unadjustedValuationSummary,
    highestAndBestUseSummary: adjustedBestUseSummaries,
    improvementsMarketValue: adjustedBestUseSummaries.reduce(
      (accum, summary) => accum.plus(summary.improvementsMarketValue),
      ZERO
    ),
    marketValue: adjustedBestUseSummaries.reduce(
      (accum, summary) => accum.plus(summary.marketValue),
      ZERO
    ),
    pvRatio: adjustedBestUseSummaries
      .reduce((accum, summary) => accum.plus(summary.pvRatio), ZERO)
      .div(adjustedBestUseSummaries.length),
    total_LWB: adjustedBestUseSummaries.reduce(
      (accum, summary) => accum.plus(summary.total_LWB),
      ZERO
    ),

    totalAdjustmentPercent: valuationAdjustments.totalAdjustmentPercent,
    lwbAdjustmentPercent: valuationAdjustments.lwbAdjustmentPercent,
  }
}

const averageValues = (
  summaries: HighestAndBestUseSummary[],
  field: keyof HighestAndBestUseSummary
): BigNumber => {
  return summaries
    .reduce(
      (accum: BigNumber, summary) => accum.plus(summary[field] as BigNumber),
      ZERO
    )
    .div(summaries.length)
}

export const averageBestUseSummaries = (
  summaries: HighestAndBestUseSummary[]
): (HighestAndBestUseSummary & { bestUses: number[] }) | undefined => {
  if (summaries.length === 0) {
    return undefined
  }

  const marketValue = averageValues(summaries, 'marketValue')
  const improvementsMarketValue = averageValues(
    summaries,
    'improvementsMarketValue'
  )
  const total_AEP = averageValues(summaries, 'total_AEP')
  const total_LWB = averageValues(summaries, 'total_LWB')

  return {
    id: -1,

    valuationSummary: -1,

    total_AEP: total_AEP,
    total_LWB: total_LWB,

    highestAndBestUseType: summaries[0].highestAndBestUseType,
    bestUses: summaries.map((summary) => summary.highestAndBestUseType),

    AEPByEffectiveHectare: averageValues(summaries, 'AEPByEffectiveHectare'),
    AEPByImprovementsValue: averageValues(summaries, 'AEPByImprovementsValue'),

    LWBByEffectiveHectare: averageValues(summaries, 'LWBByEffectiveHectare'),
    LWBByTotalHectare: averageValues(summaries, 'LWBByTotalHectare'),
    LWBBy_AEP: averageValues(summaries, 'LWBBy_AEP'),

    improvementsAreaM2: averageValues(summaries, 'improvementsAreaM2'),
    improvementsMarketValue: improvementsMarketValue,

    improvementsPercentageOfMarketValue: averageValues(
      summaries,
      'improvementsPercentageOfMarketValue'
    ),

    marketValue: marketValue,

    marketValueByEffectiveHectare: averageValues(
      summaries,
      'marketValueByEffectiveHectare'
    ),
    marketValueByTotalHectare: averageValues(
      summaries,
      'marketValueByTotalHectare'
    ),
    marketValueBy_AEP: averageValues(summaries, 'marketValueBy_AEP'),

    pvRatio: averageValues(summaries, 'pvRatio'),
    totalFarmIncome: averageValues(summaries, 'totalFarmIncome'),

    totalEffectiveHectares: averageValues(summaries, 'totalEffectiveHectares'),
    totalHectares: averageValues(summaries, 'totalHectares'),
    totalIneffectiveHectares: averageValues(
      summaries,
      'totalIneffectiveHectares'
    ),
    totalNonTitledHectares: averageValues(summaries, 'totalNonTitledHectares'),
    totalNonTitledEffectiveHectares: averageValues(
      summaries,
      'totalNonTitledEffectiveHectares'
    ),
    totalNonTitledIneffectiveHectares: averageValues(
      summaries,
      'totalNonTitledIneffectiveHectares'
    ),
    totalCanopyHectares: averageValues(summaries, 'totalCanopyHectares'),
    totalCanopyValue: averageValues(summaries, 'totalCanopyValue'),
    nonCanopyValue: averageValues(summaries, 'nonCanopyValue'),
    canopyValueByTotalHectare: averageValues(
      summaries,
      'canopyValueByTotalHectare'
    ),
    canopyValueByCanopyHectare: averageValues(
      summaries,
      'canopyValueByCanopyHectare'
    ),
    orchardValueByCanopyHectare: averageValues(
      summaries,
      'orchardValueByCanopyHectare'
    ),
    AEPByCanopyHectare: averageValues(summaries, 'AEPByCanopyHectare'),
    canopyValueBy_AEP: averageValues(summaries, 'canopyValueBy_AEP'),
  }
}
