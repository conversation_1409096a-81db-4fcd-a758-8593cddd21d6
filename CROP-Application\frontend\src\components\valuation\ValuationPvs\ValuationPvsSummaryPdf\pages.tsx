import { View } from '@react-pdf/renderer'
import * as L from 'leaflet'
import {
  PdfList,
  PdfMap,
  PdfParagraph,
  type PdfSectionDefinition,
  PdfText,
  SPACE,
  rem,
} from '@components/pdf'
import PdfTable from '@components/pdf/OldPdfTable'
import { waterSecurityPermitQuestion } from '@components/valuation/language'
import type {
  ValuationsPvsSummary,
  ValuationsSavedTitleFeature,
} from '@store/services/sdk'
import { truthy } from '@util/guards'
import { access, insertIf } from '@util/helpers'
import { formatAddress } from '@util/labels'
import type { FeatureCollectionFixup, FeatureFixup } from '@util/types'
import BestUseAssetSummaryTable from './BestUseAssetSummaryTable'
import BuildingImprovementsTable from './BuildingImprovementsTable'
import CombinedResourceConsentsTable from './CombinedResourceConsentsTable'
import { ComparableSalesTable } from './ComparableSalesTable'
import ComparativeSaleAdjustments from './ComparativeSaleAdjustments'
import DetailedComparativeSalesAnalysis from './DetailedComparativeSalesAnalysis'
import ExecutiveSummary from './ExecutiveSummary'
import FutureMarketValueTable from './FutureMarketValueTable'
import ImprovementsSummaryTable from './ImprovementsSummaryTable'
import Inspection from './Inspection'
import KeyMetricsTable from './KeyMetricsTable'
import KeySalesEvidenceTable from './KeySalesEvidenceTable'
import KiwifruitWorksheetSummary from './KiwifruitWorksheetSummary'
import LandApportionmentTable from './LandApportionmentTable'
import ValuationPvsLandClassBreakdown from './LandClassBreakdownTable'
import LandClassMap from './LandClassMap'
import LegalDescriptionTable from './LegalDescriptionTable'
import LocationTable from './LocationTable'
import LucMap from './LucMap'
import LucTable from './LucTable'
import MarketValueAssessmentSummaryTable from './MarketValueAssessmentSummaryTable'
import MarketValueAssessmentTable from './MarketValueAssessmentTable'
import MortgageValueApportionmentTable from './MortgageValueApportionment'
import PriorMarketValueTable from './PriorMarketValueTable'
import ProductionOverviewTable from './ProductionOverviewTable'
import PropertyOverviewTable from './PropertyOverviewTable'
import RatingValuationTable from './RatingValuationTable'
import RemarksAndActions from './RemarksAndActions'
import ReportTable from './ReportTable'
import TitleReviews from './TitleReviews'
import ValuationPvsAep from './ValuationPvsAep'
import { LegalDescription, MarketValueSummary } from './helpers'

const pvsPages = (valuation: ValuationsPvsSummary): PdfSectionDefinition[] => {
  const titleFeatureCollection =
    valuation.titles as FeatureCollectionFixup<ValuationsSavedTitleFeature>

  if (!valuation) return []

  const pages = [
    {
      title: 'Executive Summary',
      children: [
        {
          title: 'Reporting',
          content: (
            <ReportTable
              approach={valuation.approach}
              bestUse={valuation.highestAndBestUseName as string}
              inspectionDate={valuation.inspection?.formattedDateLong}
              reportDate={valuation.completedDateLong}
              valuationDate={valuation.createdDateLong}
            />
          ),
        },
        {
          title: 'Property Overview',
          content: (
            <>
              <ExecutiveSummary
                descriptions={valuation.descriptions}
                summary={valuation.summary}
                titles={valuation.titles}
              />
              <PropertyOverviewTable valuation={valuation} />
            </>
          ),
          break: true,
        },
        {
          title: 'Production Overview',
          content: <ProductionOverviewTable summary={valuation.summary} />,
          break: true,
        },
        ...insertIf(
          !!(
            (valuation.remarksAndActions &&
              valuation.remarksAndActionsReportInclude) ||
            valuation.titleReviewComments ||
            valuation.titleReviews.length ||
            valuation.waterSecurityReview
          ),
          {
            title:
              'Internal Assessment Limitations and Remarks For CAD Holders',
            content: <RemarksAndActions valuation={valuation} />,
          }
        ),
        {
          title: 'Land Apportionment',
          content: (
            <LandApportionmentTable
              highestAndBestUseSummary={
                valuation.summary.highestAndBestUseSummary
              }
            />
          ),
        },
        ...insertIf(!!valuation.improvements.length, {
          title: 'Improvements Summary',
          content: (
            <ImprovementsSummaryTable
              improvements={valuation.improvements}
              summary={valuation.summary}
            />
          ),
        }),
        {
          title: 'Market Value Assessment',
          content: (
            <>
              <MarketValueAssessmentSummaryTable summary={valuation.summary} />
              <FutureMarketValueTable
                projectedSummaries={valuation.projectedValuationSummaries}
              />
            </>
          ),
          break: true,
        },
        {
          title: 'Key Metrics',
          content: <KeyMetricsTable summary={valuation.summary} />,
        },
        {
          title: 'Key Sales Evidence',
          content: (
            <KeySalesEvidenceTable
              valuation={valuation}
              sales={valuation.comparableSales.features.map(
                access('properties')
              )}
            />
          ),
        },
      ],
    },
    {
      title: 'Introduction',
      children: [
        {
          title: 'Instructions',
          content: (
            <PdfParagraph>
              In accordance with the Agri Valuation Team Collateral Framework
              (effective 31 May 2022), we have provided an appraisal for the
              land and building improvements for{' '}
              {formatAddress(valuation.fullAddress)}. This assessment has been
              prepared for ANZ Bank New Zealand Limited to establish market
              value for the credit risk and mortgage and loan security purposes.
            </PdfParagraph>
          ),
        },
        {
          title: 'Property Inspection',
          content: <Inspection inspection={valuation.inspection} />,
        },
        {
          title: 'Special Assumptions & Limitations',
          content: (
            <View
              render={() => {
                return valuation.inspected ? (
                  <PdfParagraph>
                    We have assumed for purpose of this appraisal the property’s
                    current land use and/or alternative land use assessed
                    (applicable if the highest and best use differs from current
                    land use), and assessed average efficient production
                    level(s), are compliant with all Local/District, Regional
                    and Central Government Regulation(s). We reserve the right
                    to amend our assessment should additional information become
                    available.
                  </PdfParagraph>
                ) : (
                  <PdfParagraph>
                    We have not completed an internal inspection of the building
                    improvements as part of this appraisal. Our appraisal does
                    not comprise a structural / geotechnical assessment or
                    asbestos report, therefore, we cannot guarantee the
                    accuracy, integrity or condition of the building
                    improvements. We reserve the right to amend our assessment
                    should additional information become available.
                  </PdfParagraph>
                )
              }}
            />
          ),
        },
      ],
    },
    {
      title: 'Market Value Appraisal',
      children: [
        {
          /* eslint-disable @typescript-eslint/no-non-null-assertion */
          content: (
            <>
              <PdfParagraph>
                Based on the available information, the market value of the
                subject property, as at {valuation.createdDateLong}, has been
                assessed as follows:
              </PdfParagraph>
              <MarketValueSummary summary={valuation.summary} />
              <MarketValueAssessmentTable summary={valuation.summary} />
              <FutureMarketValueTable
                projectedSummaries={valuation.projectedValuationSummaries}
              />
              <PdfParagraph>
                Includes the value of any irrigation shares and resource
                consents (if any), excludes the value of any woodlots,
                shelterbelts, plant and equipment, and chattels.
              </PdfParagraph>
              <PdfParagraph style={{ marginTop: rem(2) }}>
                Detailed valuation worksheet(s) can be found in document
                appendix.
              </PdfParagraph>
            </>
          ),
          /* eslint-enable @typescript-eslint/no-non-null-assertion */
        },
        ...insertIf(!!valuation.priorMarketValue, {
          title: 'Prior Market Value',
          children: [
            {
              content: (
                <PriorMarketValueTable
                  priorMarketValue={valuation.priorMarketValue}
                />
              ),
            },
          ],
        }),
        {
          title: 'Mortgage Value Apportionment',
          children: [
            {
              title: '',
              content: (
                <>
                  <MortgageValueApportionmentTable
                    apportionments={valuation.titleApportionment}
                  />
                  <PdfParagraph>
                    This Mortgage Value Apportionment is a pro-rata
                    apportionment based on a build up of what exists ($ Value of
                    land and buildings) on each title. Each title is then summed
                    together based on the each titles' mortgage number. In
                    effect this provides the depicted Market Value on a mortgage
                    basis. This however, doesn't represent what each title could
                    or would sell for under each mortgage/instrument number if
                    sold independently of each other.
                  </PdfParagraph>
                </>
              ),
            },
          ],
        },
      ],
    },
    {
      title: 'Valuation Methodology & Considerations',
      children: [
        {
          title: 'Basis of Value',
          content: (
            <>
              <PdfParagraph>
                This assessment has been prepared to establish market value. We
                have adopted the International Valuation Standards (IVS)
                definition of market value:
              </PdfParagraph>
              <PdfParagraph>
                “market value is the estimated amount for which an asset or
                liability should exchange on the valuation date between a
                willing buyer and a willing seller in an arm's length
                transaction, after proper marketing and where the parties had
                each acted knowledgeably, prudently and without compulsion”.
              </PdfParagraph>
            </>
          ),
        },
        {
          title: 'Highest and Best Use',
          content: (
            <>
              <PdfParagraph>
                To determine market value, the appraiser will consider the
                ‘highest and best use’ of the property. ‘Highest and best use’
                involves the consideration of the following:
              </PdfParagraph>
              <PdfParagraph quote>
                “To establish whether a use is possible, regard will be had to
                what would be considered reasonable by market participants, to
                reflect the requirement to be legally permissible, any legal
                restrictions on the use of the asset, e.g. zoning designations,
                need to be taken into account, the requirement that the use be
                financially feasible takes into account whether an alternative
                use that is physically possible and legally permissible will
                generate sufficient return to a typical market participant,
                after taking into account the cost of conversion to their use,
                over and above the return on the existing use”.
              </PdfParagraph>
              <PdfParagraph>
                We determine the highest and best use of the subject property to
                be {valuation.highestAndBestUseCategory}.
              </PdfParagraph>
            </>
          ),
        },
        {
          title: 'Approach & Application of Valuation Methodology',
          content: (
            <PdfParagraph>
              We consider the most appropriate approach for valuing the subject
              property to be the Market Approach.
            </PdfParagraph>
          ),
          children: [
            {
              title: 'Market Approach',
              content: (
                <>
                  <PdfParagraph>
                    This approach involves the analysis of available evidence,
                    including property sales to determine appropriate comparable
                    rates to apply to the subject property. Utilising the
                    summation method, the estimated value of the land and the
                    added value of the structural improvements are summed to
                    derive an overall market value for the property, which is
                    then checked with reference to the market evidence.
                  </PdfParagraph>
                  <PdfParagraph>
                    The IVS define the Market Approach as:
                  </PdfParagraph>
                  <PdfParagraph>
                    “The market approach provides an indication of value by
                    comparing the asset with identical or comparable (that is
                    similar) assets for which price information is available”.
                  </PdfParagraph>
                </>
              ),
            },
          ],
        },
        {
          title: 'Information Sources',
          content: (
            <>
              <PdfParagraph>
                Our appraisal conclusions have been reached after reviewing
                data, documentation and information supplied by the customer,
                the Bank and/or another third party. The information reviewed
                and supplied may include, although not limited to, the
                following:
              </PdfParagraph>
              <PdfList
                items={[
                  'Farm Plans',
                  'Title Searches',
                  'Local Government',
                  'Landcare Research S-Maps Soil Series',
                  'Valocity Sales Data',
                  'Climate data',
                  'Customer Information either provided or gathered from ANZ databases',
                ]}
              />
            </>
          ),
        },
      ],
    },
    {
      title: 'Legal Description & Tenure',
      children: [
        {
          title: 'Legal Description',
          content: (
            <>
              <LegalDescription valuation={valuation} />
              <LegalDescriptionTable
                titles={valuation.titles}
                titleArea={valuation.summary.totalHectares}
              />
              <PdfMap
                geoJson={valuation.titles}
                featureOptions={{
                  onEachFeature(f: ValuationsSavedTitleFeature, layer) {
                    layer.bindTooltip(`${f.properties.titleNo}`, {
                      permanent: true,
                      className: 'pdfTooltip',
                    })
                  },
                  pointToLayer(_, latlng) {
                    return L.marker(latlng, { opacity: 0 })
                  },
                }}
                style={{ paddingVertical: rem(2) }}
              />
            </>
          ),
        },
        {
          // TODO: concerns
          title: 'Easements, Encumbrances & Interests',
          content: (
            <>
              <PdfParagraph>
                The Records of Title are subject to the following interests:
              </PdfParagraph>
              <PdfTable
                columns={[
                  {
                    key: 'record',
                    title: 'Record of Title',
                    isHeader: true,
                    weighting: 1,
                  },
                  {
                    key: 'memorials',
                    title: '',
                    weighting: 4,
                  },
                ]}
                rows={titleFeatureCollection.features.map(({ properties }) => ({
                  record: properties.titleNo,
                  memorials: (
                    <View>
                      <PdfList
                        size="s"
                        isNested
                        items={(properties.memorials ?? [])
                          .filter(({ memorialText }) => truthy(memorialText))
                          .map(({ memorialText }) => memorialText ?? '')}
                      />
                    </View>
                  ),
                }))}
              />

              <View>
                {valuation.titleReviewComments ||
                valuation.titleReviews.length ? (
                  <>
                    <PdfParagraph>
                      We have reviewed all the above interests have identified
                      the following concerns:
                    </PdfParagraph>
                    <PdfText>{valuation.titleReviewComments}</PdfText>
                    <TitleReviews reviews={valuation.titleReviews} />
                  </>
                ) : (
                  <PdfParagraph>
                    The encumbrances identified above are not considered to have
                    a material impact on the marketability and/or the market
                    value appraised herein.
                  </PdfParagraph>
                )}
              </View>
            </>
          ),
          break: true,
        },
      ],
    },
    {
      title: 'Zoning & Resource Management',
      children: [
        {
          title: 'District Planning',
          content: (
            <PdfParagraph>{valuation.descriptions?.dcCompliance}</PdfParagraph>
          ),
        },
        {
          title: 'Rating Valuation Details',
          content: valuation.titleMatchingRatingValuation ? (
            <RatingValuationTable
              ratingValuation={valuation.titleMatchingRatingValuation}
            />
          ) : (
            <PdfParagraph>
              The rating valuation details are not available for the subject
              property, due to being assessed as part of a larger rating unit.
              Separate rating assessments are not available at this time.
            </PdfParagraph>
          ),
        },
        {
          title: 'Resource Consents',
          content: (
            <CombinedResourceConsentsTable
              consents={valuation.combinedResourceConsents}
              // TODO: Resource consent concerns
            />
          ),
        },
      ],
    },
    {
      title: 'Environmental Management and Compliance',
      children: [
        ...insertIf(!!valuation.environmentalComplianceDescription, {
          title: 'Compliance Summary',
          content: (
            <PdfParagraph>
              {valuation.environmentalComplianceDescription}
            </PdfParagraph>
          ),
        }),
        {
          title: 'National Policy Statement for Freshwater Management 2020',
          content: (
            <PdfParagraph>
              The National Policy Statement for Freshwater Management 2020
              (NPS-FM 2020) provides local authorities with updated direction on
              how they should manage freshwater under the Resource Management
              Act 1991. The change introduced new rules and regulation to
              restore and protect the health of New Zealand’s waterways.
            </PdfParagraph>
          ),
        },
        {
          title: 'National Environmental Standards for Freshwater 2020',
          content: (
            <>
              <PdfParagraph>
                The National Environmental Standards for Freshwater 2020 (NES-F
                2020), as amended by the Resource Management (National
                Environmental Standards for Freshwater) Amendment Regulations
                2022, set out standards for farming activities and other
                activities that relate to freshwater.
              </PdfParagraph>
              <PdfParagraph>
                Farming activities captured in the standards include feedlots,
                stockholding areas, conversion of or irrigation of dairy farm
                land, use of dairy support land, intensive winter grazing, and
                the use of synthetic nitrogen fertilisers. These activities are
                permitted activities only where the conditions can be met,
                otherwise resource consent is required.
              </PdfParagraph>
            </>
          ),
        },
        {
          title: 'Resource Management (Stock Exclusion) Regulations 2020',
          content: (
            <>
              <PdfParagraph>
                The stock exclusion regulations prohibit the access of stock to
                wetlands, lakes, and rivers. They came into force 3 September
                2020.
              </PdfParagraph>
            </>
          ),
        },
      ],
    },
    {
      title: 'Property Details',
      children: [
        { title: 'Location', content: <LocationTable valuation={valuation} /> },
        {
          title: 'Land Use Classification (LUC)',
          content: (
            <>
              <PdfParagraph>
                The land use capability system as used in New Zealand has eight
                LUC classes. Classes 1 to 4 are classified as potentially arable
                land, while LUC classes 5 to 8 are potentially non-arable. It
                can tell us more about the land and provides a simple assessment
                regarding the potential land suitability for sustainable use.
              </PdfParagraph>
              <LucTable
                union={valuation.anzUnion}
                totalHectares={valuation.totalHectares}
              />
              <View style={{ marginBottom: SPACE[5] }} />
              <LucMap union={valuation.anzUnion} />
            </>
          ),
        },
        {
          title: 'Land Class Breakdown',
          content: (
            <>
              <ValuationPvsLandClassBreakdown valuation={valuation} />
              <LandClassMap landAssets={valuation.landAssets} />
            </>
          ),
        },
        ...insertIf(!!valuation.irrigationDescription, {
          title: 'Irrigation Water Resource & Adequacy',
          content: (
            <>
              <PdfText>{valuation.irrigationDescription}</PdfText>
              {!!valuation.waterSecurityReviewComments && (
                <>
                  <PdfParagraph>{waterSecurityPermitQuestion}</PdfParagraph>
                  <PdfText>{valuation.waterSecurityReviewComments}</PdfText>
                </>
              )}
            </>
          ),
        }),
        {
          title: 'Average Efficient Production',
          content: (
            <ValuationPvsAep
              landAssets={valuation.landAssets.features}
              totalHectares={valuation.summary.totalEffectiveHectares}
            />
          ),
        },
        ...insertIf(!!valuation.improvements.length, {
          title: 'Building Improvements',
          content: (
            <BuildingImprovementsTable improvements={valuation.improvements} />
          ),
        }),
        ...insertIf(!!valuation.descriptions?.other, {
          title: 'Other Commentary',
          content: <PdfParagraph>{valuation.descriptions?.other}</PdfParagraph>,
        }),
      ],
    },
    {
      title: 'Comparative Sales Analysis',
      children: [
        {
          title: 'Comparable Sales',
          content: (
            <>
              <ComparableSalesTable
                sales={valuation.comparableSales.features.map(
                  access('properties')
                )}
                valuation={valuation}
                margins={false}
              />
              <PdfMap
                style={{
                  width: 974 / 2,
                  marginVertical: SPACE[4],
                }}
                geoJson={{
                  type: 'FeatureCollection',
                  features: [
                    valuation.address,
                    ...(valuation.titles
                      .features as FeatureFixup<ValuationsSavedTitleFeature>[]),
                    ...(valuation.comparableSales.features as FeatureFixup<
                      ValuationsPvsSummary['comparableSales']['features'][number]
                    >[]),
                  ],
                }}
                featureOptions={{
                  onEachFeature(f, layer) {
                    if (f.geometry.type === 'Point') {
                      layer.bindTooltip(
                        formatAddress(f.properties?.fullAddress as string),
                        {
                          permanent: true,
                          className: 'pdfTooltip',
                        }
                      )
                    }
                  },
                }}
              />
            </>
          ),
        },
        {
          title: 'Detailed Comparative Sales Analysis',
          content: (
            <DetailedComparativeSalesAnalysis
              comparableSales={valuation.comparableSales}
            />
          ),
          break: true,
        },
        {
          title: 'Comparative Sale Adjustments',
          content: (
            <ComparativeSaleAdjustments
              comparableSales={valuation.comparableSales}
            />
          ),
          break: true,
        },
      ],
    },
    {
      title: 'Appendix',
      children: [
        {
          title: 'Appendix A: Valuation Worksheet',
          content: (
            // TODO check $/unit
            // TODO include adjustments?
            // Canopy metrics?
            <>
              <BestUseAssetSummaryTable
                bestUses={valuation.summary.highestAndBestUseSummary}
              />
            </>
          ),
        },
        ...insertIf(valuation.hasKiwifruitBestUse, {
          title: 'Appendix B: Kiwifruit Worksheet',
          content: (
            <>
              <KiwifruitWorksheetSummary
                projectedValuationSummaries={
                  valuation.projectedValuationSummaries
                }
                horticultureLandAssetMetrics={
                  valuation.horticultureLandAssetMetrics
                }
              />
            </>
          ),
          break: true,
        }),
      ],
    },
  ]

  return pages
}

export default pvsPages
