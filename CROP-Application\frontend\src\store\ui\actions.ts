import { uiSlice } from './slice'

export const {
  addBookmark,
  removeBookmark,
  updateBookmark,
  setSelectedBookmarkId,
  setSalesAndListingCurrentTab,
  toggleSalesAndListingLayerVisibility,
  setLayerStyles,
  setSaleEditState,
  setSelectedSale,
  setSelectedComparableSale,
  setComparableSaleAdjustments,
  updateSaleAssetMetricAdjustment,
  toggleSaleInSaleEditState,
  setViewport,
  setBounds,
  toggleSelectedAddressId,
  setSelectedAddressIds,
  setWidgetState,
  setLayerState,
  setSelectedAddressColor,
  setStoredStraightLines,
  updateLineDraftState,
  setInfoState,
  setMapState,
  toggleSelectedSaleId,
  toggleSelectedListingId,
  setSelectedListingIds,
  setSelectedSaleColor,
  setSelectedTab,
  setSavedSettings,
  setFilterValue,
  setFilter,
  setLayoutValue,
  setRecentEntry,
  setUser,
  clearFilter,
  setSelectedSaleIds,
  setBaseLayer,
  setMapContainerRect,
  setExplorerFilters,
  setSelectedValocitySaleIds,
  setSelectedValocityListingIds,
  toggleSelectedValocitySaleId,
  toggleSelectedValocityListingId,
  openGreenDrawer,
  closeGreenDrawer,
  toggleGreenDrawer,
  setGreenTab,
  setTradingGroupTab,
  setGreenDrawerState,
  updateSaleFilterState,
  updateValocitySaleFilterState,
  clearValocitySaleFilterState,
  clearSaleFilterState,
} = uiSlice.actions
