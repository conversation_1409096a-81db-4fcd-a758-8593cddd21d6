import { get, set } from 'lodash'
import { truthy } from '@util/guards'
import { UIState } from './types'

export type SelectedIDType =
  | 'addressIds'
  | 'saleIds'
  | 'listingIds'
  | 'valocityListingIds'
  | 'valocitySaleIds'
export type SelectedID = number | string

const filterNullish = (values: SelectedID[]) =>
  values.filter(
    (value) =>
      truthy(value) && !['NaN', 'null', 'undefined'].includes(String(value))
  )

export function selectedItemsPathByType(type: SelectedIDType) {
  return `saved.map.selected.${type}`
}

export function getSelectedItemsByType(
  state: UIState,
  type: SelectedIDType
): Set<string> {
  const saved: SelectedID[] = get(state, selectedItemsPathByType(type), [])
  return new Set(filterNullish(saved).map((v) => String(v)))
}

export function setSelectedItemsByType(
  state: UIState,
  type: SelectedIDType,
  payload: SelectedID[]
): void {
  set(state, selectedItemsPathByType(type), filterNullish(payload))
}

export function isItemOfTypeSelected(
  state: UIState,
  type: SelectedIDType,
  id: string
) {
  const selected = getSelectedItemsByType(state, type)
  return selected.has(id)
}

export function toggleSelectedId(
  state: UIState,
  payload: string,
  type: SelectedIDType
) {
  const newId = String(payload)
  const selected = getSelectedItemsByType(state, type)

  selected.has(newId) ? selected.delete(newId) : selected.add(newId)

  setSelectedItemsByType(state, type, [...selected])
}

export function addSelectedId(
  state: UIState,
  payload: string,
  type: SelectedIDType
) {
  const newId = String(payload)
  const selected = getSelectedItemsByType(state, type)

  selected.add(newId)

  setSelectedItemsByType(state, type, [...selected])
}

export function addSelectedIds(
  state: UIState,
  payload: string[],
  type: SelectedIDType
) {
  const selected = getSelectedItemsByType(state, type)

  setSelectedItemsByType(state, type, [...new Set([...selected, ...payload])])
}
