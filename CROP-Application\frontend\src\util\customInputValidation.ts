import type { FormInstance } from 'antd'
import type { ConditionalRuleCondition } from '@store/services/sdk'
import _ from 'lodash'

export const shouldSkipValidation = (
  conditions: ConditionalRuleCondition[],
  form: FormInstance
) => {
  if (!Array.isArray(conditions)) {
    return false
  }

  return conditions.some((condition) => {
    const dependentValue = form.getFieldValue(condition.field)

    if (
      condition.inputType === 'multiSelect' &&
      condition.condition === 'equals'
    ) {
      if (Array.isArray(dependentValue) && Array.isArray(condition.values)) {
        return _.isEqual(
          [...dependentValue].sort(),
          [...condition.values].sort()
        )
      }
    } else if (
      condition.inputType === 'multiSelect' &&
      condition.condition === 'contains'
    ) {
      if (Array.isArray(dependentValue) && Array.isArray(condition.values)) {
        return dependentValue.every((item) => condition.values.includes(item))
      }
    }
    return false
  })
}
