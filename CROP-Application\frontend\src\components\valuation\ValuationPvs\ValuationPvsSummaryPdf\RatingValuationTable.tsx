import { pick, startCase } from 'lodash'
import { PdfTable } from '@components/pdf'
import type { DistrictValuationRoll } from '@store/services/sdk'

const TITLES = {
  landAreaHectaresWithUnit: 'Land Area',
  tlaName: 'TLA Name',
  lvDollars: 'Land Value',
  ivDollars: 'Improvement Value',
  cvDollars: 'Capital Value',
  valDateFormatted: 'Val Date',
}

const keyToTitle = (key: string) => {
  return TITLES[key as keyof typeof TITLES] || startCase(key)
}

const RatingValuationTable = ({
  ratingValuation,
}: {
  ratingValuation: DistrictValuationRoll
}) => {
  const values = pick(ratingValuation, [
    'tlaName',
    'valRef',
    'valDateFormatted',
    'landAreaHectaresWithUnit',
    'legalDesc',
    'landZoneDesc',
  ])

  const rows = Object.entries(values).map(([key, value]) => ({
    key: keyToTitle(key),
    value: value || '–',
  }))

  const breakdownValues = pick(ratingValuation, [
    'lvDollars',
    'ivDollars',
    'cvDollars',
  ])

  const breakdownRows = Object.entries(breakdownValues).map(([key, value]) => ({
    key: keyToTitle(key),
    value: value || '–',
  }))

  return (
    <>
      <PdfTable striped rows={rows} />
      <PdfTable striped rows={breakdownRows} />
    </>
  )
}

export default RatingValuationTable
