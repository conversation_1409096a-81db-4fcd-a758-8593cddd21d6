import { CheckOutlined, DollarOutlined, UserOutlined } from '@ant-design/icons'
import { skipToken } from '@reduxjs/toolkit/dist/query'
import { Button } from 'antd'
import type { NumberSize } from 're-resizable'
import React, { useCallback, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import { ButtonWidget, MapContainer, Widget } from '@components/generic'
import { Dashboard } from '@components/generic/Layout'
import { ResizablePane } from '@components/generic/Pane'
import { ViewportTitlesLayer } from '@components/map/layers/ViewportTitlesLayer'
import { TitleLayerProvider } from '@components/map/titleLayerContext'
import type { RootState } from '@store'
import { useGetValuationSummaryQuery } from '@store/services/valuations'
import { uiSelectors } from '@store/ui'
import { setLayoutValue } from '@store/ui/actions'
import { isValuer } from '@store/ui/selectors'
import { AgriMap } from '../AgriMap'
import { SelectedValuationControls } from './dashboard/SelectedValuationControls'
import { SelectedValuationDetails } from './dashboard/SelectedValuationDetails'
import { ValuationsSearchWidget } from './dashboard/ValuationsSearchWidget'
import { useValuationSearchListQuery } from '@store/services/sdk'

export interface ValuationsPageRouteParams {
  valuationId?: string
  addressId?: string
}

const selector = (state: RootState) => {
  return {
    userValuationsFilterState: uiSelectors.getFilterState(state, 'user'),
    valuationsFilterState: uiSelectors.getFilterState(state, 'valuations'),
    layoutState: uiSelectors.getLayoutState(state, 'valuationDashboard'),
    isValuer: isValuer(state),
  }
}

const ValuationsDashboardView = () => {
  const {
    userValuationsFilterState,
    valuationsFilterState,
    layoutState,
    isValuer,
  } = useSelector(selector)

  const dispatch = useDispatch()

  const layoutDispatch = useCallback(
    (payload: { type: string; value: string | number }) => {
      dispatch(
        setLayoutValue({
          pageName: 'valuationDashboard',
          layoutKey: payload?.type,
          layoutValue: payload?.value,
        })
      )
    },
    [dispatch]
  )

  const { valuationId = '' } = useParams()

  const { data: userValuations } = useValuationSearchListQuery({
    ...(userValuationsFilterState || {}),
    currentUser: true,
  })
  const { data: valuations } = useValuationSearchListQuery(
    valuationsFilterState || {}
  )
  const { data: summary } = useGetValuationSummaryQuery(
    valuationId ?? skipToken
  )

  const { address, titles, dvr, valuation } = useMemo(() => {
    if (summary) {
      return summary
    }
    return {
      address: undefined,
      titles: [],
      dvr: [],
      valuation: undefined,
    }
  }, [summary])

  const onPrimaryResize = useCallback(
    (_, _direction, _ref, delta: NumberSize) => {
      const width = (layoutState.leftPaneWidth as number) + delta.width
      layoutDispatch({ type: 'leftPaneWidth', value: width })
    },
    [layoutState.leftPaneWidth, layoutDispatch]
  )

  const onSecondaryResize = useCallback(
    (_, _direction, _ref, delta: NumberSize) => {
      const width = (layoutState.rightPaneWidth as number) + delta.width
      layoutDispatch({ type: 'rightPaneWidth', value: width })
    },
    [layoutState.rightPaneWidth, layoutDispatch]
  )

  const selectedView = isValuer ? layoutState.selectedView : 'SEARCH'

  const viewControls = useMemo(() => {
    if (!selectedView) {
      return <></>
    }

    return (
      <ButtonWidget>
        <Button
          disabled={!isValuer}
          onClick={() =>
            layoutDispatch({ type: 'selectedView', value: 'USER' })
          }
          type={selectedView === 'USER' ? 'primary' : 'ghost'}
          icon={<UserOutlined />}
        >
          My Valuations
        </Button>
        <Button
          onClick={() =>
            layoutDispatch({
              type: 'selectedView',
              value: 'SEARCH',
            })
          }
          type={selectedView === 'SEARCH' ? 'primary' : 'ghost'}
          icon={<CheckOutlined />}
        >
          Valuations Search
        </Button>
      </ButtonWidget>
    )
  }, [selectedView, layoutDispatch, isValuer])

  return (
    <Dashboard data-testid="valuations-page">
      <ResizablePane
        size={{
          width: layoutState.leftPaneWidth ?? 400,
          height: 'auto',
        }}
        type="left"
        onResizeStop={onPrimaryResize}
      >
        <Widget
          type="page-header"
          title="Valuations Dashboard"
          icon={<DollarOutlined />}
          extra={viewControls}
        >
          <ValuationsSearchWidget
            valuations={
              selectedView === 'USER'
                ? userValuations?.results ?? []
                : valuations?.results ?? []
            }
            creatorDisabled={selectedView === 'USER'}
            valuationsCount={
              selectedView === 'USER'
                ? userValuations?.count ?? 0
                : valuations?.count ?? 0
            }
            isLoading={false} // TODO: Rework this properly for next week. Having it the way it was caused it to be synchronously blocked
          />
        </Widget>
      </ResizablePane>
      <TitleLayerProvider>
        <MapContainer hidden={!valuation}>
          {address && valuation && (
            <AgriMap center={{ lat: address.lat, lng: address.lng }}>
              <ViewportTitlesLayer
                exportMode
                forcedTitleMode
                position={1}
                titles={titles}
              />
            </AgriMap>
          )}
        </MapContainer>
      </TitleLayerProvider>
      <ResizablePane
        type="right"
        size={{
          width: layoutState.rightPaneWidth ?? 400,
          height: 'auto',
        }}
        onResizeStop={onSecondaryResize}
        minimised={!valuation}
      >
        {valuation ? <SelectedValuationControls valuation={valuation} /> : null}
        {valuation ? (
          <SelectedValuationDetails
            valuation={valuation}
            valuationTitles={titles || []}
            valuationDvrs={dvr || []}
          />
        ) : null}
      </ResizablePane>
    </Dashboard>
  )
}

export default ValuationsDashboardView
