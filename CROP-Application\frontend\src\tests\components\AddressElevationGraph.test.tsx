import { elevationLabeler } from '../../util/elevationLabeler'

test('elevationCode 50 expected to have outputs 1175 & 1200', () => {
  expect(elevationLabeler(50, 'start', 25)).toBe(50)
  expect(elevationLabeler(50, 'end', 25)).toBe(75)
})

test('elevationCode 16 expected to have outputs 325 & 350', () => {
  expect(elevationLabeler(16, 'start', 25)).toBe(0)
  expect(elevationLabeler(16, 'end', 25)).toBe(25)
})

test('elevationCode 0 expected to have outputs -75 & 50', () => {
  expect(elevationLabeler(0, 'start', 25)).toBe(0)
  expect(elevationLabeler(0, 'end', 25)).toBe(25)
})

test('elevationCode "" expected to have outputs null & null', () => {
  // @ts-expect-error
  expect(elevationLabeler('', 'start', 25)).toBe(null)
  // @ts-expect-error
  expect(elevationLabeler('', 'end', 25)).toBe(null)
})
