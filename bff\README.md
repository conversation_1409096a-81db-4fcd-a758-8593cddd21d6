# BFF (Backend for Frontend) - Django Ninja Integration

This BFF service acts as a proxy layer between the frontend and the Django Ninja backend services, providing authentication, rate limiting, and request forwarding capabilities.

## Features

- **PingFederate Authentication**: OAuth 2.0 integration with PingFederate
- **Rate Limiting**: Configurable rate limiting for authenticated and anonymous users
- **Request Proxying**: Forwards all Django Ninja API requests through the BFF
- **Session Management**: Handles user sessions and token management
- **Error Handling**: Comprehensive error handling and logging

## Architecture

```
Frontend → BFF → Django Ninja Backend
```

The BFF intercepts all API requests from the frontend, handles authentication and rate limiting, then forwards the requests to the appropriate Django Ninja backend services.

## API Endpoints

### Authentication Endpoints
- `POST /api/auth/login` - Direct login (placeholder)
- `GET /api/auth/authorize` - Start OAuth 2.0 flow
- `GET /api/auth/callback` - OAuth callback handler
- `POST /api/auth/logout` - Logout and clear session

### Core API Proxy Endpoints
- `GET|POST|PUT|DELETE /api/health` - Health check
- `GET|POST|PUT|DELETE /api/addresses/*` - Address management
- `GET|POST|PUT|DELETE /api/sales/*` - Sales data
- `GET|POST|PUT|DELETE /api/valuations/*` - Property valuations
- `GET|POST|PUT|DELETE /api/customers/*` - Customer management

### RiskRadar Module Proxy Endpoints
- `GET /riskradar/health` - RiskRadar health check
- `GET|POST|PUT|DELETE /riskradar/locations/*` - Location management
- `GET|POST|PUT|DELETE /riskradar/perils/*` - Peril data
- `GET|POST|PUT|DELETE /riskradar/loss-models/*` - Loss model management
- `GET|POST|PUT|DELETE /riskradar/risk-groups/*` - Risk group management
- `GET|POST|PUT|DELETE /riskradar/exposures/*` - Exposure data
- `GET|POST|PUT|DELETE /riskradar/analytics/*` - Analytics endpoints

### Other Module Proxy Endpoints
- `GET /green/health` - Green module health check
- `GET|POST|PUT|DELETE /green/*` - Green module endpoints
- `GET /finance/health` - Finance module health check
- `GET|POST|PUT|DELETE /finance/*` - Finance module endpoints
- `GET /ccra/health` - CCRA module health check
- `GET|POST|PUT|DELETE /ccra/*` - CCRA module endpoints
- `GET /propertyflow/health` - PropertyFlow module health check
- `GET|POST|PUT|DELETE /propertyflow/*` - PropertyFlow module endpoints

### Generic Proxy Endpoint
- `GET|POST|PUT|DELETE /ninja/*` - Generic proxy for any Django Ninja endpoint

## Configuration

### Environment Variables

```bash
# Django settings
SECRET_KEY=your-secret-key
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1

# Backend service configuration
BACKEND_API_URL=http://localhost:8000/api
DJANGO_NINJA_BASE_URL=http://localhost:8000
DJANGO_NINJA_API_PATH=/api
DJANGO_NINJA_TIMEOUT=30

# PingFederate configuration
PINGFEDERATE_BASE_URL=https://your-pingfederate-url
PINGFEDERATE_CLIENT_ID=your-client-id
PINGFEDERATE_CLIENT_SECRET=your-client-secret
PINGFEDERATE_REDIRECT_URI=http://localhost:8080/api/auth/callback

# CORS settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate limiting
NINJA_RATE_LIMIT_USER=100/hour
NINJA_RATE_LIMIT_ANON=20/hour
```

### Django Settings

Key settings in `bff_project/settings.py`:

- `BFF_SESSION_COOKIE_NAME`: Session cookie name
- `BFF_SESSION_EXPIRY`: Session expiry time in seconds
- `NINJA_RATE_LIMIT_USER`: Rate limit for authenticated users
- `NINJA_RATE_LIMIT_ANON`: Rate limit for anonymous users

## Running the BFF

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set environment variables (create `.env` file)

3. Run migrations:
```bash
python manage.py migrate
```

4. Start the server:
```bash
python manage.py runserver 8080
```

The BFF will be available at `http://localhost:8080`

## Request Flow

1. **Frontend Request**: Frontend makes API request to BFF
2. **Authentication**: BFF validates PingFederate token
3. **Rate Limiting**: BFF checks rate limits
4. **Request Forwarding**: BFF forwards request to Django Ninja backend
5. **Response Processing**: BFF processes backend response
6. **Frontend Response**: BFF returns response to frontend

## Error Handling

The BFF handles various error scenarios:

- **401 Unauthorized**: Invalid or expired authentication token
- **429 Too Many Requests**: Rate limit exceeded
- **502 Bad Gateway**: Backend service unavailable
- **504 Gateway Timeout**: Backend service timeout
- **500 Internal Server Error**: General server errors

## Logging

The BFF logs all requests and errors for monitoring and debugging:

- Request/response logging
- Authentication events
- Rate limiting events
- Backend service errors
- Performance metrics

## Security Features

- **Token Validation**: All requests validated against PingFederate
- **Rate Limiting**: Prevents abuse and DoS attacks
- **CORS Protection**: Configurable CORS policies
- **Session Security**: Secure session cookie handling
- **Request Sanitization**: Input validation and sanitization

## Development

For development, you can run the BFF in debug mode:

```bash
DEBUG=True python manage.py runserver 8080
```

This enables detailed error messages and request logging.
