import { MinusOutlined, PlusOutlined } from '@ant-design/icons'
import { Collapse } from 'antd'
import React from 'react'
import { Title } from '@components/typography'
import ValuationCombinedResourceConsents from './ValuationCombinedResourceConsentRecords'
import ValuationResourceConsentRecordForm from './ValuationResourceConsentRecordForm'

const ValuationResourceConsentRecordsCard = () => {
  return (
    <div className="ValuationResourceConsentRecordsCard unstyle">
      <div style={{ marginBottom: 16 }}>
        <Title>Resource Consents</Title>
        <ValuationCombinedResourceConsents />
      </div>
      <Collapse
        expandIcon={({ isActive }) =>
          isActive ? <MinusOutlined /> : <PlusOutlined />
        }
        expandIconPosition="end"
      >
        <Collapse.Panel header="Add Record" key="add" forceRender>
          <ValuationResourceConsentRecordForm />
        </Collapse.Panel>
      </Collapse>
    </div>
  )
}

export default ValuationResourceConsentRecordsCard
